#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Gold Data Fetcher - Unified Data Collection
==========================================

This script provides a unified interface for fetching gold data from multiple sources:
1. Yahoo Finance (yfinance) - Primary source, works reliably
2. Alpha Vantage API - Alternative for real-time data
3. MT5 integration (Windows only)

Data is saved in standardized format to data/gold/ directory.
"""

import yfinance as yf
import pandas as pd
import requests
import json
from datetime import datetime, timedelta
import os
from pathlib import Path
import argparse
import time


class GoldDataFetcher:
    """Unified gold data fetcher supporting multiple sources"""
    
    def __init__(self, data_dir="gold"):
        self.data_dir = Path(data_dir)
        self.data_dir.mkdir(parents=True, exist_ok=True)
        
        # API Keys (replace with your own)
        self.alpha_vantage_key = "FGBY3H50WIGHJZSX"
        
    def fetch_yfinance_data(self, symbol="GC=F", period="5y", interval="1d", days_back=None):
        """
        Fetch data from Yahoo Finance with special handling for 1m data
        
        Args:
            symbol (str): Symbol to fetch (GC=F for gold futures, GLD for gold ETF)
            period (str): Period to fetch (1d, 5d, 1mo, 3mo, 6mo, 1y, 2y, 5y, 10y, ytd, max)
            interval (str): Data interval (1m, 2m, 5m, 15m, 30m, 60m, 90m, 1h, 1d, 5d, 1wk, 1mo, 3mo)
            days_back (int): Number of days back to fetch (overrides period for batch downloading)
        
        Returns:
            pd.DataFrame: OHLCV data
        """
        print(f"📊 Fetching {symbol} data from Yahoo Finance...")
        
        # Special handling for 1m data with batch downloading
        if interval == '1m' and days_back and days_back > 7:
            return self._fetch_minute_data_batched(symbol, days_back)
        
        print(f"   Period: {period}, Interval: {interval}")
        
        try:
            data = yf.download(
                symbol, 
                period=period, 
                interval=interval, 
                progress=False, 
                auto_adjust=True
            )
            
            if data.empty:
                print(f"   ❌ No data received for {symbol}")
                return None
                
            print(f"   ✅ Success: {len(data)} records from {data.index[0]} to {data.index[-1]}")
            return data
            
        except Exception as e:
            print(f"   ❌ Error: {e}")
            return None

    def _fetch_minute_data_batched(self, symbol="GC=F", days_back=30):
        """
        Fetch 1-minute data in batches to overcome yfinance 7-day limit
        
        Args:
            symbol (str): Symbol to fetch
            days_back (int): Number of days to fetch
            
        Returns:
            pd.DataFrame: Combined 1-minute data
        """
        print(f"   📦 Batch downloading {days_back} days of 1m data (yfinance 7-day limit workaround)")
        
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days_back)
        all_data = []
        
        # Use 6-day batches to ensure stability
        batch_delta = timedelta(days=6)
        current_start = start_date
        batch_num = 1
        
        while current_start < end_date:
            batch_end = min(current_start + batch_delta, end_date)
            print(f"   📥 Batch {batch_num}: {current_start.strftime('%Y-%m-%d')} -> {batch_end.strftime('%Y-%m-%d')}")
            
            try:
                batch_data = yf.download(
                    tickers=symbol, 
                    start=current_start, 
                    end=batch_end, 
                    interval='1m', 
                    progress=False, 
                    auto_adjust=True
                )
                
                if not batch_data.empty:
                    all_data.append(batch_data)
                    print(f"      ✅ Success: {len(batch_data)} records")
                else:
                    print(f"      🟡 No data returned for batch {batch_num}")
                    
            except Exception as e:
                print(f"      ❌ Batch {batch_num} failed: {e}")
                
            current_start += batch_delta
            batch_num += 1
            time.sleep(1)  # Polite delay between requests
        
        if not all_data:
            print(f"   ❌ No data retrieved from any batch")
            return None
        
        # Combine and clean data
        print(f"   🔄 Combining data from {len(all_data)} batches...")
        combined_data = pd.concat(all_data, ignore_index=False)
        
        # Remove duplicates and sort
        combined_data = combined_data[~combined_data.index.duplicated(keep='first')].sort_index()
        
        print(f"   ✅ Combined success: {len(combined_data)} records from {combined_data.index[0]} to {combined_data.index[-1]}")
        
        return combined_data
    
    def fetch_alpha_vantage_data(self, symbol="GLD", interval="1min", outputsize="compact"):
        """
        Fetch data from Alpha Vantage API
        
        Args:
            symbol (str): Symbol (GLD for gold ETF)
            interval (str): Interval (1min, 5min, 15min, 30min, 60min)
            outputsize (str): compact or full
        
        Returns:
            pd.DataFrame: OHLCV data
        """
        print(f"📊 Fetching {symbol} data from Alpha Vantage...")
        
        if not self.alpha_vantage_key:
            print("   ❌ Alpha Vantage API key not provided")
            return None
        
        url = f"https://www.alphavantage.co/query"
        params = {
            'function': 'TIME_SERIES_INTRADAY',
            'symbol': symbol,
            'interval': interval,
            'outputsize': outputsize,
            'apikey': self.alpha_vantage_key
        }
        
        try:
            response = requests.get(url, params=params)
            response.raise_for_status()
            data = response.json()
            
            if 'Error Message' in data:
                print(f"   ❌ API Error: {data['Error Message']}")
                return None
            
            if 'Note' in data:
                print(f"   ⚠️  API Note: {data['Note']}")
                return None
                
            time_series_key = f'Time Series ({interval})'
            if time_series_key not in data:
                print(f"   ❌ No time series data found")
                return None
            
            # Convert to DataFrame
            df = pd.DataFrame(data[time_series_key]).T
            df.index = pd.to_datetime(df.index)
            
            # Rename columns
            df.columns = ['Open', 'High', 'Low', 'Close', 'Volume']
            df = df.astype(float)
            
            print(f"   ✅ Success: {len(df)} records from {df.index[0]} to {df.index[-1]}")
            return df.sort_index()
            
        except Exception as e:
            print(f"   ❌ Error: {e}")
            return None
    
    def save_data(self, df, filename):
        """Save data to parquet file"""
        if df is None or df.empty:
            print(f"❌ No data to save for {filename}")
            return False
        
        filepath = self.data_dir / filename
        
        try:
            df.to_parquet(filepath, engine='pyarrow', compression='snappy')
            print(f"✅ Saved {filename}: {len(df)} records")
            print(f"   📁 Path: {filepath}")
            print(f"   📊 Columns: {list(df.columns)}")
            if len(df) > 0:
                print(f"   📅 Range: {df.index[0]} to {df.index[-1]}")
            return True
        except Exception as e:
            print(f"❌ Failed to save {filename}: {e}")
            return False
    
    def fetch_all_timeframes(self):
        """Fetch data for all standard timeframes"""
        
        print("=" * 60)
        print("Gold Data Fetcher - Fetching All Timeframes")
        print("=" * 60)
        
        # Define data sources and timeframes
        tasks = [
            # Daily data - 5 years
            {
                'source': 'yfinance',
                'symbol': 'GC=F',
                'period': '5y',
                'interval': '1d',
                'filename': '1d.parquet'
            },
            # Hourly data - 1 year
            {
                'source': 'yfinance',
                'symbol': 'GC=F', 
                'period': '1y',
                'interval': '1h',
                'filename': '1h.parquet'
            },
            # 15-minute data - 2 months
            {
                'source': 'yfinance',
                'symbol': 'GC=F',
                'period': '60d', 
                'interval': '15m',
                'filename': '15m.parquet'
            },
            # 5-minute data - 1 month
            {
                'source': 'yfinance',
                'symbol': 'GC=F',
                'period': '30d',
                'interval': '5m', 
                'filename': '5m.parquet'
            },
            # 1-minute data - 30 days (using batch download)
            {
                'source': 'yfinance',
                'symbol': 'GC=F',
                'days_back': 30,
                'interval': '1m',
                'filename': '1m.parquet'
            }
        ]
        
        success_count = 0
        
        for task in tasks:
            print(f"\n🔄 Processing {task['filename']}...")
            
            if task['source'] == 'yfinance':
                if 'days_back' in task:
                    # Use batch download for minute data
                    df = self.fetch_yfinance_data(
                        symbol=task['symbol'],
                        interval=task['interval'],
                        days_back=task['days_back']
                    )
                else:
                    # Use standard period-based download
                    df = self.fetch_yfinance_data(
                        symbol=task['symbol'],
                        period=task['period'],
                        interval=task['interval']
                    )
            elif task['source'] == 'alpha_vantage':
                df = self.fetch_alpha_vantage_data(
                    symbol=task['symbol'],
                    interval=task['interval']
                )
            
            if self.save_data(df, task['filename']):
                success_count += 1
            
            # Polite delay between requests
            time.sleep(1)
        
        print(f"\n" + "=" * 60)
        print(f"Completed! Successfully fetched {success_count}/{len(tasks)} datasets")
        print("=" * 60)
        
        # List final results
        print("\n📈 Final data files:")
        for file in ['1d.parquet', '1h.parquet', '15m.parquet', '5m.parquet', '1m.parquet']:
            filepath = self.data_dir / file
            if filepath.exists():
                try:
                    df = pd.read_parquet(filepath)
                    print(f"✅ {file}: {len(df)} records")
                except Exception as e:
                    print(f"❌ {file}: Error reading - {e}")
            else:
                print(f"⚪ {file}: Not found")


def main():
    """Main execution function"""
    parser = argparse.ArgumentParser(description='Fetch gold data from multiple sources')
    parser.add_argument('--data-dir', default='gold', help='Output directory for data files')
    parser.add_argument('--source', choices=['yfinance', 'alpha_vantage'], 
                       help='Specific source to use (default: fetch all)')
    parser.add_argument('--symbol', default='GC=F', help='Symbol to fetch')
    parser.add_argument('--interval', default='1d', help='Data interval')
    parser.add_argument('--period', default='5y', help='Data period (yfinance only)')
    
    args = parser.parse_args()
    
    fetcher = GoldDataFetcher(data_dir=args.data_dir)
    
    if args.source:
        # Fetch specific source
        if args.source == 'yfinance':
            # Use batch download for 1m data by default
            if args.interval == '1m':
                df = fetcher.fetch_yfinance_data(args.symbol, interval=args.interval, days_back=30)
            else:
                df = fetcher.fetch_yfinance_data(args.symbol, args.period, args.interval)
            filename = f"{args.interval}.parquet"
            fetcher.save_data(df, filename)
        elif args.source == 'alpha_vantage':
            df = fetcher.fetch_alpha_vantage_data(args.symbol, args.interval)
            filename = f"av_{args.interval}.parquet"
            fetcher.save_data(df, filename)
    else:
        # Fetch all timeframes
        fetcher.fetch_all_timeframes()


if __name__ == "__main__":
    main()