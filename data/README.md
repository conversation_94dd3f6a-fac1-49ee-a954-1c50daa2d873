# Gold Data Fetching Guide

数据获取脚本使用方法，用于获取黄金（XAUUSD）历史数据。

## 📁 可用脚本

### 1. `gold_data_fetcher.py` - 主要数据获取脚本 ⭐

**功能**：
- 支持多数据源：Yahoo Finance、Alpha Vantage API
- 智能1分钟数据批量下载（突破7天限制，获取30天数据）
- 获取全套时间周期：1d、1h、15m、5m、1m
- 统一数据格式保存到 `data/gold/` 目录

### 2. `mt5_xauusd_windows.py` - MT5专业数据获取

**功能**：
- 仅限Windows系统使用
- 直接从MetaTrader 5获取XAUUSD数据
- 数据质量最高，无时间限制
- 需要MT5安装且已登录

## 🚀 使用方法

### 方法一：一键获取全部数据（推荐）

```bash
# 获取所有时间周期的黄金数据
python data/gold_data_fetcher.py
```

**输出结果**：
```
gold/
├── 1d.parquet      # 日数据，5年历史 (~1,258条)
├── 1h.parquet      # 小时数据，1年历史 (~5,772条)
├── 15m.parquet     # 15分钟数据，2个月 (~4,394条)
├── 5m.parquet      # 5分钟数据，1个月 (~6,592条)
└── 1m.parquet      # 1分钟数据，30天 (~28,762条)
```

### 方法二：获取特定数据

```bash
# 只获取日数据
python data/gold_data_fetcher.py --source yfinance --interval 1d

# 只获取1分钟数据（自动使用30天批量下载）
python data/gold_data_fetcher.py --source yfinance --interval 1m

# 只获取5分钟数据
python data/gold_data_fetcher.py --source yfinance --interval 5m

# 使用Alpha Vantage API获取数据
python data/gold_data_fetcher.py --source alpha_vantage --interval 1min

# 指定保存目录
python data/gold_data_fetcher.py --data-dir custom/path
```

### 方法三：Windows MT5数据获取

**前提条件**：
- Windows系统
- 已安装MetaTrader 5
- MT5已登录到经纪商账户
- 安装依赖：`pip install MetaTrader5 pandas pyarrow`

```bash
# 在Windows系统上运行
python mt5_xauusd_windows.py
```

## 📊 数据特点

### Yahoo Finance (yfinance)
- ✅ 免费无限制，最稳定
- ✅ 支持所有时间周期
- ✅ macOS/Linux/Windows兼容
- 🚀 **增强版1分钟数据**：30天历史（原本只有7天）
- ⚠️ 数据有15分钟延迟

### Alpha Vantage API
- ✅ 实时数据
- ✅ 高质量数据源
- ⚠️ 免费版限制：25次请求/天
- ⚠️ 需要API密钥

### MetaTrader 5
- ✅ 专业级数据质量
- ✅ 无历史数据时间限制
- ✅ 实时tick级数据
- ❌ 仅限Windows系统
- ❌ 需要经纪商账户

## 🔧 配置说明

### API密钥设置

如需使用Alpha Vantage API，在 `gold_data_fetcher.py` 中修改：

```python
# 替换为你的API密钥
self.alpha_vantage_key = "你的API密钥"
```

### 批量下载参数

1分钟数据默认获取30天，可修改：

```python
# 修改获取天数（在fetch_all_timeframes方法中）
'days_back': 30,  # 改为你需要的天数
```

## ⚡ 快速示例

```bash
# 场景1：日常数据更新
python data/gold_data_fetcher.py

# 场景2：只需要最新1分钟数据做实时分析
python data/gold_data_fetcher.py --source yfinance --interval 1m

# 场景3：获取长期日数据做趋势分析
python data/gold_data_fetcher.py --source yfinance --interval 1d

# 场景4：Windows用户获取专业数据
python data/mt5_xauusd_windows.py
```

## 📝 数据格式

所有输出数据统一为标准OHLCV格式：
- **Open**: 开盘价
- **High**: 最高价  
- **Low**: 最低价
- **Close**: 收盘价
- **Volume**: 成交量
- **Index**: 时间索引（UTC时区）
- **格式**: Parquet文件，Snappy压缩

## 🔄 定时更新

建议设置定时任务每日更新数据：

```bash
# Linux/macOS crontab示例 (每天上午8点更新)
0 8 * * * cd /path/to/quant-hmm && python data/gold_data_fetcher.py

# Windows计划任务
# 运行：python data/gold_data_fetcher.py
# 频率：每日
```

## 🚨 重要特性

- **智能批量下载**：自动处理1分钟数据的API限制
- **数据去重**：自动移除重复记录
- **错误恢复**：单个批次失败不影响整体下载
- **进度显示**：实时显示下载进度和数据量
- **礼貌延迟**：请求间有1秒延迟，避免API限制