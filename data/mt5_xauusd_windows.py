#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MetaTrader 5 XAUUSD Data Fetcher for Windows
============================================

This script fetches XAUUSD (Gold) data from MetaTrader 5 on Windows systems.
Copy this file to a Windows machine with MT5 installed and configured.

Requirements:
- Windows OS
- MetaTrader 5 installed and logged in
- pip install MetaTrader5 pandas pyarrow

Usage:
    python mt5_xauusd_windows.py

Data Output:
- Saves to gold/ directory in parquet format
- Files: 1d.parquet, 1h.parquet, 1m.parquet
"""

import MetaTrader5 as mt5
import pandas as pd
from datetime import datetime, timedelta
import os
import sys
from pathlib import Path


def ensure_data_directory():
    """Create gold directory if it doesn't exist"""
    data_dir = Path("gold")
    data_dir.mkdir(parents=True, exist_ok=True)
    return data_dir


def init_mt5():
    """Initialize MT5 connection"""
    if not mt5.initialize():
        print("Failed to initialize MetaTrader5")
        print(f"Error: {mt5.last_error()}")
        return False
    
    print("MetaTrader5 initialized successfully")
    return True


def get_xauusd_data(symbol="XAUUSD", timeframe=mt5.TIMEFRAME_D1, count=1000):
    """
    Fetch XAUUSD data from MT5
    
    Args:
        symbol (str): Trading symbol (default: XAUUSD)
        timeframe: MT5 timeframe constant
        count (int): Number of bars to fetch
    
    Returns:
        pd.DataFrame: OHLCV data
    """
    print(f"Fetching {count} bars of {symbol} data...")
    
    # Get rates from MT5
    rates = mt5.copy_rates_from_pos(symbol, timeframe, 0, count)
    
    if rates is None:
        print(f"Failed to get data for {symbol}")
        print(f"Error: {mt5.last_error()}")
        return None
    
    # Convert to DataFrame
    df = pd.DataFrame(rates)
    
    # Convert time to datetime
    df['time'] = pd.to_datetime(df['time'], unit='s')
    df.set_index('time', inplace=True)
    
    # Rename columns to standard format
    df.rename(columns={
        'open': 'Open',
        'high': 'High',
        'low': 'Low',
        'close': 'Close',
        'tick_volume': 'Volume'
    }, inplace=True)
    
    # Keep only OHLCV columns
    df = df[['Open', 'High', 'Low', 'Close', 'Volume']]
    
    print(f"Successfully fetched {len(df)} bars")
    print(f"Date range: {df.index[0]} to {df.index[-1]}")
    
    return df


def save_data(df, filename, data_dir):
    """Save DataFrame to parquet file"""
    if df is None or df.empty:
        print(f"No data to save for {filename}")
        return
    
    filepath = data_dir / filename
    try:
        df.to_parquet(filepath, engine='pyarrow', compression='snappy')
        print(f"✅ Data saved to: {filepath}")
        print(f"   Records: {len(df)}")
        print(f"   Columns: {list(df.columns)}")
    except Exception as e:
        print(f"❌ Failed to save {filename}: {e}")


def main():
    """Main execution function"""
    print("=" * 60)
    print("MetaTrader 5 XAUUSD Data Fetcher")
    print("=" * 60)
    
    # Check if running on Windows
    if os.name != 'nt':
        print("⚠️  This script is designed for Windows systems with MT5 installed.")
        print("Current OS:", os.name)
        return
    
    # Initialize MT5
    if not init_mt5():
        return
    
    # Create output directory
    data_dir = ensure_data_directory()
    print(f"Data will be saved to: {data_dir.absolute()}")
    
    # Define timeframes and corresponding filenames
    timeframes = [
        (mt5.TIMEFRAME_D1, "1d.parquet", 2000),    # Daily data, 2000 days (~5.5 years)
        (mt5.TIMEFRAME_H1, "1h.parquet", 8760),   # Hourly data, 8760 hours (1 year)
        (mt5.TIMEFRAME_M1, "1m.parquet", 10080),  # 1-minute data, 10080 minutes (1 week)
    ]
    
    # Fetch data for each timeframe
    for timeframe, filename, count in timeframes:
        print(f"\n📊 Processing {filename}...")
        
        try:
            df = get_xauusd_data("XAUUSD", timeframe, count)
            save_data(df, filename, data_dir)
        except Exception as e:
            print(f"❌ Error processing {filename}: {e}")
            continue
    
    # Display account info
    account_info = mt5.account_info()
    if account_info:
        print(f"\n📈 Account Info:")
        print(f"   Server: {account_info.server}")
        print(f"   Balance: {account_info.balance}")
        print(f"   Currency: {account_info.currency}")
    
    # Shutdown MT5
    mt5.shutdown()
    print("\n✅ MetaTrader5 connection closed")
    print("=" * 60)
    print("All tasks completed!")
    print("=" * 60)


if __name__ == "__main__":
    main()