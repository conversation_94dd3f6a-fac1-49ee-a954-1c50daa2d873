#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
NVDA HMM V2 Optimized Strategy - 基于SPY策略v9经验的优化版本
==========================================================

第一轮优化重点：
1. 增强特征工程 (基于SPY策略v9成功经验)
2. 改进状态映射逻辑 (波动率+收益率双重标准)
3. 优化仓位配置 (过渡状态50%→75%)
4. 修复VIX数据问题 (改用日频数据)
5. 增强HMM稳定性 (调整模型参数)

基于发现的问题：
- 超额收益-9.78%，胜率41.23%
- VIX月频数据失败，HMM收敛不稳定
- 状态识别在不同年份不一致

作者：基于quant-hmm项目SPY策略v9经验优化
版本：V2 Optimized
"""

import pandas as pd
import numpy as np
import yfinance as yf
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.preprocessing import StandardScaler
from hmmlearn.hmm import GaussianHMM
import warnings
import datetime
from typing import Tuple, Dict, List
import os

warnings.filterwarnings('ignore')

# 中文字体配置
plt.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'SimHei', 'Heiti TC', 'PingFang SC', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class NVDAHMMOptimizedStrategy:
    """NVDA HMM V2优化策略 - 基于SPY策略v9经验"""
    
    def __init__(self, start_date="2020-01-01", end_date=None):
        self.start_date = start_date
        self.end_date = end_date or datetime.datetime.today().strftime('%Y-%m-%d')
        self.data = None
        self.tactical_model = None
        self.macro_model = None
        self.tactical_scaler = StandardScaler()
        self.macro_scaler = StandardScaler()
        
    def download_and_prepare_data(self) -> pd.DataFrame:
        """下载并准备NVDA及相关数据"""
        print("📊 下载NVDA及相关市场数据...")
        
        # 扩展符号列表，基于SPY策略经验
        symbols = ["NVDA", "^VIX", "^TNX", "QQQ", "SOXX", "XLK", "^NDX", "RSP", "HYG", "IEF"]
        
        try:
            data_raw = yf.download(symbols, start=self.start_date, end=self.end_date, progress=False)
            data = data_raw['Close'].copy()
            
            # 重命名列
            data.rename(columns={
                'NVDA': 'price',
                '^VIX': 'vix', 
                '^TNX': 'tnx',
                '^NDX': 'ndx',
                'QQQ': 'qqq',
                'SOXX': 'soxx',  # 半导体ETF
                'XLK': 'xlk',     # 科技股ETF
                'RSP': 'rsp',     # 等权重SPY (市场广度)
                'HYG': 'hyg',     # 高收益债
                'IEF': 'ief'      # 国债ETF
            }, inplace=True)
            
            # 处理缺失数据
            data = data.ffill().dropna()
            
            print(f"✅ 数据下载完成: {len(data)} 个交易日")
            return data
            
        except Exception as e:
            print(f"❌ 数据下载失败: {e}")
            raise
    
    def engineer_enhanced_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """增强特征工程 - 基于SPY策略v9成功经验"""
        print("🔧 构建增强特征 (基于SPY策略v9经验)...")
        
        df = data.copy()
        
        # 1. 基础价格特征
        df['log_return'] = np.log(df['price'] / df['price'].shift(1))
        df['volatility'] = df['log_return'].rolling(window=21).std() * np.sqrt(252)
        
        # 2. 多时间框架移动平均 (SPY策略v9关键成功因素)
        ma_windows = [20, 35, 55, 200, 233]  # 加入SPY策略的关键55和233日
        for window in ma_windows:
            df[f'ma_{window}'] = df['price'].rolling(window=window).mean()
            df[f'dist_from_ma{window}'] = df['price'] / df[f'ma_{window}'] - 1
        
        # 3. 动量特征优化
        df['momentum_5d'] = df['price'].pct_change(5)
        df['momentum_20d'] = df['price'].pct_change(20)
        df['momentum_ratio'] = df['momentum_5d'] / (df['momentum_20d'].abs() + 1e-8)
        
        # 4. 科技股相对强度 (NVDA特色)
        df['nvda_qqq_ratio'] = df['price'] / df['qqq']
        df['nvda_ndx_ratio'] = df['price'] / df['ndx']  # 新增纳斯达克比率
        df['nvda_soxx_ratio'] = df['price'] / df['soxx']
        
        # 5. 市场环境指标 (基于SPY策略经验)
        df['tnx_change'] = df['tnx'].diff()
        df['vix_ma'] = df['vix'].rolling(20).mean()
        df['vix_signal'] = df['vix'] / df['vix_ma'] - 1
        
        # 6. 市场广度指标 (来自SPY策略的成功经验)
        df['market_breadth'] = df['rsp'] / df['qqq']  # 等权重vs市值加权
        df['credit_risk_ratio'] = df['hyg'] / df['ief']  # 信用风险偏好
        
        # 7. 技术指标
        df['rsi'] = self._calculate_rsi(df['price'])
        df['bb_position'] = self._calculate_bollinger_position(df['price'])
        
        print(f"✅ 增强特征工程完成，生成 {len([c for c in df.columns if c not in data.columns])} 个特征")
        return df
    
    def _calculate_rsi(self, prices: pd.Series, period: int = 14) -> pd.Series:
        """计算RSI指标"""
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        return rsi.fillna(50)
    
    def _calculate_bollinger_position(self, prices: pd.Series, period: int = 20, std_dev: int = 2) -> pd.Series:
        """计算布林带位置"""
        sma = prices.rolling(period).mean()
        std = prices.rolling(period).std()
        upper_band = sma + (std * std_dev)
        lower_band = sma - (std * std_dev)
        bb_position = (prices - lower_band) / (upper_band - lower_band)
        return bb_position.fillna(0.5)
    
    def build_optimized_macro_regime(self, start_date: str, end_date: str) -> pd.DataFrame:
        """构建优化的宏观环境模型 - 修复VIX数据问题"""
        print("🌍 构建优化宏观环境模型...")
        
        try:
            # 使用日频数据替代月频，避免VIX月频数据问题
            macro_symbols = ["QQQ", "TLT", "GLD"]
            macro_data_raw = yf.download(macro_symbols, start=start_date, end=end_date, progress=False)
            macro_data = macro_data_raw['Close'].copy()
            macro_data.columns = [s.lower() for s in macro_data.columns]
            
            # 重采样为周频，平衡信号质量和稳定性
            macro_data_weekly = macro_data.resample('W').last().dropna()
            
            # 优化的宏观特征工程
            macro_data_weekly['tech_bond_ratio'] = macro_data_weekly['qqq'] / macro_data_weekly['tlt']
            macro_data_weekly['gold_tech_ratio'] = macro_data_weekly['gld'] / macro_data_weekly['qqq']
            
            # 使用更稳定的特征变化率
            macro_data_weekly['risk_appetite'] = macro_data_weekly['tech_bond_ratio'].pct_change(2)
            macro_data_weekly['safe_haven_demand'] = macro_data_weekly['gold_tech_ratio'].pct_change(2)
            
            macro_features = macro_data_weekly[['risk_appetite', 'safe_haven_demand']].dropna()
            
            # 训练2状态宏观HMM (增强稳定性参数)
            X_macro = self.macro_scaler.fit_transform(macro_features)
            self.macro_model = GaussianHMM(
                n_components=2, 
                covariance_type="diag",  # 简化协方差矩阵提升稳定性
                n_iter=500,              # 减少迭代次数避免过拟合
                random_state=42,
                tol=1e-3                 # 放松收敛标准
            )
            self.macro_model.fit(X_macro)
            
            # 优化的状态识别和映射
            macro_states = self.macro_model.predict(X_macro)
            state_means = self.macro_scaler.inverse_transform(self.macro_model.means_)
            risk_on_state = np.argmax(state_means[:, 0])  # 风险偏好高的状态
            
            regime_map = {state: ('Risk-On' if state == risk_on_state else 'Risk-Off') 
                         for state in range(2)}
            
            # 转换为日频数据
            macro_regime_weekly = pd.DataFrame({
                'regime': pd.Series(macro_states, index=macro_features.index).map(regime_map)
            })
            
            # 重采样到日频并前向填充
            macro_regime_daily = macro_regime_weekly.resample('D').ffill().dropna()
            
            print(f"✅ 优化宏观模型完成，状态映射: {regime_map}")
            return macro_regime_daily[['regime']]
            
        except Exception as e:
            print(f"⚠️  宏观模型构建失败: {e}")
            # 创建默认的Risk-On状态
            date_range = pd.date_range(start=start_date, end=end_date, freq='D')
            default_regime = pd.DataFrame({'regime': 'Risk-On'}, index=date_range)
            return default_regime

    def train_optimized_tactical_hmm(self, feature_data: pd.DataFrame) -> Tuple[GaussianHMM, Dict]:
        """训练优化的战术层HMM模型"""
        print("🧠 训练优化战术层HMM模型 (基于SPY策略v9经验)...")

        # 精选特征 (基于SPY策略v9成功特征)
        tactical_features = [
            'log_return',
            'volatility', 
            'dist_from_ma20',
            'dist_from_ma55',      # SPY策略v9关键
            'dist_from_ma200',
            'dist_from_ma233',     # SPY策略v9关键
            'momentum_ratio',
            'nvda_qqq_ratio',
            'nvda_ndx_ratio',      # 新增科技股指标
            'tnx_change',          # SPY策略关键宏观指标
            'market_breadth',      # SPY策略关键市场指标
            'vix_signal'
        ]

        # 准备训练数据
        available_features = [f for f in tactical_features if f in feature_data.columns]
        if len(available_features) < len(tactical_features):
            missing = set(tactical_features) - set(available_features)
            print(f"⚠️  缺少特征: {missing}")
            
        train_data = feature_data[available_features].dropna()
        X_train = self.tactical_scaler.fit_transform(train_data)

        # 优化的3状态HMM参数 (提升稳定性)
        self.tactical_model = GaussianHMM(
            n_components=3,
            covariance_type="diag",  # 简化协方差矩阵
            n_iter=500,              # 适度的迭代次数
            random_state=42,
            tol=1e-3,                # 放松收敛标准
            init_params='st',        # 只初始化状态转移和起始概率
            params='stmc'            # 训练所有参数
        )

        # 添加训练稳定性检查
        try:
            self.tactical_model.fit(X_train)
        except Exception as e:
            print(f"⚠️  HMM训练遇到问题: {e}")
            # 使用更简单的配置重试
            self.tactical_model = GaussianHMM(n_components=3, covariance_type="spherical", 
                                            n_iter=100, random_state=42)
            self.tactical_model.fit(X_train)

        # 基于SPY策略v9经验的状态分析和映射
        states = self.tactical_model.predict(X_train)
        state_analysis = {}

        for i in range(3):
            state_mask = (states == i)
            state_returns = train_data.loc[state_mask, 'log_return']
            state_vol = train_data.loc[state_mask, 'volatility'] if 'volatility' in train_data.columns else pd.Series([0.3])

            state_analysis[i] = {
                'mean_return': state_returns.mean(),
                'mean_volatility': state_vol.mean(),
                'count': state_mask.sum(),
                'percentage': state_mask.sum() / len(states) * 100
            }

        # 基于SPY策略的双重标准状态映射
        # 1. 先按波动率确定牛熊
        vol_sorted = sorted(state_analysis.items(), key=lambda x: x[1]['mean_volatility'])
        tentative_bull = vol_sorted[0][0]    # 最低波动率
        tentative_bear = vol_sorted[-1][0]   # 最高波动率
        
        # 2. 验证收益率是否匹配
        if state_analysis[tentative_bull]['mean_return'] < state_analysis[tentative_bear]['mean_return']:
            # 如果低波动率状态收益率也低，说明映射有问题，改用收益率排序
            return_sorted = sorted(state_analysis.items(), key=lambda x: x[1]['mean_return'])
            state_map = {
                return_sorted[0][0]: "Bear",      
                return_sorted[1][0]: "Neutral",   
                return_sorted[2][0]: "Bull"       
            }
        else:
            # 正常的波动率映射
            remaining_states = [s for s in range(3) if s not in [tentative_bull, tentative_bear]]
            state_map = {
                tentative_bull: "Bull",
                tentative_bear: "Bear", 
                remaining_states[0]: "Neutral"
            }

        print("优化战术状态分析:")
        for original_state, mapped_state in state_map.items():
            analysis = state_analysis[original_state]
            print(f"  {mapped_state}状态: 收益{analysis['mean_return']:.6f}, "
                  f"波动{analysis['mean_volatility']:.4f}, 占比{analysis['percentage']:.1f}%")

        return self.tactical_model, state_map

    def calculate_optimized_position(self, tactical_state: str, macro_regime: str,
                                   features: Dict) -> float:
        """优化的自适应仓位计算"""

        # 基于SPY策略v9的关键优化：过渡状态75%仓位
        base_positions = {
            'Bull': 1.0, 
            'Neutral': 0.75,  # 关键优化：50% → 75%
            'Bear': 0.0
        }
        base_position = base_positions.get(tactical_state, 0.0)

        # 科技股特色调整
        nvda_qqq_ratio = features.get('nvda_qqq_ratio', 1.0)
        nvda_ndx_ratio = features.get('nvda_ndx_ratio', 1.0)
        
        # 综合科技股强度
        tech_strength = (nvda_qqq_ratio + nvda_ndx_ratio) / 2
        if tech_strength > 1.08:  # 提高阈值，减少噪音交易
            tech_adjustment = min(0.15, (tech_strength - 1.0) * 1.5)  # 降低调整幅度
        else:
            tech_adjustment = 0.0

        # 基于SPY策略v9的趋势热度调节
        dist_ma200 = features.get('dist_from_ma200', 0.0)
        dist_ma233 = features.get('dist_from_ma233', 0.0)
        avg_dist = (dist_ma200 + dist_ma233) / 2
        
        if tactical_state == 'Bull' and macro_regime == 'Risk-On':
            if 0.03 <= avg_dist <= 0.12:  # 健康的趋势区间
                trend_adjustment = min(0.1, avg_dist * 0.8)  # 适度的趋势奖励
            else:
                trend_adjustment = 0.0
        else:
            trend_adjustment = 0.0

        # VIX恐慌调节
        vix_signal = features.get('vix_signal', 0.0)
        if vix_signal > 0.8:  # 提高VIX阈值，减少过度反应
            vix_penalty = min(base_position * 0.6, vix_signal * 0.3)  # 适度的VIX惩罚
        else:
            vix_penalty = 0.0

        # 最终仓位计算
        final_position = base_position + tech_adjustment + trend_adjustment - vix_penalty
        final_position = max(0.0, min(1.1, final_position))  # 限制最大110%

        return final_position

    def run_optimized_backtest(self, transaction_cost: float = 0.001) -> Dict:
        """运行优化回测"""
        print("🚀 开始NVDA优化HMM策略回测...")

        # 1. 数据准备
        raw_data = self.download_and_prepare_data()
        feature_data = self.engineer_enhanced_features(raw_data)
        macro_regime = self.build_optimized_macro_regime(self.start_date, self.end_date)

        # 2. Walk-Forward回测
        backtest_start = pd.to_datetime("2021-01-01")
        backtest_end = pd.to_datetime(self.end_date)

        all_signals = []

        for year in range(backtest_start.year, backtest_end.year + 1):
            train_end = f"{year-1}-12-31"
            predict_start = f"{year}-01-01"
            predict_end = f"{year}-12-31"

            print(f"\n训练年度 {year} (数据至 {train_end})...")

            # 训练数据
            train_df = feature_data.loc[:train_end]
            predict_df = feature_data.loc[predict_start:predict_end]

            if len(train_df) < 252 or len(predict_df) == 0:
                continue

            # 训练优化战术模型
            tactical_model, state_map = self.train_optimized_tactical_hmm(train_df)

            # 预测状态
            tactical_features = [
                'log_return', 'volatility', 'dist_from_ma20', 'dist_from_ma55',
                'dist_from_ma200', 'dist_from_ma233', 'momentum_ratio', 
                'nvda_qqq_ratio', 'nvda_ndx_ratio', 'tnx_change', 
                'market_breadth', 'vix_signal'
            ]
            
            # 只使用可用特征
            available_features = [f for f in tactical_features if f in predict_df.columns]
            
            X_predict = self.tactical_scaler.transform(predict_df[available_features].fillna(method='ffill').fillna(0))
            predicted_states = tactical_model.predict(X_predict)

            # 生成信号
            signals_df = pd.DataFrame(index=predict_df.index)
            signals_df['tactical_state'] = pd.Series(predicted_states, index=predict_df.index).map(state_map)
            all_signals.append(signals_df)

        if not all_signals:
            raise ValueError("无法生成交易信号")

        # 3. 合并信号和计算收益
        backtest_results = pd.concat(all_signals)
        backtest_results = feature_data.join(backtest_results, how='right')
        backtest_results = pd.merge_asof(backtest_results.sort_index(), macro_regime.sort_index(),
                                       left_index=True, right_index=True, direction='backward')
        backtest_results.dropna(inplace=True)

        # 4. 计算优化仓位和收益
        positions = []
        for idx, row in backtest_results.iterrows():
            features = {
                'nvda_qqq_ratio': row.get('nvda_qqq_ratio', 1.0),
                'nvda_ndx_ratio': row.get('nvda_ndx_ratio', 1.0),
                'dist_from_ma200': row.get('dist_from_ma200', 0.0),
                'dist_from_ma233': row.get('dist_from_ma233', 0.0),
                'vix_signal': row.get('vix_signal', 0.0)
            }

            position = self.calculate_optimized_position(
                row['tactical_state'], row['regime'], features
            )
            positions.append(position)

        backtest_results['position'] = positions
        backtest_results['trade'] = backtest_results['position'].diff().fillna(0).abs()
        backtest_results['strategy_return'] = (backtest_results['position'].shift(1).fillna(0) *
                                             backtest_results['log_return'])
        backtest_results['strategy_return'] -= backtest_results['trade'] * transaction_cost

        # 5. 计算性能指标
        return self._calculate_performance_metrics(backtest_results)

    def _calculate_performance_metrics(self, results: pd.DataFrame) -> Dict:
        """计算性能指标"""

        # 累计收益
        results['strategy_cumulative'] = np.exp(results['strategy_return'].cumsum())
        results['benchmark_cumulative'] = np.exp(results['log_return'].cumsum())

        # 年化指标
        days_in_year = 252
        strategy_annual_return = np.exp(results['strategy_return'].mean() * days_in_year) - 1
        strategy_annual_vol = results['strategy_return'].std() * np.sqrt(days_in_year)
        strategy_sharpe = strategy_annual_return / strategy_annual_vol if strategy_annual_vol != 0 else 0

        benchmark_annual_return = np.exp(results['log_return'].mean() * days_in_year) - 1
        benchmark_annual_vol = results['log_return'].std() * np.sqrt(days_in_year)
        benchmark_sharpe = benchmark_annual_return / benchmark_annual_vol if benchmark_annual_vol != 0 else 0

        # 最大回撤
        strategy_running_max = results['strategy_cumulative'].cummax()
        strategy_drawdown = (results['strategy_cumulative'] - strategy_running_max) / strategy_running_max
        strategy_max_drawdown = strategy_drawdown.min()

        benchmark_running_max = results['benchmark_cumulative'].cummax()
        benchmark_drawdown = (results['benchmark_cumulative'] - benchmark_running_max) / benchmark_running_max
        benchmark_max_drawdown = benchmark_drawdown.min()

        # 其他指标
        win_rate = (results['strategy_return'] > 0).mean()
        total_trades = (results['trade'] > 0).sum()

        # Sortino比率 (下行风险调整收益)
        downside_returns = results['strategy_return'][results['strategy_return'] < 0]
        downside_std = downside_returns.std() * np.sqrt(days_in_year) if len(downside_returns) > 0 else 0.01
        sortino_ratio = strategy_annual_return / downside_std

        performance_metrics = {
            'strategy_annual_return': strategy_annual_return,
            'benchmark_annual_return': benchmark_annual_return,
            'strategy_sharpe': strategy_sharpe,
            'benchmark_sharpe': benchmark_sharpe,
            'strategy_max_drawdown': strategy_max_drawdown,
            'benchmark_max_drawdown': benchmark_max_drawdown,
            'strategy_sortino': sortino_ratio,
            'win_rate': win_rate,
            'total_trades': total_trades,
            'excess_return': strategy_annual_return - benchmark_annual_return,
            'results_data': results
        }

        return performance_metrics

    def display_optimized_results(self, metrics: Dict):
        """显示优化回测结果"""
        print("\n" + "="*90)
        print("🏆 NVDA优化HMM策略回测结果 (V2 Optimized)")
        print("="*90)
        print(f"回测期间: {metrics['results_data'].index[0].date()} 至 {metrics['results_data'].index[-1].date()}")
        print(f"总交易日: {len(metrics['results_data'])} 天")
        print("-"*90)
        print(f"{'指标':<25} | {'NVDA V2策略':<15} | {'买入持有':<15} | {'超额收益':<15} | {'改进情况':<15}")
        print("-"*90)
        print(f"{'年化收益率':<25} | {metrics['strategy_annual_return']:14.2%} | {metrics['benchmark_annual_return']:14.2%} | {metrics['excess_return']:14.2%} | {'目标+9.78%':<15}")
        print(f"{'夏普比率':<25} | {metrics['strategy_sharpe']:14.2f} | {metrics['benchmark_sharpe']:14.2f} | {metrics['strategy_sharpe'] - metrics['benchmark_sharpe']:14.2f} | {'维持高水平':<15}")
        print(f"{'Sortino比率':<25} | {metrics['strategy_sortino']:14.2f} | {'N/A':<15} | {'N/A':<15} | {'下行风险优化':<15}")
        print(f"{'最大回撤':<25} | {metrics['strategy_max_drawdown']:14.2%} | {metrics['benchmark_max_drawdown']:14.2%} | {metrics['strategy_max_drawdown'] - metrics['benchmark_max_drawdown']:14.2%} | {'风险控制':<15}")
        print(f"{'胜率':<25} | {metrics['win_rate']:14.2%} | {'N/A':<15} | {'N/A':<15} | {'目标>45%':<15}")
        print(f"{'总交易次数':<25} | {metrics['total_trades']:14.0f} | {'0':<15} | {'N/A':<15} | {'交易效率':<15}")
        print("="*90)

        # V2优化特色总结
        print(f"\n🚀 NVDA V2优化特色 (基于SPY策略v9经验):")
        print(f"   ✅ 增强特征工程: 12个精选特征 (含SPY关键55/233日MA)")
        print(f"   ✅ 智能状态映射: 波动率+收益率双重标准")
        print(f"   ✅ 优化仓位配置: 过渡状态50%→75%")
        print(f"   ✅ 修复数据问题: 日频替代月频VIX")
        print(f"   ✅ 增强模型稳定性: 优化HMM参数设置")
        print(f"   ✅ 科技股专属优化: NVDA/NDX比率 + 市场广度")

    def save_results(self, metrics: Dict):
        """保存优化结果"""
        # 确保目录存在
        os.makedirs('log', exist_ok=True)
        
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 保存详细结果
        results_file = f"log/nvda_hmm_v2_optimized_results_{timestamp}.csv"
        metrics['results_data'].to_csv(results_file)
        
        # 保存性能摘要
        summary_file = f"log/nvda_hmm_v2_performance_summary_{timestamp}.txt"
        with open(summary_file, 'w', encoding='utf-8') as f:
            f.write("NVDA HMM V2 优化策略性能摘要\n")
            f.write("="*50 + "\n")
            f.write(f"年化收益率: {metrics['strategy_annual_return']:.2%}\n")
            f.write(f"基准收益率: {metrics['benchmark_annual_return']:.2%}\n")
            f.write(f"超额收益: {metrics['excess_return']:.2%}\n")
            f.write(f"夏普比率: {metrics['strategy_sharpe']:.2f}\n")
            f.write(f"Sortino比率: {metrics['strategy_sortino']:.2f}\n")
            f.write(f"最大回撤: {metrics['strategy_max_drawdown']:.2%}\n")
            f.write(f"胜率: {metrics['win_rate']:.1%}\n")
            f.write(f"总交易次数: {metrics['total_trades']}\n")
        
        print(f"\n✅ 结果已保存:")
        print(f"   详细数据: {results_file}")
        print(f"   性能摘要: {summary_file}")


def main():
    """主函数 - 运行NVDA优化HMM策略"""
    print("🚀 NVDA HMM V2优化策略 - 基于SPY策略v9经验优化")
    print("="*70)

    try:
        # 创建优化策略实例
        strategy = NVDAHMMOptimizedStrategy(start_date="2020-01-01")

        # 运行回测
        metrics = strategy.run_optimized_backtest()

        # 显示结果
        strategy.display_optimized_results(metrics)
        
        # 保存结果
        strategy.save_results(metrics)

        print(f"\n✅ NVDA V2优化策略回测完成!")
        print(f"🎯 核心成果: 年化收益{metrics['strategy_annual_return']:.1%}, "
              f"夏普比率{metrics['strategy_sharpe']:.2f}, "
              f"最大回撤{metrics['strategy_max_drawdown']:.1%}")
        
        # 与V1对比
        v1_annual_return = 0.6641  # V1的66.41%
        v1_excess_return = -0.0978  # V1的-9.78%超额收益
        
        improvement_return = metrics['strategy_annual_return'] - v1_annual_return
        improvement_excess = metrics['excess_return'] - v1_excess_return
        
        print(f"\n📈 相比V1改进情况:")
        print(f"   年化收益率改进: {improvement_return:+.1%}")
        print(f"   超额收益改进: {improvement_excess:+.1%}")

    except Exception as e:
        print(f"❌ 优化策略运行失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()