#!/usr/bin/env python3
"""
NVDA HMM V1 基础版本 (已使用滚动评估重构)
==========================================

这是NVDA HMM策略的第一个版本，包含最基础的HMM实现。
基于SPY策略的经验，但针对NVDA的高波动特性进行了初步调整。

核心特征:
- 基础HMM 3状态模型
- 简单的技术指标特征
- 基础仓位管理 (Bull: 1.0, Neutral: 0.5, Bear: 0.0)

*注意: 此文件已被重构，以使用与nvda_hmm_ultimate.py一致的滚动回测框架，
从而实现公平的性能比较。*

作者：量化交易研究团队
日期：2024年 (重构于2025年)
版本：V1.2 (滚动评估稳定版)
"""

import warnings
warnings.filterwarnings('ignore')

import pandas as pd
import numpy as np
import yfinance as yf
from datetime import datetime
import matplotlib.pyplot as plt
from typing import Dict, Tuple
from sklearn.preprocessing import StandardScaler
from hmmlearn.hmm import GaussianHMM

# 中文字体配置
plt.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'SimHei', 'Heiti TC', 'PingFang SC', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class NVDAHMMV1Strategy:
    """NVDA HMM V1策略 - 基础实现 (滚动评估版)"""

    def __init__(self, start_date="2020-01-01", end_date=None, transaction_cost=0.001):
        self.symbol = "NVDA"
        self.start_date = start_date
        self.end_date = end_date or datetime.now().strftime('%Y-%m-%d')
        self.transaction_cost = transaction_cost
        
        self.scaler = StandardScaler()
        self.model = None
        self.state_mapping = {}
        
        self.features = [
            'log_return', 'dist_ma20', 'dist_ma50', 'volatility',
            'momentum', 'rsi', 'volume_ratio', 'price_rank'
        ]

    def download_and_prepare_data(self) -> pd.DataFrame:
        """下载并准备NVDA数据及特征"""
        print(f"📊 下载 {self.symbol} 数据...")
        data = yf.download(self.symbol, start=self.start_date, end=self.end_date, progress=False)
        if data.empty:
            raise ValueError(f"无法下载{self.symbol}数据")

        # --- 关键修复：扁平化列索引 ---
        if isinstance(data.columns, pd.MultiIndex):
            # yfinance对于单个ticker也可能返回MultiIndex, e.g., ('Close', 'NVDA')
            # 我们通过droplevel(1)移除ticker层，保留特征层
            data.columns = data.columns.droplevel(1)
        # --- 修复结束 ---
        
        print(f"✅ 数据下载完成: {len(data)} 个交易日")
        
        print("🔧 V1基础特征工程...")
        result = data.copy()
        result['price'] = result['Close']
        result['log_return'] = np.log(result['price'] / result['price'].shift(1))
        
        for window in [10, 20, 50]:
            result[f'ma_{window}'] = result['price'].rolling(window).mean()
        
        result['dist_ma20'] = (result['price'] - result['ma_20']) / result['ma_20']
        result['dist_ma50'] = (result['price'] - result['ma_50']) / result['ma_50']
        result['volatility'] = result['log_return'].rolling(20).std()
        result['momentum'] = result['price'] / result['price'].shift(20) - 1
        
        delta = result['price'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(14).mean()
        rs = gain / loss
        result['rsi'] = 100 - (100 / (1 + rs))
        
        volume_series = result['Volume'].squeeze()
        result['volume_ma'] = volume_series.rolling(20).mean()
        result['volume_ratio'] = volume_series / result['volume_ma']

        result['price_rank'] = result['price'].rolling(60).rank(pct=True)
        
        print("✅ V1基础特征工程完成")
        return result

    def train_hmm(self, features_data: pd.DataFrame) -> Dict:
        """训练HMM模型 (适配滚动框架)"""
        clean_data = features_data[self.features].dropna()
        if len(clean_data) < 100:
            raise ValueError(f"训练数据不足: {len(clean_data)}")
            
        X_scaled = self.scaler.fit_transform(clean_data)
        
        self.model = GaussianHMM(
            n_components=3, covariance_type="full", n_iter=100,
            random_state=42, tol=1e-3
        )
        self.model.fit(X_scaled)
        
        states = self.model.predict(X_scaled)
        
        state_analysis = {}
        for i in range(3):
            mask = (states == i)
            if mask.sum() > 0:
                state_analysis[i] = {
                    'mean_return': clean_data.loc[mask, 'log_return'].mean(),
                    'volatility': clean_data.loc[mask, 'volatility'].mean()
                }
        
        sorted_states = sorted(state_analysis.items(), key=lambda x: x[1]['mean_return'])
        self.state_mapping = {
            sorted_states[0][0]: "Bear",
            sorted_states[1][0]: "Neutral",
            sorted_states[2][0]: "Bull"
        }
        return self.state_mapping

    def run_backtest(self) -> Dict:
        """运行滚动回测"""
        print("🚀 开始NVDA HMM V1策略滚动回测...")
        
        feature_data = self.download_and_prepare_data()
        
        backtest_start = pd.to_datetime("2021-01-01")
        backtest_end = pd.to_datetime(self.end_date)
        
        all_signals = []
        
        for year in range(backtest_start.year, backtest_end.year + 1):
            train_end_date = f"{year-1}-12-31"
            predict_start_date = f"{year}-01-01"
            predict_end_date = f"{year}-12-31"
            
            print(f"\n训练年度 {year} (数据至 {train_end_date})...")
            
            train_df = feature_data.loc[:train_end_date]
            predict_df = feature_data.loc[predict_start_date:predict_end_date]
            
            if len(train_df) < 252 or len(predict_df) == 0:
                print(f"数据不足，跳过 {year} 年度")
                continue
            
            self.train_hmm(train_df)
            
            clean_predict_data = predict_df[self.features].dropna()
            if not clean_predict_data.empty:
                X_predict_scaled = self.scaler.transform(clean_predict_data)
                raw_states = self.model.predict(X_predict_scaled)
                
                signals_df = pd.DataFrame(index=clean_predict_data.index)
                signals_df['state'] = pd.Series(raw_states, index=clean_predict_data.index).map(self.state_mapping)
                all_signals.append(signals_df)

        if not all_signals:
            raise ValueError("无法生成任何交易信号")
            
        backtest_results = pd.concat(all_signals)
        backtest_results = feature_data.join(backtest_results, how='right')
        
        position_map = {'Bull': 1.0, 'Neutral': 0.5, 'Bear': 0.0}
        backtest_results['position'] = backtest_results['state'].map(position_map).fillna(0)
        
        backtest_results['trade'] = backtest_results['position'].diff().fillna(0).abs()
        backtest_results['strategy_return'] = (backtest_results['position'].shift(1).fillna(0) *
                                             backtest_results['log_return'])
        backtest_results['strategy_return'] -= backtest_results['trade'] * self.transaction_cost
        
        return self._calculate_performance_metrics(backtest_results)

    def _calculate_performance_metrics(self, results: pd.DataFrame) -> Dict:
        """计算性能指标"""
        if results.empty: return {}
        
        results['strategy_cumulative'] = np.exp(results['strategy_return'].cumsum())
        results['benchmark_cumulative'] = np.exp(results['log_return'].cumsum())
        
        days = 252
        annual_return = np.exp(results['strategy_return'].mean() * days) - 1
        annual_vol = results['strategy_return'].std() * np.sqrt(days)
        sharpe = annual_return / annual_vol if annual_vol != 0 else 0
        
        bench_annual_return = np.exp(results['log_return'].mean() * days) - 1
        
        running_max = results['strategy_cumulative'].cummax()
        drawdown = (results['strategy_cumulative'] - running_max) / running_max
        max_drawdown = drawdown.min()
        
        return {
            'strategy_annual_return': annual_return,
            'benchmark_annual_return': bench_annual_return,
            'strategy_sharpe': sharpe,
            'strategy_max_drawdown': max_drawdown,
            'win_rate': (results['strategy_return'] > 0).mean(),
            'total_trades': (results['trade'] > 0).sum(),
            'results_data': results
        }

    def display_results(self, metrics: Dict):
        """显示回测结果"""
        if not metrics: 
            print("无回测结果可显示。")
            return
            
        print("\n" + "="*80)
        print("📈 NVDA HMM V1 (滚动评估版) 回测结果")
        print("="*80)
        print(f"回测期间: {metrics['results_data'].index[0].date()} 至 {metrics['results_data'].index[-1].date()}")
        print(f"{ '指标':<25} | {'NVDA策略':<15} | {'买入持有':<15}")
        print("-"*80)
        print(f"{ '年化收益率':<25} | {metrics['strategy_annual_return']:14.2%} | {metrics['benchmark_annual_return']:14.2%}")
        print(f"{ '夏普比率':<25} | {metrics['strategy_sharpe']:14.2f} | {'N/A':<15}")
        print(f"{ '最大回撤':<25} | {metrics['strategy_max_drawdown']:14.2%} | {'N/A':<15}")
        print(f"{ '胜率':<25} | {metrics['win_rate']:14.2%} | {'N/A':<15}")
        print(f"{ '总交易次数':<25} | {metrics['total_trades']:14.0f} | {'N/A':<15}")
        print("="*80)

    def plot_results(self, metrics: Dict):
        """绘制策略结果"""
        if not metrics: return
        results = metrics['results_data']
        
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(16, 12), sharex=True)
        fig.suptitle('NVDA HMM V1 (滚动评估版) 表现', fontsize=16)
        
        ax1.plot(results.index, results['strategy_cumulative'], label='策略累计收益', color='r')
        ax1.plot(results.index, results['benchmark_cumulative'], label='基准累计收益', color='b', alpha=0.7)
        ax1.set_title('累计收益对比')
        ax1.legend()
        ax1.grid(True)
        
        ax2.plot(results.index, results['position'], label='仓位', color='g', drawstyle='steps-post')
        ax2.set_title('策略仓位变化')
        ax2.legend()
        ax2.grid(True)
        
        plt.tight_layout()
        plt.show()

def main():
    """主程序 - V1策略 (滚动评估版)"""
    print("🚀 NVDA HMM V1 基础策略 (滚动评估版)")
    print("=" * 60)
    
    try:
        strategy = NVDAHMMV1Strategy()
        metrics = strategy.run_backtest()
        strategy.display_results(metrics)
        strategy.plot_results(metrics)
        
        print("\n✅ V1策略滚动回测完成!")
        
    except Exception as e:
        print(f"❌ V1策略运行失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()