#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
NVDA HMM V8.3 无偏差版本 - 修复前视偏差问题
=========================================

修复V8.2中发现的严重前视偏差问题：
1. 移除基于全历史统计的状态映射
2. 采用固定的状态-仓位映射机制
3. 实施真正的实时特征计算
4. 严格遵循时间序列约束

Author: Fixed bias-free version
Date: 2025
Version: V8.3 (Bias-Free)
"""

import pandas as pd
import numpy as np
import yfinance as yf
import matplotlib.pyplot as plt
from sklearn.preprocessing import StandardScaler
from hmmlearn.hmm import GaussianHMM
import warnings
import datetime

warnings.filterwarnings('ignore')

def calculate_features_realtime(price_data, current_idx):
    """实时计算特征 - 只使用当前时点之前的数据"""
    
    # 获取到当前时点的价格数据
    data = price_data.iloc[:current_idx + 1].copy()
    
    if len(data) < 200:  # 需要足够的历史数据来计算特征
        return None
    
    # 基础特征
    data['price'] = data['Close_NVDA']
    data['log_return'] = np.log(data['price'] / data['price'].shift(1))
    data['volatility'] = data['log_return'].rolling(20).std()

    # 移动平均距离特征
    for window in [20, 50, 200]:
        data[f'ma_{window}'] = data['price'].rolling(window).mean()
        data[f'dist_from_ma{window}'] = (data['price'] - data[f'ma_{window}']) / data[f'ma_{window}']

    # 动量特征
    data['momentum_ratio'] = data['price'] / data['price'].shift(20)
    data['momentum_5d'] = data['price'] / data['price'].shift(5)

    # RSI - 确保只使用历史数据
    delta = data['price'].diff()
    gain = (delta.where(delta > 0, 0)).rolling(14).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(14).mean()
    rs = gain / loss
    data['rsi'] = 100 - (100 / (1 + rs))

    # 布林带位置
    bb_mid = data['price'].rolling(20).mean()
    bb_std = data['price'].rolling(20).std()
    data['bb_position'] = (data['price'] - (bb_mid - 2 * bb_std)) / (4 * bb_std)

    # 趋势和突破特征 - 关键：使用shift(1)避免前视偏差
    data['trend_strength'] = (data[f'ma_20'] > data[f'ma_50']).astype(int) + (data[f'ma_50'] > data[f'ma_200']).astype(int)
    data['breakout_20'] = (data['price'] > data['price'].rolling(20).max().shift(1)).astype(int)
    data['price_rank_60'] = data['price'].rolling(60).rank(pct=True)
    data['momentum_acceleration'] = data['momentum_5d'] / data['momentum_5d'].shift(5)

    # NVDA vs QQQ 和 VIX 特征
    data['nvda_qqq_ratio'] = data['Close_NVDA'] / data['Close_QQQ']
    vix_ma = data['Close_^VIX'].rolling(20).mean()
    data['vix_signal'] = data['Close_^VIX'] / vix_ma - 1
    data['vix_regime'] = (data['Close_^VIX'] < 25).astype(int)

    # Pin Bar 特征 - 只使用OHLC数据，无前视偏差
    open_price = data['Open_NVDA']
    high_price = data['High_NVDA']
    low_price = data['Low_NVDA']
    close_price = data['Close_NVDA']

    body_size = abs(close_price - open_price)
    total_range = high_price - low_price
    total_range[total_range == 0] = 1e-6

    upper_wick = high_price - np.maximum(open_price, close_price)
    lower_wick = np.minimum(open_price, close_price) - low_price

    bullish_pin = (lower_wick / total_range > 0.6) & (body_size / total_range < 0.2)
    bearish_pin = (upper_wick / total_range > 0.6) & (body_size / total_range < 0.2)

    data['pin_bar_score'] = np.where(bullish_pin, 1, np.where(bearish_pin, -1, 0))

    # 特征列表
    feature_columns = [
        'log_return', 'volatility', 'dist_from_ma20', 'dist_from_ma200',
        'momentum_ratio', 'rsi', 'bb_position', 'nvda_qqq_ratio', 'vix_signal',
        'dist_from_ma50', 'momentum_5d', 'trend_strength', 'breakout_20',
        'price_rank_60', 'momentum_acceleration', 'vix_regime', 'pin_bar_score'
    ]
    
    # 只返回最后一行的特征（当前时点）
    current_features = data[feature_columns].iloc[-1]
    
    # 检查是否有NaN值
    if current_features.isnull().any():
        return None
        
    return current_features

def train_hmm_model_bias_free(train_data, feature_columns):
    """无偏差HMM模型训练"""
    
    features_list = []
    for i in range(200, len(train_data)):  # 确保有足够历史数据计算特征
        features = calculate_features_realtime(train_data, i)
        if features is not None:
            features_list.append(features)
    
    if len(features_list) < 100:
        return None, None
        
    features_df = pd.DataFrame(features_list)
    
    scaler = StandardScaler()
    X = scaler.fit_transform(features_df)
    
    # 训练HMM模型
    best_model = None
    best_bic = np.inf
    
    for n_states in [2, 3, 4]:
        try:
            model = GaussianHMM(n_components=n_states, covariance_type="spherical", 
                              n_iter=200, random_state=42, tol=1e-2, 
                              init_params='st', params='stmc')
            model.fit(X)
            bic = model.bic(X)
            
            if bic < best_bic:
                best_bic = bic
                best_model = model
        except:
            continue
    
    return best_model, scaler

def get_bias_free_position(predicted_state, n_states):
    """无偏差的仓位映射 - 基于固定逻辑，不使用历史统计"""
    
    if n_states == 2:
        # 2状态：简单的牛熊分类
        return 0.0 if predicted_state == 0 else 1.0
    
    elif n_states == 3:
        # 3状态：熊市-中性-牛市
        position_map = {0: 0.0, 1: 0.6, 2: 1.0}
        return position_map.get(predicted_state, 0.5)
    
    else:
        # 4+状态：均匀分布仓位
        return predicted_state / (n_states - 1)

def run_bias_free_backtest(start_date="2019-01-01", end_date="2025-08-30", 
                          backtest_start="2021-01-01", retraining_days=63):
    """运行无前视偏差的回测"""
    
    print(f"🚀 开始NVDA V8.3无偏差回测 (每{retraining_days}天重训)...")
    
    # 下载原始数据
    symbols = ["NVDA", "QQQ", "^VIX"]
    print("📊 下载数据...")
    raw_data = yf.download(symbols, start=start_date, end=end_date, progress=False)
    
    if isinstance(raw_data.columns, pd.MultiIndex):
        raw_data.columns = raw_data.columns.to_series().apply(lambda x: f"{x[0]}_{x[1]}")
    
    backtest_start_date = pd.to_datetime(backtest_start)
    backtest_indices = raw_data.loc[backtest_start_date:].index
    
    results = []
    current_model = None
    current_scaler = None
    last_retrain_date = None
    retrain_count = 0
    
    print(f"   回测期间: {backtest_indices[0].date()} 到 {backtest_indices[-1].date()}")
    
    for current_date in backtest_indices:
        
        # 获取到当前日期的历史数据索引
        historical_end_idx = raw_data.index.get_loc(current_date)
        
        # 检查是否需要重新训练
        should_retrain = (
            current_model is None or
            last_retrain_date is None or
            (current_date - last_retrain_date).days >= retraining_days
        )
        
        if should_retrain:
            retrain_count += 1
            if retrain_count <= 5 or retrain_count % 10 == 0:
                print(f"   📚 重训 #{retrain_count}: {current_date.date()}")
            
            try:
                # 使用到前一日的数据进行训练
                train_data = raw_data.iloc[:historical_end_idx]  # 不包含当日
                
                if len(train_data) < 252:
                    continue
                
                # 训练模型
                feature_columns = [
                    'log_return', 'volatility', 'dist_from_ma20', 'dist_from_ma200',
                    'momentum_ratio', 'rsi', 'bb_position', 'nvda_qqq_ratio', 'vix_signal',
                    'dist_from_ma50', 'momentum_5d', 'trend_strength', 'breakout_20',
                    'price_rank_60', 'momentum_acceleration', 'vix_regime', 'pin_bar_score'
                ]
                
                model, scaler = train_hmm_model_bias_free(train_data, feature_columns)
                
                if model is not None and scaler is not None:
                    current_model = model
                    current_scaler = scaler
                    last_retrain_date = current_date
                    
                    if retrain_count <= 3:
                        print(f"     模型训练成功: {model.n_components}个状态")
                else:
                    if retrain_count <= 3:
                        print("     模型训练失败")
                    continue
                    
            except Exception as e:
                if retrain_count <= 3:
                    print(f"     训练异常: {e}")
                continue
        
        # 使用当前模型进行预测
        if current_model is not None and current_scaler is not None:
            try:
                # 计算当日特征（使用到前一日的数据）
                features_data = raw_data.iloc[:historical_end_idx + 1]  # 包含当日用于特征计算
                current_features = calculate_features_realtime(features_data, historical_end_idx)
                
                if current_features is None:
                    continue
                
                # 预测状态
                X_current = current_scaler.transform([current_features])
                predicted_state = current_model.predict(X_current)[0]
                
                # 获取仓位 - 使用无偏差映射
                position = get_bias_free_position(predicted_state, current_model.n_components)
                
                # 计算当日收益
                current_price = raw_data.loc[current_date, 'Close_NVDA']
                prev_date_idx = historical_end_idx - 1
                if prev_date_idx >= 0:
                    prev_price = raw_data.iloc[prev_date_idx]['Close_NVDA']
                    log_return = np.log(current_price / prev_price)
                else:
                    log_return = 0
                
                results.append({
                    'date': current_date,
                    'predicted_state': predicted_state,
                    'position': position,
                    'log_return': log_return,
                    'n_states': current_model.n_components
                })
                
            except Exception as e:
                continue
    
    if not results:
        print("   ❌ 无法生成交易信号")
        return None, 0
    
    results_df = pd.DataFrame(results)
    results_df.set_index('date', inplace=True)
    
    print(f"   ✅ 生成{len(results_df)}个信号, 重训{retrain_count}次")
    return results_df, retrain_count

def calculate_performance_bias_free(results_df):
    """计算无偏差策略表现"""
    
    # 策略收益计算
    results_df['trade'] = results_df['position'].diff().fillna(0).abs()
    results_df['strategy_return'] = (
        results_df['position'].shift(1).fillna(0) * results_df['log_return']
    )
    results_df['strategy_return'] -= results_df['trade'] * 0.001  # 交易成本
    
    # 累计收益
    results_df['strategy_cumulative'] = np.exp(results_df['strategy_return'].cumsum())
    results_df['buy_and_hold_cumulative'] = np.exp(results_df['log_return'].cumsum())
    
    # 年化指标
    days_in_year = 252
    
    strategy_annual_return = np.exp(results_df['strategy_return'].mean() * days_in_year) - 1
    strategy_annual_vol = results_df['strategy_return'].std() * np.sqrt(days_in_year)
    strategy_sharpe = strategy_annual_return / strategy_annual_vol if strategy_annual_vol != 0 else 0
    
    bnh_annual_return = np.exp(results_df['log_return'].mean() * days_in_year) - 1
    bnh_annual_vol = results_df['log_return'].std() * np.sqrt(days_in_year)
    
    # 最大回撤
    strategy_cum_max = results_df['strategy_cumulative'].cummax()
    strategy_drawdown = (results_df['strategy_cumulative'] - strategy_cum_max) / strategy_cum_max
    strategy_max_drawdown = strategy_drawdown.min()
    
    bnh_cum_max = results_df['buy_and_hold_cumulative'].cummax()
    bnh_drawdown = (results_df['buy_and_hold_cumulative'] - bnh_cum_max) / bnh_cum_max
    bnh_max_drawdown = bnh_drawdown.min()
    
    return {
        'strategy_annual_return': strategy_annual_return,
        'strategy_annual_vol': strategy_annual_vol,
        'strategy_sharpe': strategy_sharpe,
        'strategy_max_drawdown': strategy_max_drawdown,
        'bnh_annual_return': bnh_annual_return,
        'bnh_max_drawdown': bnh_max_drawdown,
        'total_trades': (results_df['trade'] > 0).sum(),
        'win_rate': (results_df['strategy_return'] > 0).mean(),
        'results_df': results_df
    }

def main():
    """主函数"""
    try:
        print("🚀 NVDA HMM V8.3 无前视偏差版本")
        print("修复了V8.2中发现的严重前视偏差问题")
        print("="*60)
        
        # 运行无偏差回测
        results_df, retrain_count = run_bias_free_backtest(
            retraining_days=63  # 季度重训
        )
        
        if results_df is not None:
            performance = calculate_performance_bias_free(results_df)
            
            print("\\n" + "="*80)
            print("📊 NVDA HMM V8.3 无前视偏差回测结果")
            print("="*80)
            print(f"回测期间: {results_df.index[0].date()} 至 {results_df.index[-1].date()}")
            print(f"{'指标':<20} | {'策略表现':<12} | {'买入持有':<12}")
            print("-"*80)
            print(f"{'年化收益率':<20} | {performance['strategy_annual_return']:<12.2%} | {performance['bnh_annual_return']:<12.2%}")
            print(f"{'夏普比率':<20} | {performance['strategy_sharpe']:<12.2f} | {'N/A':<12}")
            print(f"{'最大回撤':<20} | {performance['strategy_max_drawdown']:<12.2%} | {performance['bnh_max_drawdown']:<12.2%}")
            print(f"{'胜率':<20} | {performance['win_rate']:<12.2%} | {'N/A':<12}")
            print(f"{'交易次数':<20} | {performance['total_trades']:<12.0f} | {'0':<12}")
            print(f"{'重训次数':<20} | {retrain_count:<12.0f} | {'0':<12}")
            print("="*80)
            
            print("\\n🔍 关键修复点:")
            print("✅ 移除了基于全历史统计的状态映射")
            print("✅ 采用固定的状态-仓位映射逻辑")
            print("✅ 实施真正的实时特征计算")
            print("✅ 严格遵循时间序列约束")
        
    except Exception as e:
        print(f"❌ 程序执行失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()