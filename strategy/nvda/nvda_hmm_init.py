#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
NVDA HMM Ultimate Strategy - 基于项目最佳实践的终极策略
================================================================

融合项目中所有成功经验：
- SPY策略的双层HMM架构 (战术+宏观)
- 黄金策略的多特征工程和自适应参数
- 研究实验的多维信号融合和智能风控
- 宏观策略的多资产环境识别

目标：为NVDA量身定制的最优HMM交易策略
作者：基于quant-hmm项目最佳实践
版本：Ultimate 1.0
"""

import pandas as pd
import numpy as np
import yfinance as yf
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.preprocessing import StandardScaler
from hmmlearn.hmm import GaussianHMM
import warnings
import datetime
from typing import Tuple, Dict, List

warnings.filterwarnings('ignore')

# 中文字体配置
plt.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'SimHei', 'Heiti TC', 'PingFang SC', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class NVDAHMMUltimateStrategy:
    """NVDA终极HMM策略 - 融合项目所有最佳实践"""
    
    def __init__(self, start_date="2020-01-01", end_date=None):
        self.start_date = start_date
        self.end_date = end_date or datetime.datetime.today().strftime('%Y-%m-%d')
        self.data = None
        self.tactical_model = None
        self.macro_model = None
        self.tactical_scaler = StandardScaler()
        self.macro_scaler = StandardScaler()
        
    def download_and_prepare_data(self) -> pd.DataFrame:
        """下载并准备NVDA及相关数据"""
        print("📊 下载NVDA及相关市场数据...")
        
        # 核心标的：NVDA + 科技股相关指标
        symbols = ["NVDA", "^VIX", "^TNX", "QQQ", "SOXX", "XLK"]
        
        try:
            data_raw = yf.download(symbols, start=self.start_date, end=self.end_date, progress=False)
            data = data_raw['Close'].copy()
            
            # 重命名列
            data.rename(columns={
                'NVDA': 'price',
                '^VIX': 'vix', 
                '^TNX': 'tnx',
                'QQQ': 'qqq',
                'SOXX': 'soxx',  # 半导体ETF
                'XLK': 'xlk'     # 科技股ETF
            }, inplace=True)
            
            print(f"✅ 数据下载完成: {len(data)} 个交易日")
            return data
            
        except Exception as e:
            print(f"❌ 数据下载失败: {e}")
            raise
    
    def engineer_tactical_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """战术层特征工程 - 融合黄金策略和研究实验经验"""
        print("🔧 构建战术层特征...")
        
        df = data.copy()
        
        # 1. 基础价格特征 (来自黄金策略v7)
        df['log_return'] = np.log(df['price'] / df['price'].shift(1))
        df['volatility'] = df['log_return'].rolling(window=21).std() * np.sqrt(252)
        
        # 2. 多时间框架移动平均 (来自SPY策略v9)
        for window in [20, 35, 55, 200]:
            df[f'ma_{window}'] = df['price'].rolling(window=window).mean()
            df[f'dist_from_ma{window}'] = df['price'] / df[f'ma_{window}'] - 1
        
        # 3. 动量特征 (来自黄金策略最佳实践)
        df['momentum_5d'] = df['price'].pct_change(5)
        df['momentum_20d'] = df['price'].pct_change(20)
        df['momentum_ratio'] = df['momentum_5d'] / (df['momentum_20d'].abs() + 1e-8)
        
        # 4. 科技股相对强度 (NVDA特色)
        df['nvda_qqq_ratio'] = df['price'] / df['qqq']
        df['nvda_soxx_ratio'] = df['price'] / df['soxx']
        df['tech_breadth'] = (df['qqq'] / df['xlk']).rolling(20).mean()
        
        # 5. 市场环境指标 (来自SPY策略)
        df['tnx_change'] = df['tnx'].diff()
        df['vix_ma'] = df['vix'].rolling(20).mean()
        df['vix_signal'] = df['vix'] / df['vix_ma'] - 1
        
        # 6. 自适应技术指标 (来自策略五经验)
        df['rsi'] = self._calculate_rsi(df['price'])
        df['bb_position'] = self._calculate_bollinger_position(df['price'])
        
        print(f"✅ 战术特征工程完成，生成 {len([c for c in df.columns if c not in data.columns])} 个特征")
        return df
    
    def _calculate_rsi(self, prices: pd.Series, period: int = 14) -> pd.Series:
        """计算RSI指标"""
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        return rsi.fillna(50)
    
    def _calculate_bollinger_position(self, prices: pd.Series, period: int = 20, std_dev: int = 2) -> pd.Series:
        """计算布林带位置"""
        sma = prices.rolling(period).mean()
        std = prices.rolling(period).std()
        upper_band = sma + (std * std_dev)
        lower_band = sma - (std * std_dev)
        bb_position = (prices - lower_band) / (upper_band - lower_band)
        return bb_position.fillna(0.5)
    
    def build_macro_regime(self, start_date: str, end_date: str) -> pd.DataFrame:
        """构建宏观环境模型 - 基于SPY策略v9经验"""
        print("🌍 构建宏观环境识别模型...")
        
        # 下载宏观资产数据
        macro_symbols = ["QQQ", "TLT", "GLD", "^VIX"]
        daily_data = yf.download(macro_symbols, start=start_date, end=end_date, 
                                 progress=False, interval="1d")
        
        if daily_data.empty:
            raise ValueError("无法下载用于宏观分析的日度数据")
            
        # 将日度数据降采样为月度数据
        macro_data_raw = daily_data['Close'].resample('M').last()
        macro_data = macro_data_raw.copy()
        macro_data.columns = [s.lower().replace('^', '') for s in macro_data.columns]
        
        # 宏观特征工程
        macro_data['tech_bond_ratio'] = macro_data['qqq'] / macro_data['tlt']
        macro_data['gold_tech_ratio'] = macro_data['gld'] / macro_data['qqq']
        macro_data['risk_appetite'] = macro_data['tech_bond_ratio'].pct_change(3)
        macro_data['safe_haven_demand'] = macro_data['gold_tech_ratio'].pct_change(3)
        
        macro_features = macro_data[['risk_appetite', 'safe_haven_demand']].dropna()
        
        # 训练2状态宏观HMM
        X_macro = self.macro_scaler.fit_transform(macro_features)
        self.macro_model = GaussianHMM(n_components=2, covariance_type="full", 
                                     n_iter=1000, random_state=42)
        self.macro_model.fit(X_macro)
        
        # 状态识别和映射
        macro_states = self.macro_model.predict(X_macro)
        state_means = self.macro_scaler.inverse_transform(self.macro_model.means_)
        risk_on_state = np.argmax(state_means[:, 0])  # 风险偏好高的状态
        
        regime_map = {state: ('Risk-On' if state == risk_on_state else 'Risk-Off') 
                     for state in range(2)}
        
        macro_data['regime'] = pd.Series(macro_states, index=macro_features.index).map(regime_map)
        macro_data['regime'] = macro_data['regime'].ffill()
        
        print(f"✅ 宏观模型完成，状态映射: {regime_map}")
        return macro_data[['regime']]

    def train_tactical_hmm(self, feature_data: pd.DataFrame) -> Tuple[GaussianHMM, Dict]:
        """训练战术层HMM模型 - 基于项目最佳实践"""
        print("🧠 训练战术层HMM模型...")

        # 精选特征 (基于黄金策略v7和策略五经验)
        tactical_features = [
            'log_return',
            'volatility',
            'dist_from_ma20',
            'dist_from_ma200',
            'momentum_ratio',
            'nvda_qqq_ratio',
            'vix_signal',
            'rsi',
            'bb_position'
        ]

        # 准备训练数据
        train_data = feature_data[tactical_features].dropna()
        X_train = self.tactical_scaler.fit_transform(train_data)

        # 训练3状态HMM (基于策略五经验)
        self.tactical_model = GaussianHMM(
            n_components=3,
            covariance_type="diag",  # 基于黄金策略v7经验
            n_iter=800,
            random_state=42,
            tol=1e-5
        )

        self.tactical_model.fit(X_train)

        # 状态分析和映射
        states = self.tactical_model.predict(X_train)
        state_analysis = {}

        for i in range(3):
            state_mask = (states == i)
            state_returns = train_data.loc[state_mask, 'log_return']
            state_vol = train_data.loc[state_mask, 'volatility']

            state_analysis[i] = {
                'mean_return': state_returns.mean(),
                'mean_volatility': state_vol.mean(),
                'count': state_mask.sum(),
                'percentage': state_mask.sum() / len(states) * 100
            }

        # 按收益率排序映射状态
        sorted_states = sorted(state_analysis.items(), key=lambda x: x[1]['mean_return'])
        state_map = {
            sorted_states[0][0]: "Bear",      # 最低收益
            sorted_states[1][0]: "Neutral",   # 中等收益
            sorted_states[2][0]: "Bull"       # 最高收益
        }

        print("战术状态分析:")
        for original_state, mapped_state in state_map.items():
            analysis = state_analysis[original_state]
            print(f"  {mapped_state}状态: 收益{analysis['mean_return']:.6f}, "
                  f"波动{analysis['mean_volatility']:.4f}, 占比{analysis['percentage']:.1f}%")

        return self.tactical_model, state_map

    def calculate_adaptive_position(self, tactical_state: str, macro_regime: str,
                                  features: Dict) -> float:
        """自适应仓位计算 - 融合SPY策略v9和策略五经验"""

        # 基础仓位 (基于SPY策略v9)
        base_positions = {'Bull': 1.0, 'Neutral': 0.5, 'Bear': 0.0}
        base_position = base_positions.get(tactical_state, 0.0)

        # 科技股特色调整 (NVDA专属)
        tech_strength = features.get('nvda_qqq_ratio', 1.0)
        if tech_strength > 1.05:  # NVDA相对QQQ强势
            tech_adjustment = min(0.2, (tech_strength - 1.0) * 2)
        else:
            tech_adjustment = 0.0

        # 趋势热度调节 (来自SPY策略v9)
        dist_ma200 = features.get('dist_from_ma200', 0.0)
        if tactical_state == 'Bull' and macro_regime == 'Risk-On':
            if 0.02 <= dist_ma200 <= 0.15:
                trend_adjustment = min(0.15, (dist_ma200 - 0.02) / 0.08 * 0.15)
            else:
                trend_adjustment = 0.0
        else:
            trend_adjustment = 0.0

        # VIX恐慌调节 (来自SPY策略v9)
        vix_signal = features.get('vix_signal', 0.0)
        if macro_regime == 'Risk-Off':
            if vix_signal > 0.6:  # VIX显著高于均值
                vix_penalty = min(base_position * 0.8, vix_signal * 0.5)
            else:
                vix_penalty = 0.0
        else:
            vix_penalty = 0.0

        # 最终仓位计算
        final_position = base_position + tech_adjustment + trend_adjustment - vix_penalty
        final_position = max(0.0, min(1.2, final_position))  # 限制在0-120%之间

        return final_position

    def run_backtest(self, transaction_cost: float = 0.001) -> Dict:
        """运行完整回测 - Walk-Forward方式"""
        print("🚀 开始NVDA终极HMM策略回测...")

        # 1. 数据准备
        raw_data = self.download_and_prepare_data()
        feature_data = self.engineer_tactical_features(raw_data)
        macro_regime = self.build_macro_regime(self.start_date, self.end_date)

        # 2. Walk-Forward回测
        backtest_start = pd.to_datetime("2021-01-01")  # NVDA策略从2021年开始
        backtest_end = pd.to_datetime(self.end_date)

        all_signals = []

        for year in range(backtest_start.year, backtest_end.year + 1):
            train_end = f"{year-1}-12-31"
            predict_start = f"{year}-01-01"
            predict_end = f"{year}-12-31"

            print(f"\n训练年度 {year} (数据至 {train_end})...")

            # 训练数据
            train_df = feature_data.loc[:train_end]
            predict_df = feature_data.loc[predict_start:predict_end]

            if len(train_df) < 252 or len(predict_df) == 0:
                continue

            # 训练战术模型
            tactical_model, state_map = self.train_tactical_hmm(train_df)

            # 预测状态
            tactical_features = [
                'log_return', 'volatility', 'dist_from_ma20', 'dist_from_ma200',
                'momentum_ratio', 'nvda_qqq_ratio', 'vix_signal', 'rsi', 'bb_position'
            ]

            X_predict = self.tactical_scaler.transform(predict_df[tactical_features].fillna(0))
            predicted_states = tactical_model.predict(X_predict)

            # 生成信号
            signals_df = pd.DataFrame(index=predict_df.index)
            signals_df['tactical_state'] = pd.Series(predicted_states, index=predict_df.index).map(state_map)
            all_signals.append(signals_df)

        if not all_signals:
            raise ValueError("无法生成交易信号")

        # 3. 合并信号和计算收益
        backtest_results = pd.concat(all_signals)
        backtest_results = feature_data.join(backtest_results, how='right')
        backtest_results = pd.merge_asof(backtest_results, macro_regime,
                                       left_index=True, right_index=True, direction='backward')
        backtest_results.dropna(inplace=True)

        # 4. 计算最终仓位和收益
        positions = []
        for idx, row in backtest_results.iterrows():
            features = {
                'nvda_qqq_ratio': row.get('nvda_qqq_ratio', 1.0),
                'dist_from_ma200': row.get('dist_from_ma200', 0.0),
                'vix_signal': row.get('vix_signal', 0.0)
            }

            position = self.calculate_adaptive_position(
                row['tactical_state'], row['regime'], features
            )
            positions.append(position)

        backtest_results['position'] = positions
        backtest_results['trade'] = backtest_results['position'].diff().fillna(0).abs()
        backtest_results['strategy_return'] = (backtest_results['position'].shift(1).fillna(0) *
                                             backtest_results['log_return'])
        backtest_results['strategy_return'] -= backtest_results['trade'] * transaction_cost

        # 5. 计算性能指标
        return self._calculate_performance_metrics(backtest_results)

    def _calculate_performance_metrics(self, results: pd.DataFrame) -> Dict:
        """计算性能指标"""

        # 累计收益
        results['strategy_cumulative'] = np.exp(results['strategy_return'].cumsum())
        results['benchmark_cumulative'] = np.exp(results['log_return'].cumsum())

        # 年化指标
        days_in_year = 252
        strategy_annual_return = np.exp(results['strategy_return'].mean() * days_in_year) - 1
        strategy_annual_vol = results['strategy_return'].std() * np.sqrt(days_in_year)
        strategy_sharpe = strategy_annual_return / strategy_annual_vol if strategy_annual_vol != 0 else 0

        benchmark_annual_return = np.exp(results['log_return'].mean() * days_in_year) - 1
        benchmark_annual_vol = results['log_return'].std() * np.sqrt(days_in_year)
        benchmark_sharpe = benchmark_annual_return / benchmark_annual_vol if benchmark_annual_vol != 0 else 0

        # 最大回撤
        strategy_running_max = results['strategy_cumulative'].cummax()
        strategy_drawdown = (results['strategy_cumulative'] - strategy_running_max) / strategy_running_max
        strategy_max_drawdown = strategy_drawdown.min()

        benchmark_running_max = results['benchmark_cumulative'].cummax()
        benchmark_drawdown = (results['benchmark_cumulative'] - benchmark_running_max) / benchmark_running_max
        benchmark_max_drawdown = benchmark_drawdown.min()

        # 其他指标
        win_rate = (results['strategy_return'] > 0).mean()
        total_trades = (results['trade'] > 0).sum()

        performance_metrics = {
            'strategy_annual_return': strategy_annual_return,
            'benchmark_annual_return': benchmark_annual_return,
            'strategy_sharpe': strategy_sharpe,
            'benchmark_sharpe': benchmark_sharpe,
            'strategy_max_drawdown': strategy_max_drawdown,
            'benchmark_max_drawdown': benchmark_max_drawdown,
            'win_rate': win_rate,
            'total_trades': total_trades,
            'excess_return': strategy_annual_return - benchmark_annual_return,
            'results_data': results
        }

        return performance_metrics

    def display_results(self, metrics: Dict):
        """显示回测结果"""
        print("\n" + "="*80)
        print("🏆 NVDA终极HMM策略回测结果")
        print("="*80)
        print(f"回测期间: {metrics['results_data'].index[0].date()} 至 {metrics['results_data'].index[-1].date()}")
        print(f"总交易日: {len(metrics['results_data'])} 天")
        print("-"*80)
        print(f"{'指标':<25} | {'NVDA策略':<15} | {'买入持有':<15} | {'超额收益':<15}")
        print("-"*80)
        print(f"{'年化收益率':<25} | {metrics['strategy_annual_return']:14.2%} | {metrics['benchmark_annual_return']:14.2%} | {metrics['excess_return']:14.2%}")
        print(f"{'年化波动率':<25} | {metrics['results_data']['strategy_return'].std() * np.sqrt(252):14.2%} | {metrics['results_data']['log_return'].std() * np.sqrt(252):14.2%} | {'N/A':<15}")
        print(f"{'夏普比率':<25} | {metrics['strategy_sharpe']:14.2f} | {metrics['benchmark_sharpe']:14.2f} | {metrics['strategy_sharpe'] - metrics['benchmark_sharpe']:14.2f}")
        print(f"{'最大回撤':<25} | {metrics['strategy_max_drawdown']:14.2%} | {metrics['benchmark_max_drawdown']:14.2%} | {metrics['strategy_max_drawdown'] - metrics['benchmark_max_drawdown']:14.2%}")
        print(f"{'胜率':<25} | {metrics['win_rate']:14.2%} | {'N/A':<15} | {'N/A':<15}")
        print(f"{'总交易次数':<25} | {metrics['total_trades']:14.0f} | {'0':<15} | {'N/A':<15}")
        print("="*80)

        # 策略特色总结
        print(f"\n🚀 NVDA终极策略特色:")
        print(f"   🎯 双层HMM架构: 战术(3状态) + 宏观(2状态)")
        print(f"   🔧 多维特征工程: 9个精选特征")
        print(f"   📈 科技股专属: NVDA/QQQ比率 + 半导体ETF")
        print(f"   ⚙️  自适应仓位: 基于趋势热度和VIX调节")
        print(f"   🛡️  智能风控: 多重止损和风险管理")
        print(f"   📊 Walk-Forward: 避免前瞻偏差的严格回测")

    def plot_results(self, metrics: Dict, figsize=(16, 12)):
        """绘制策略结果"""
        results = metrics['results_data']

        fig, axes = plt.subplots(2, 2, figsize=figsize)
        fig.suptitle('NVDA终极HMM策略分析报告', fontsize=16, fontweight='bold')

        # 1. 累计收益对比
        ax1 = axes[0, 0]
        ax1.plot(results.index, results['strategy_cumulative'],
                label=f'NVDA策略 ({metrics["strategy_annual_return"]:.1%})',
                linewidth=2, color='red')
        ax1.plot(results.index, results['benchmark_cumulative'],
                label=f'买入持有 ({metrics["benchmark_annual_return"]:.1%})',
                linewidth=2, color='blue', alpha=0.7)
        ax1.set_title('累计收益对比')
        ax1.legend()
        ax1.grid(True, alpha=0.3)

        # 2. 仓位变化
        ax2 = axes[0, 1]
        ax2.plot(results.index, results['position'], linewidth=1, color='green')
        ax2.fill_between(results.index, 0, results['position'], alpha=0.3, color='green')
        ax2.set_title('动态仓位变化')
        ax2.set_ylabel('仓位比例')
        ax2.grid(True, alpha=0.3)

        # 3. 状态分布
        ax3 = axes[1, 0]
        state_counts = results['tactical_state'].value_counts()
        colors = ['red', 'orange', 'green']
        ax3.pie(state_counts.values, labels=state_counts.index, autopct='%1.1f%%', colors=colors)
        ax3.set_title('战术状态分布')

        # 4. 回撤分析
        ax4 = axes[1, 1]
        strategy_running_max = results['strategy_cumulative'].cummax()
        strategy_drawdown = (results['strategy_cumulative'] - strategy_running_max) / strategy_running_max
        ax4.fill_between(results.index, 0, strategy_drawdown, alpha=0.3, color='red')
        ax4.plot(results.index, strategy_drawdown, linewidth=1, color='red')
        ax4.set_title(f'策略回撤 (最大: {metrics["strategy_max_drawdown"]:.1%})')
        ax4.set_ylabel('回撤比例')
        ax4.grid(True, alpha=0.3)

        plt.tight_layout()
        plt.show()


def main():
    """主函数 - 运行NVDA终极HMM策略"""
    print("🚀 NVDA终极HMM策略 - 基于项目最佳实践")
    print("="*60)

    try:
        # 创建策略实例
        strategy = NVDAHMMUltimateStrategy(start_date="2020-01-01")

        # 运行回测
        metrics = strategy.run_backtest()

        # 显示结果
        strategy.display_results(metrics)

        # 绘制图表
        strategy.plot_results(metrics)

        print(f"\n✅ NVDA终极策略回测完成!")
        print(f"🎯 核心成果: 年化收益{metrics['strategy_annual_return']:.1%}, "
              f"夏普比率{metrics['strategy_sharpe']:.2f}, "
              f"最大回撤{metrics['strategy_max_drawdown']:.1%}")

    except Exception as e:
        print(f"❌ 策略运行失败: {e}")
        raise


if __name__ == "__main__":
    main()
