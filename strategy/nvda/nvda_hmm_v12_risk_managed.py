#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
NVDA HMM V12 Risk Managed - 系统性风险管理版
================================================

基于V11的成功信号系统和其回撤巨大的失败教训，本版本核心目标是
引入系统性的风险管理模块，在尽可能保留V11收益能力的同时，
严格控制最大回撤。

核心优化:
1.  **波动率倒数加权**: 仓位与市场短期波动率成反比，实现风险平价。
2.  **动态回撤控制**: 引入策略自身净值回撤的监控，作为强制减仓的“安全阀”。
3.  **状态风险约束**: 在识别为熊市状态时，严格限制最大仓位。

作者: Gemini
日期: 2025年8月30日
版本: V12.0 (Risk Managed)
"""

import pandas as pd
import numpy as np
import yfinance as yf
import matplotlib.pyplot as plt
from sklearn.preprocessing import StandardScaler
from hmmlearn.hmm import GaussianHMM
import warnings
import datetime

warnings.filterwarnings('ignore')

# --- V11/V12 通用函数 (特征计算, 模型训练) ---

plt.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'SimHei', 'Heiti TC', 'PingFang SC', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def calculate_features_realtime(price_data, current_idx):
    data = price_data.iloc[:current_idx + 1].copy()
    if len(data) < 252: return None, None
    data['price'] = data['Close_NVDA']
    data['log_return'] = np.log(data['price'] / data['price'].shift(1))
    data['volatility'] = data['log_return'].rolling(20).std() * np.sqrt(252)
    data['ma200'] = data['price'].rolling(200).mean()
    data['dist_from_ma200'] = (data['price'] - data['ma200']) / data['ma200']
    delta = data['price'].diff()
    gain = (delta.where(delta > 0, 0)).rolling(14).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(14).mean()
    rs = gain / loss
    data['rsi'] = 100 - (100 / (1 + rs))
    bb_mid = data['price'].rolling(20).mean()
    bb_std = data['price'].rolling(20).std()
    data['bb_position'] = (data['price'] - (bb_mid - 2 * bb_std)) / (4 * bb_std)
    data['bb_width'] = (4 * bb_std) / bb_mid
    data['nvda_qqq_ratio'] = data['Close_NVDA'] / data['Close_QQQ']
    vix_ma = data['Close_^VIX'].rolling(20).mean()
    data['vix_signal'] = data['Close_^VIX'] / vix_ma - 1
    data['ma20'] = data['price'].rolling(20).mean()
    data['ma50'] = data['price'].rolling(50).mean()
    data['trend_strength'] = (data['ma20'] > data['ma50']).astype(int) + (data['ma50'] > data['ma200']).astype(int)
    data['breakout_20'] = (data['price'] > data['price'].rolling(20).max().shift(1)).astype(int)
    open_p, high_p, low_p, close_p = data['Open_NVDA'], data['High_NVDA'], data['Low_NVDA'], data['Close_NVDA']
    body = abs(close_p - open_p); total_range = high_p - low_p; total_range[total_range == 0] = 1e-6
    upper_wick = high_p - np.maximum(open_p, close_p); lower_wick = np.minimum(open_p, close_p) - low_p
    bullish_pin = (lower_wick / total_range > 0.6) & (body / total_range < 0.2)
    bearish_pin = (upper_wick / total_range > 0.6) & (body / total_range < 0.2)
    data['pin_bar_score'] = np.where(bullish_pin, 1, np.where(bearish_pin, -1, 0))
    plus_dm = high_p.diff(); minus_dm = low_p.diff()
    plus_dm[plus_dm < 0] = 0; minus_dm[minus_dm > 0] = 0
    tr = pd.concat([high_p - low_p, abs(high_p - close_p.shift(1)), abs(low_p - close_p.shift(1))], axis=1).max(axis=1)
    atr = tr.rolling(14).mean()
    plus_di = 100 * (plus_dm.ewm(alpha=1/14).mean() / atr)
    minus_di = abs(100 * (minus_dm.ewm(alpha=1/14).mean() / atr))
    dx = (abs(plus_di - minus_di) / (abs(plus_di + minus_di) + 1e-6)) * 100
    data['adx'] = ((dx.shift(1) * 13) + dx) / 14
    feature_columns = ['log_return', 'volatility', 'dist_from_ma200', 'rsi', 'bb_position', 'nvda_qqq_ratio', 'vix_signal', 'trend_strength', 'breakout_20', 'pin_bar_score', 'adx', 'bb_width']
    current_features = data[feature_columns].iloc[-1]
    if current_features.isnull().any(): return None, None
    return current_features, feature_columns

def train_optimal_hmm(train_data):
    features_list = []; feature_columns = []
    for i in range(252, len(train_data)):
        features, f_cols = calculate_features_realtime(train_data, i)
        if features is not None:
            features_list.append(features)
            if not feature_columns: feature_columns = f_cols
    if len(features_list) < 100: return None, None, None
    features_df = pd.DataFrame(features_list, columns=feature_columns).fillna(method='ffill').dropna()
    scaler = StandardScaler()
    X = scaler.fit_transform(features_df)
    best_model = None; best_bic = np.inf
    for n_states in [2, 3, 4]:
        try:
            model = GaussianHMM(n_components=n_states, covariance_type="spherical", n_iter=200, random_state=42, tol=1e-2, init_params='st', params='stmc')
            model.fit(X)
            bic = model.bic(X)
            if bic < best_bic: best_bic = bic; best_model = model
        except Exception: continue
    return best_model, scaler, feature_columns

# --- V12 核心：风险管理仓位计算 ---

def calculate_risk_managed_position(predicted_state, n_states, features, recent_returns, target_vol=0.4, drawdown_threshold=0.1):
    """
    引入波动率倒数加权和动态回撤控制的仓位计算。
    """
    # 1. V11的自适应基础仓位
    if n_states == 2: base_position = 1.0 if predicted_state == 1 else 0.0
    elif n_states == 3: base_position = {0: 0.0, 1: 0.6, 2: 1.0}.get(predicted_state, 0.5)
    else: base_position = {0: 0.0, 1: 0.4, 2: 0.8, 3: 1.0}.get(predicted_state, 0.5)
    
    pos_adj = 0.0
    if base_position > 0.5 and features.get('adx', 20) > 25: pos_adj += 0.05
    if features.get('vix_signal', 0) > 0.5: pos_adj -= base_position * 0.1
    if base_position > 0.5 and features.get('pin_bar_score', 0) == 1: pos_adj += 0.05
    signal_position = np.clip(base_position + pos_adj, 0.0, 1.0)

    # 2. 状态风险约束 (熊市状态严格限制仓位)
    if n_states > 2 and predicted_state == 0: # 状态0被定义为最熊的状态
        signal_position = min(signal_position, 0.1) # 在熊市状态下，最大仓位不超过10%

    # 3. 波动率倒数加权 (Volatility Targeting)
    current_vol = features.get('volatility', target_vol)
    vol_scalar = min(1.0, target_vol / (current_vol + 1e-6))

    # 4. 动态回撤控制 (Drawdown Control)
    if len(recent_returns) >= 20:
        recent_cumulative = np.exp(recent_returns.cumsum())
        recent_max = recent_cumulative.cummax()
        current_drawdown = (recent_cumulative.iloc[-1] - recent_max.iloc[-1]) / recent_max.iloc[-1]
        if current_drawdown < -drawdown_threshold:
            drawdown_limiter = 0.2 # 触发回撤阈值，仓位限制在20%
        else:
            drawdown_limiter = 1.0
    else:
        drawdown_limiter = 1.0

    # 最终仓位 = 信号仓位 * 波动率标量 * 回撤限制器
    final_position = signal_position * vol_scalar * drawdown_limiter
    return np.clip(final_position, 0.0, 1.0)

# --- V12 回测主函数 ---

def run_v12_backtest(start_date="2019-01-01", end_date=None, backtest_start="2021-01-01", retraining_days=63):
    end_date = end_date or datetime.datetime.now().strftime('%Y-%m-%d')
    print(f"🚀 开始NVDA V12风险管理策略回测...")
    
    symbols = ["NVDA", "QQQ", "^VIX"]
    raw_data = yf.download(symbols, start=start_date, end=end_date, progress=False)
    if isinstance(raw_data.columns, pd.MultiIndex): raw_data.columns = raw_data.columns.to_series().apply(lambda x: f"{x[0]}_{x[1]}")
    
    backtest_indices = raw_data.loc[pd.to_datetime(backtest_start):].index
    results = []
    current_model, current_scaler, feature_columns = None, None, []
    last_retrain_date = None
    strategy_returns_history = pd.Series(dtype=float)

    for current_date in backtest_indices:
        historical_end_idx = raw_data.index.get_loc(current_date)
        
        if current_model is None or (current_date - last_retrain_date).days >= retraining_days:
            print(f"   📚 正在重训模型，数据截止于: {current_date.date()}")
            try:
                train_data = raw_data.iloc[:historical_end_idx]
                if len(train_data) < 252: continue
                model, scaler, f_cols = train_optimal_hmm(train_data)
                if model and scaler and f_cols:
                    current_model, current_scaler, feature_columns = model, scaler, f_cols
                    last_retrain_date = current_date
                    print(f"     ✅ 模型训练成功: 动态选择 {model.n_components} 个状态")
                else: print("     ⚠️ 模型训练失败，沿用旧模型")
            except Exception as e: print(f"     ❌ 训练异常: {e}，沿用旧模型")
        
        if current_model:
            try:
                features_data = raw_data.iloc[:historical_end_idx + 1]
                current_features, _ = calculate_features_realtime(features_data, historical_end_idx)
                if current_features is None: continue
                
                X_current = current_scaler.transform(pd.DataFrame([current_features], columns=feature_columns))
                predicted_state = current_model.predict(X_current)[0]
                
                # 传递最近20天的策略收益用于回撤计算
                recent_returns = strategy_returns_history.iloc[-20:]
                position = calculate_risk_managed_position(predicted_state, current_model.n_components, current_features, recent_returns)
                
                log_return = np.log(raw_data.loc[current_date, 'Close_NVDA'] / raw_data.iloc[historical_end_idx - 1]['Close_NVDA'])
                strategy_return = position * log_return # 简化计算，成本在最后统一处理
                strategy_returns_history.at[current_date] = strategy_return

                results.append({'date': current_date, 'position': position, 'log_return': log_return, 'strategy_return_pre_cost': strategy_return})
            except Exception:
                continue
    
    if not results: raise ValueError("回测未能生成任何结果。")
    results_df = pd.DataFrame(results).set_index('date')
    return results_df

# --- V12 性能评估与显示 ---

def calculate_performance(results_df):
    results_df['trade'] = results_df['position'].diff().fillna(0).abs()
    results_df['strategy_return'] = results_df['strategy_return_pre_cost'] - (results_df['trade'] * 0.001)
    results_df['strategy_cumulative'] = np.exp(results_df['strategy_return'].cumsum())
    results_df['buy_and_hold_cumulative'] = np.exp(results_df['log_return'].cumsum())
    days = 252
    strat_ret = np.exp(results_df['strategy_return'].mean() * days) - 1
    strat_vol = results_df['strategy_return'].std() * np.sqrt(days)
    strat_sharpe = strat_ret / strat_vol if strat_vol != 0 else 0
    bnh_ret = np.exp(results_df['log_return'].mean() * days) - 1
    strat_max_dd = (results_df['strategy_cumulative'] / results_df['strategy_cumulative'].cummax() - 1).min()
    return {'strategy_annual_return': strat_ret, 'benchmark_annual_return': bnh_ret, 'strategy_sharpe': strat_sharpe, 'strategy_max_drawdown': strat_max_dd, 'total_trades': (results_df['trade'] > 0).sum(), 'win_rate': (results_df['strategy_return'] > 0).mean(), 'results_data': results_df}

def display_results(metrics):
    print("\n" + "="*80)
    print("🏆 NVDA HMM V12 风险管理策略 - 回测结果")
    print("="*80)
    rdf = metrics['results_data']
    print(f"回测期间: {rdf.index[0].date()} 至 {rdf.index[-1].date()}")
    print(f"{ '指标':<25} | { 'V12策略':<15} | { '买入持有':<15}")
    print("-"*80)
    print(f"{ '年化收益率':<25} | {metrics['strategy_annual_return']:14.2%} | {metrics['benchmark_annual_return']:14.2%}")
    print(f"{ '夏普比率':<25} | {metrics['strategy_sharpe']:14.2f} | {'N/A':<15}")
    print(f"{ '最大回撤':<25} | {metrics['strategy_max_drawdown']:14.2%} | {'N/A':<15}")
    print(f"{ '胜率':<25} | {metrics['win_rate']:14.2%} | {'N/A':<15}")
    print(f"{ '总交易次数':<25} | {metrics['total_trades']:14.0f} | {'N/A':<15}")
    print("="*80)
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(16, 12), sharex=True, gridspec_kw={'height_ratios': [3, 1]})
    fig.suptitle('NVDA HMM V12 风险管理策略表现', fontsize=16)
    ax1.plot(rdf.index, rdf['strategy_cumulative'], label='策略累计收益', color='r'); ax1.plot(rdf.index, rdf['buy_and_hold_cumulative'], label='基准累计收益', color='b', alpha=0.6)
    ax1.set_title('累计收益对比'); ax1.legend(); ax1.grid(True)
    ax2.plot(rdf.index, rdf['position'], label='仓位', color='g', drawstyle='steps-post')
    ax2.set_title('策略仓位变化'); ax2.legend(); ax2.grid(True)
    plt.tight_layout(rect=[0, 0, 1, 0.96]); plt.show()

def main():
    try:
        results_df = run_v12_backtest()
        metrics = calculate_performance(results_df)
        display_results(metrics)
    except Exception as e:
        print(f"❌ 程序执行失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
