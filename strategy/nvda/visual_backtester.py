#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Visual Backtester for HMM Strategies
=====================================

This script integrates a specific HMM strategy (e.g., V7) into a 
`vectorbt` backtesting framework to provide detailed visualizations
and performance reports, including a trade-by-trade log with reasons.
"""

import vectorbt as vbt
import pandas as pd
import numpy as np
import os
import sys

# --- Robust Import for Relocated Script ---
try:
    from nvda_hmm_v7_pinbar import NVDAHMMV7Strategy
except ImportError:
    project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    if project_root not in sys.path:
        sys.path.append(project_root)
    from strategy.nvda.nvda_hmm_v7_pinbar import NVDAHMMV7Strategy

# --- Module 1: Data Handler ---
class DataHandler:
    """Handles fetching and preparing market data."""
    def __init__(self, ticker: str, start_date: str, end_date: str):
        self.ticker = ticker
        self.start_date = start_date
        self.end_date = end_date

    def fetch_data(self) -> pd.DataFrame:
        """
        Fetches data from Yahoo Finance and handles potential network errors.
        Returns a timezone-naive DataFrame.
        """
        print(f"Fetching data for {self.ticker}...")
        try:
            price_data = vbt.YFData.download(
                self.ticker, 
                start=self.start_date,
                end=self.end_date,
                interval="1d"
            ).get(["Open", "Close", "High", "Low", "Volume"])
            
            if price_data.empty:
                print("Error: No data downloaded.")
                return None

            data = price_data.dropna().copy()
            data.index = data.index.tz_localize(None)
            print("Data fetching complete.")
            return data
        except Exception as e:
            print(f"\n--- Network or Data Error ---\n{e}")
            return None

# --- Module 2: HMM Signal Generator ---
class HMMSignalGenerator:
    """Runs the HMM strategy to generate signals."""
    def __init__(self, data: pd.DataFrame, strategy_class):
        if data is None: raise ValueError("Data must be valid.")
        self.data = data
        self.strategy = strategy_class(start_date=data.index[0].strftime('%Y-%m-%d'), 
                                     end_date=data.index[-1].strftime('%Y-%m-%d'))

    def generate_signals(self) -> dict:
        """
        Runs the full HMM walk-forward backtest and converts the
        resulting positions into vectorbt-compatible signals.
        """
        print("Running HMM strategy to generate position signals...")
        metrics = self.strategy.run_backtest()
        results_data = metrics['results_data']
        
        positions = results_data['position']
        states = results_data['state']
        
        positions = positions.reindex(self.data.index, method='ffill').fillna(0)
        states = states.reindex(self.data.index, method='ffill').fillna("N/A")

        print("Converting positions to entry/exit signals...")
        entries = (positions > 0) & (positions.shift(1) == 0)
        exits = (positions == 0) & (positions.shift(1) > 0)
        
        signals = {"V7_HMM_Strategy": {"entries": entries, "exits": exits, "states": states}}
        print("Signal generation complete.")
        return signals

# --- Module 3: Backtester ---
class Backtester:
    """Executes vectorbt backtests."""
    def __init__(self, close_prices: pd.Series, signals: dict, freq: str = 'D', fees=0.001, slippage=0.001):
        self.close_prices = close_prices
        self.signals = signals
        self.freq = freq
        self.fees = fees
        self.slippage = slippage

    def run(self) -> dict:
        """Runs the vectorbt portfolio simulation."""
        print("Running vectorbt backtest...")
        results = {}
        for name, params in self.signals.items():
            results[name] = vbt.Portfolio.from_signals(
                close=self.close_prices,
                entries=params["entries"],
                exits=params["exits"],
                size=np.inf,
                fees=self.fees,
                slippage=self.slippage,
                freq=self.freq
            )
        print("Backtesting complete.")
        return results

# --- Module 4: Results Analyzer ---
class Analyzer:
    """Displays and saves backtest results, including a detailed trade log."""
    def __init__(self, backtest_results: dict, signals: dict, close_prices: pd.Series, report_dir: str = "reports"):
        self.results = backtest_results
        self.signals = signals
        self.close_prices = close_prices
        self.report_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), report_dir)
        if not os.path.exists(self.report_dir):
            os.makedirs(self.report_dir)

    def print_stats(self):
        """Prints the main performance stats from vectorbt."""
        for name, portfolio in self.results.items():
            print(f"\n--- Performance Analysis for: {name} ---")
            print(portfolio.stats())
            print("-" * 50)

    def list_trades_with_reasons(self):
        """Lists each trade with the HMM state as the reason."""
        print("\n--- Trade Log with Reasons ---")
        for name, portfolio in self.results.items():
            print(f"\nStrategy: {name}")
            trades = portfolio.trades.records_readable
            if trades.empty:
                print("No trades were executed.")
                continue
            
            states = self.signals[name].get('states')
            
            print("{:<22} {:<5} {:>10}   {:<20}".format("Date", "Side", "Price", "Reason (HMM State)"))
            print("-" * 65)
            for i, trade in trades.iterrows():
                entry_date = trade['Entry Timestamp']
                entry_reason = states.get(entry_date, "N/A") if states is not None else "N/A"
                print("{:<22} {:<5} {:>10.2f}   {:<20}".format(
                    str(entry_date), 
                    "Buy", 
                    trade['Avg Entry Price'], 
                    f"Enter on {entry_reason}"
                ))
                
                exit_date = trade['Exit Timestamp']
                if exit_date == self.close_prices.index[-1]:
                    exit_reason = "End of Backtest"
                else:
                    exit_reason = states.get(exit_date, "N/A") if states is not None else "N/A"
                
                print("{:<22} {:<5} {:>10.2f}   {:<20}".format(
                    str(exit_date), 
                    "Sell", 
                    trade['Avg Exit Price'], 
                    f"Exit on {exit_reason}"
                ))
            print("-" * 65)

    def show_and_save_report(self):
        """Saves the HTML report without attempting to display it directly."""
        print(f"\nGenerating HTML report...")
        for name, portfolio in self.results.items():
            report_path = os.path.join(self.report_dir, f"{name}_vbt_dashboard.html")
            try:
                fig = portfolio.plot(title=f"{name} Backtest")
                # fig.show() # Disabled to prevent hanging in some environments
                fig.write_html(report_path)
                print(f"  - Successfully generated report for {name}: {report_path}")
            except Exception as e:
                print(f"  - Could not generate report for {name}: {e}")

# --- Main Execution Flow ---
def main():
    """Main pipeline to run the backtest and visualization."""
    # 1. Data
    data_handler = DataHandler(ticker="NVDA", start_date="2019-01-01", end_date="2025-08-31")
    market_data = data_handler.fetch_data()
    if market_data is None:
        print("\nExecution stopped because data could not be downloaded.")
        return

    # 2. Signals
    signal_generator = HMMSignalGenerator(market_data, NVDAHMMV7Strategy)
    strategy_signals = signal_generator.generate_signals()

    # 3. Backtest
    backtest_start_date = "2021-01-01"
    close_prices_for_backtest = market_data.loc[backtest_start_date:]['Close']
    sliced_signals = {}
    for name, signals in strategy_signals.items():
        sliced_signals[name] = {
            "entries": signals["entries"].loc[backtest_start_date:],
            "exits": signals["exits"].loc[backtest_start_date:]
        }
    backtester = Backtester(close_prices_for_backtest, sliced_signals)
    results = backtester.run()

    # 4. Analysis
    analyzer = Analyzer(results, strategy_signals, close_prices_for_backtest)
    analyzer.print_stats()
    analyzer.list_trades_with_reasons()
    analyzer.show_and_save_report()

if __name__ == "__main__":
    main()
