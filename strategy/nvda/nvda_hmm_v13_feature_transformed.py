#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
NVDA HMM V13 Feature Transformed - 特征变换驱动版
======================================================

基于V12的成功，并从 feature_engineering_lab.py 获得启发，
本版本将优化重点从“特征选择”转向“特征变换与生成”。

核心优化:
1.  **小波变换 (DWT)**: 使用DWT对收益率序列进行分解，生成一个去噪后的趋势特征和一个高频噪声特征，以更先进的方式捕捉趋势与波动。
2.  **主成分分析 (PCA)**: 对多个相关的技术指标进行PCA降维，提取少数几个不相关的核心主成分，提升模型稳定性和效率。
3.  **保留V12风险管理**: 继承V12已被验证成功的系统性风险管理模块。

作者: Gemini
日期: 2025年8月30日
版本: V13.0 (Feature Transformed)
"""

import pandas as pd
import numpy as np
import yfinance as yf
import matplotlib.pyplot as plt
from sklearn.preprocessing import StandardScaler
from sklearn.decomposition import PCA
from hmmlearn.hmm import GaussianHMM
import pywt # 新增小波变换库
import warnings
import datetime

warnings.filterwarnings('ignore')
plt.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'SimHei', 'Heiti TC', 'PingFang SC', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

# --- V13 核心：特征变换与生成 ---

def calculate_transformed_features(price_data, current_idx, pca_model=None, fit_pca=False):
    """
    计算经过小波变换和PCA处理的特征。
    """
    data = price_data.iloc[:current_idx + 1].copy()
    if len(data) < 252: return None, None, None

    # 1. 计算基础指标 (用于PCA)
    data['price'] = data['Close_NVDA']
    log_return = np.log(data['price'] / data['price'].shift(1))
    bb_mid = data['price'].rolling(20).mean()
    bb_std = data['price'].rolling(20).std()
    data['bb_width'] = (4 * bb_std) / bb_mid
    delta = data['price'].diff()
    gain = (delta.where(delta > 0, 0)).rolling(14).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(14).mean()
    data['rsi'] = 100 - (100 / (1 + gain / loss))
    high_p, low_p, close_p = data['High_NVDA'], data['Low_NVDA'], data['Close_NVDA']
    plus_dm = high_p.diff(); minus_dm = low_p.diff()
    plus_dm[plus_dm < 0] = 0; minus_dm[minus_dm > 0] = 0
    tr = pd.concat([high_p - low_p, abs(high_p - close_p.shift(1)), abs(low_p - close_p.shift(1))], axis=1).max(axis=1)
    atr = tr.rolling(14).mean()
    plus_di = 100 * (plus_dm.ewm(alpha=1/14).mean() / atr)
    minus_di = abs(100 * (minus_dm.ewm(alpha=1/14).mean() / atr))
    dx = (abs(plus_di - minus_di) / (abs(plus_di + minus_di) + 1e-6)) * 100
    data['adx'] = ((dx.shift(1) * 13) + dx) / 14
    data['vix_signal'] = data['Close_^VIX'] / data['Close_^VIX'].rolling(20).mean() - 1

    # 2. 小波变换 (DWT)
    dwt_coeffs = pywt.wavedec(log_return.dropna(), 'db4', level=3)
    dwt_trend = dwt_coeffs[0][-1] # 低频趋势
    dwt_noise = np.sum(np.square(dwt_coeffs[-1])) # 高频噪声能量

    # 3. 主成分分析 (PCA)
    pca_features_cols = ['rsi', 'adx', 'bb_width', 'vix_signal']
    pca_data = data[pca_features_cols].iloc[-1].values.reshape(1, -1)
    
    if fit_pca:
        pca_train_data = data[pca_features_cols].dropna()
        pca_model = PCA(n_components=2)
        pca_model.fit(pca_train_data)

    if pca_model is None:
        return None, None, None
        
    transformed_pca = pca_model.transform(pca_data)

    # 4. 组合成最终特征
    final_features = {
        'dwt_trend': dwt_trend,
        'dwt_noise': dwt_noise,
        'pca_1': transformed_pca[0, 0],
        'pca_2': transformed_pca[0, 1]
    }
    feature_columns = list(final_features.keys())
    current_features = pd.Series(final_features)

    if current_features.isnull().any(): return None, None, None
    return current_features, feature_columns, pca_model

# --- V13 模型训练与回测框架 (继承V12) ---

def train_v13_hmm(train_data):
    features_list = []; feature_columns = []; pca_model = None
    # 首次计算特征以拟合PCA
    _, _, pca_model = calculate_transformed_features(train_data, len(train_data)-1, fit_pca=True)
    if pca_model is None: return None, None, None

    for i in range(252, len(train_data)):
        features, f_cols, _ = calculate_transformed_features(train_data, i, pca_model=pca_model)
        if features is not None:
            features_list.append(features)
            if not feature_columns: feature_columns = f_cols
    
    if len(features_list) < 100: return None, None, None
    features_df = pd.DataFrame(features_list, columns=feature_columns).fillna(method='ffill').dropna()
    scaler = StandardScaler()
    X = scaler.fit_transform(features_df)
    
    best_model = None; best_bic = np.inf
    for n_states in [2, 3, 4]:
        try:
            model = GaussianHMM(n_components=n_states, covariance_type="diag", n_iter=200, random_state=42, tol=1e-2)
            model.fit(X)
            bic = model.bic(X)
            if bic < best_bic: best_bic = bic; best_model = model
        except Exception: continue
    return best_model, scaler, feature_columns, pca_model

def calculate_risk_managed_position(predicted_state, n_states, features, recent_returns, target_vol=0.4, drawdown_threshold=0.1):
    # V12的风险管理仓位逻辑保持不变
    if n_states == 2: base_position = 1.0 if predicted_state == 1 else 0.0
    elif n_states == 3: base_position = {0: 0.0, 1: 0.6, 2: 1.0}.get(predicted_state, 0.5)
    else: base_position = {0: 0.0, 1: 0.4, 2: 0.8, 3: 1.0}.get(predicted_state, 0.5)
    signal_position = np.clip(base_position, 0.0, 1.0)
    if n_states > 2 and predicted_state == 0: signal_position = min(signal_position, 0.1)
    current_vol = features.get('volatility', target_vol) # 需要原始波动率
    vol_scalar = min(1.0, target_vol / (current_vol + 1e-6))
    if len(recent_returns) >= 20:
        recent_cumulative = np.exp(recent_returns.cumsum())
        current_drawdown = (recent_cumulative.iloc[-1] / recent_cumulative.max()) - 1
        drawdown_limiter = 0.2 if current_drawdown < -drawdown_threshold else 1.0
    else: drawdown_limiter = 1.0
    final_position = signal_position * vol_scalar * drawdown_limiter
    return np.clip(final_position, 0.0, 1.0)

def run_v13_backtest(start_date="2019-01-01", end_date=None, backtest_start="2021-01-01", retraining_days=63):
    end_date = end_date or datetime.datetime.now().strftime('%Y-%m-%d')
    print(f"🚀 开始NVDA V13特征变换策略回测...")
    
    symbols = ["NVDA", "QQQ", "^VIX"]
    raw_data = yf.download(symbols, start=start_date, end=end_date, progress=False)
    if isinstance(raw_data.columns, pd.MultiIndex): raw_data.columns = raw_data.columns.to_series().apply(lambda x: f"{x[0]}_{x[1]}")
    raw_data['volatility'] = np.log(raw_data['Close_NVDA'] / raw_data['Close_NVDA'].shift(1)).rolling(20).std() * np.sqrt(252)

    backtest_indices = raw_data.loc[pd.to_datetime(backtest_start):].index
    results = []
    current_model, current_scaler, feature_columns, current_pca = None, None, [], None
    last_retrain_date = None
    strategy_returns_history = pd.Series(dtype=float)

    for current_date in backtest_indices:
        historical_end_idx = raw_data.index.get_loc(current_date)
        
        if current_model is None or (current_date - last_retrain_date).days >= retraining_days:
            print(f"   📚 正在重训模型(含PCA/DWT)，数据截止于: {current_date.date()}")
            try:
                train_data = raw_data.iloc[:historical_end_idx]
                if len(train_data) < 252: continue
                model, scaler, f_cols, pca_model = train_v13_hmm(train_data)
                if model and scaler and f_cols and pca_model:
                    current_model, current_scaler, feature_columns, current_pca = model, scaler, f_cols, pca_model
                    last_retrain_date = current_date
                    print(f"     ✅ 模型训练成功: 动态选择 {model.n_components} 个状态")
                else: print("     ⚠️ 模型训练失败")
            except Exception as e: print(f"     ❌ 训练异常: {e}")
        
        if current_model:
            try:
                features_data = raw_data.iloc[:historical_end_idx + 1]
                current_features, _, _ = calculate_transformed_features(features_data, historical_end_idx, pca_model=current_pca)
                if current_features is None: continue
                
                X_current = current_scaler.transform(pd.DataFrame([current_features], columns=feature_columns))
                predicted_state = current_model.predict(X_current)[0]
                
                recent_returns = strategy_returns_history.iloc[-20:]
                # 传递原始波动率用于风险管理
                current_features['volatility'] = raw_data.loc[current_date, 'volatility']
                position = calculate_risk_managed_position(predicted_state, current_model.n_components, current_features, recent_returns)
                
                log_return = np.log(raw_data.loc[current_date, 'Close_NVDA'] / raw_data.iloc[historical_end_idx - 1]['Close_NVDA'])
                strategy_return = position * log_return
                strategy_returns_history.at[current_date] = strategy_return
                results.append({'date': current_date, 'position': position, 'log_return': log_return, 'strategy_return_pre_cost': strategy_return})
            except Exception: continue
    
    if not results: raise ValueError("回测未能生成任何结果。")
    return pd.DataFrame(results).set_index('date')

# --- V13 性能评估与显示 (继承V12) ---
def calculate_performance(results_df):
    results_df['trade'] = results_df['position'].diff().fillna(0).abs()
    results_df['strategy_return'] = results_df['strategy_return_pre_cost'] - (results_df['trade'] * 0.001)
    results_df['strategy_cumulative'] = np.exp(results_df['strategy_return'].cumsum())
    results_df['buy_and_hold_cumulative'] = np.exp(results_df['log_return'].cumsum())
    days = 252
    strat_ret = np.exp(results_df['strategy_return'].mean() * days) - 1
    strat_vol = results_df['strategy_return'].std() * np.sqrt(days)
    strat_sharpe = strat_ret / strat_vol if strat_vol != 0 else 0
    bnh_ret = np.exp(results_df['log_return'].mean() * days) - 1
    strat_max_dd = (results_df['strategy_cumulative'] / results_df['strategy_cumulative'].cummax() - 1).min()
    return {'strategy_annual_return': strat_ret, 'benchmark_annual_return': bnh_ret, 'strategy_sharpe': strat_sharpe, 'strategy_max_drawdown': strat_max_dd, 'total_trades': (results_df['trade'] > 0).sum(), 'win_rate': (results_df['strategy_return'] > 0).mean(), 'results_data': results_df}

def display_results(metrics):
    print("\n" + "="*80)
    print("🏆 NVDA HMM V13 特征变换策略 - 回测结果")
    print("="*80)
    rdf = metrics['results_data']
    print(f"回测期间: {rdf.index[0].date()} 至 {rdf.index[-1].date()}")
    print(f"{ '指标':<25} | { 'V13策略':<15} | { '买入持有':<15}")
    print("-"*80)
    print(f"{ '年化收益率':<25} | {metrics['strategy_annual_return']:14.2%} | {metrics['benchmark_annual_return']:14.2%}")
    print(f"{ '夏普比率':<25} | {metrics['strategy_sharpe']:14.2f} | {'N/A':<15}")
    print(f"{ '最大回撤':<25} | {metrics['strategy_max_drawdown']:14.2%} | {'N/A':<15}")
    print(f"{ '胜率':<25} | {metrics['win_rate']:14.2%} | {'N/A':<15}")
    print(f"{ '总交易次数':<25} | {metrics['total_trades']:14.0f} | {'N/A':<15}")
    print("="*80)

def main():
    try:
        results_df = run_v13_backtest()
        metrics = calculate_performance(results_df)
        display_results(metrics)
    except Exception as e:
        print(f"❌ 程序执行失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
