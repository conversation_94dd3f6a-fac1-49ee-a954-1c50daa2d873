#!/usr/bin/env python3
"""
NVDA HMM V6 Ultimate (已使用滚动评估重构)
==========================================

基于V1-V6演进分析，融合V3成功要素，并应用滚动回测框架。

核心设计原则:
1. 保持V3的成功配置(Neutral 60%仓位等)
2. 增强Bull状态识别敏感度 (通过新特征和状态评分)
3. 加入适度趋势跟踪机制 (通过动态仓位增强)

*注意: 此文件已被重构，以使用与之前版本一致的滚动回测框架，
从而实现公平的性能比较。*

作者：基于V1-V6演进分析的终极优化
日期：2024年 (重构于2025年)
版本：V6.2 (滚动评估版)
"""

import warnings
warnings.filterwarnings('ignore')

import pandas as pd
import numpy as np
import yfinance as yf
from datetime import datetime
import matplotlib.pyplot as plt
from typing import Dict, Tuple
from sklearn.preprocessing import StandardScaler
from hmmlearn.hmm import GaussianHMM

# 中文字体配置
plt.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'SimHei', 'Heiti TC', 'PingFang SC', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class NVDAHMMUltimateStrategy:
    """NVDA HMM V6 Ultimate策略 (滚动评估版)"""

    def __init__(self, start_date="2019-01-01", end_date=None, transaction_cost=0.001):
        self.start_date = start_date
        self.end_date = end_date or datetime.now().strftime('%Y-%m-%d')
        self.transaction_cost = transaction_cost
        
        self.scaler = StandardScaler()
        self.model = None
        self.state_mapping = {}
        
        self.features = [
            'log_return', 'volatility', 'dist_from_ma20', 'dist_from_ma200',
            'momentum_ratio', 'rsi', 'bb_position', 'nvda_qqq_ratio', 'vix_signal',
            'dist_from_ma50', 'momentum_5d', 'trend_strength', 'breakout_20',
            'price_rank_60', 'momentum_acceleration', 'vix_regime'
        ]

    def download_and_prepare_data(self) -> pd.DataFrame:
        """下载并准备数据及特征"""
        print("📊 下载所需数据...")
        symbols = ["NVDA", "QQQ", "^VIX"]
        data = yf.download(symbols, start=self.start_date, end=self.end_date, progress=False)
        
        if isinstance(data.columns, pd.MultiIndex):
            data.columns = data.columns.to_series().apply(lambda x: f"{x[0]}_{x[1]}")
        print("✅ 数据下载完成")

        print("🔧 终极特征工程...")
        result = data.copy()
        result['price'] = result['Close_NVDA']
        result['log_return'] = np.log(result['price'] / result['price'].shift(1))
        result['volatility'] = result['log_return'].rolling(20).std()

        for window in [20, 50, 200]:
            result[f'ma_{window}'] = result['price'].rolling(window).mean()
            result[f'dist_from_ma{window}'] = (result['price'] - result[f'ma_{window}']) / result[f'ma_{window}']

        result['momentum_ratio'] = result['price'] / result['price'].shift(20)
        result['momentum_5d'] = result['price'] / result['price'].shift(5)

        delta = result['price'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(14).mean()
        rs = gain / loss
        result['rsi'] = 100 - (100 / (1 + rs))

        bb_mid = result['price'].rolling(20).mean()
        bb_std = result['price'].rolling(20).std()
        result['bb_position'] = (result['price'] - (bb_mid - 2 * bb_std)) / (4 * bb_std)

        result['trend_strength'] = (result['ma_20'] > result['ma_50']).astype(int) + (result['ma_50'] > result['ma_200']).astype(int)
        result['breakout_20'] = (result['price'] > result['price'].rolling(20).max().shift(1)).astype(int)
        result['price_rank_60'] = result['price'].rolling(60).rank(pct=True)
        result['momentum_acceleration'] = result['momentum_5d'] / result['momentum_5d'].shift(5)

        result['nvda_qqq_ratio'] = result['Close_NVDA'] / result['Close_QQQ']
        vix_ma = result['Close_^VIX'].rolling(20).mean()
        result['vix_signal'] = result['Close_^VIX'] / vix_ma - 1
        result['vix_regime'] = (result['Close_^VIX'] < 25).astype(int)
        
        print("✅ 终极特征工程完成")
        return result

    def train_hmm(self, features_data: pd.DataFrame) -> Dict:
        """训练HMM模型"""
        clean_data = features_data[self.features].dropna()
        if len(clean_data) < 252: raise ValueError(f"训练数据不足: {len(clean_data)}")
            
        X_scaled = self.scaler.fit_transform(clean_data)
        
        self.model = GaussianHMM(
            n_components=3, covariance_type="spherical", n_iter=200,
            random_state=42, tol=1e-2, init_params='st', params='stmc'
        )
        self.model.fit(X_scaled)
        
        states = self.model.predict(X_scaled)
        
        state_analysis = {}
        for i in range(3):
            mask = (states == i)
            if mask.sum() > 0:
                state_data = clean_data.loc[mask]
                state_analysis[i] = {
                    'mean_return': state_data['log_return'].mean(),
                    'trend_strength': state_data['trend_strength'].mean(),
                    'breakout_ratio': state_data['breakout_20'].mean(),
                    'ma200_dist': state_data['dist_from_ma200'].mean(),
                }
        
        def state_score(analysis):
            return (analysis['mean_return'] * 0.6 + analysis['trend_strength'] * 0.2 + 
                    analysis['breakout_ratio'] * 0.1 + max(0, analysis['ma200_dist']) * 0.1)
        
        sorted_states = sorted(state_analysis.items(), key=lambda x: state_score(x[1]))
        
        self.state_mapping = {
            sorted_states[0][0]: "Bear",
            sorted_states[1][0]: "Neutral",
            sorted_states[2][0]: "Bull"
        }
        return self.state_mapping

    def calculate_position(self, state: str, features: Dict) -> float:
        """终极仓位计算"""
        base_pos = {'Bull': 1.0, 'Neutral': 0.6, 'Bear': 0.0}.get(state, 0.0)
        
        tech_adjustment = min(0.1, (features.get('nvda_qqq_ratio', 1.0) - 1.0) * 1.0) if features.get('nvda_qqq_ratio', 1.0) > 1.1 else 0.0
        trend_adjustment = min(0.05, features.get('dist_from_ma200', 0.0) * 0.25) if state == 'Bull' and 0.05 <= features.get('dist_from_ma200', 0.0) <= 0.2 else 0.0
        vix_penalty = min(base_pos * 0.2, features.get('vix_signal', 0.0) * 0.1) if features.get('vix_signal', 0.0) > 0.3 else 0.0
        
        trend_boost = 0.05 if state in ['Bull', 'Neutral'] and features.get('trend_strength', 0.0) >= 2 else 0.0
        breakout_boost = min(0.05, features.get('breakout_20', 0) * 0.025) if state == 'Bull' and features.get('breakout_20', 0) > 0 else 0.0
        momentum_boost = min(0.03, (features.get('momentum_acceleration', 1.0) - 1.0) * 0.1) if state == 'Bull' and features.get('momentum_acceleration', 1.0) > 1.1 else 0.0
        
        final_position = base_pos + tech_adjustment + trend_adjustment + trend_boost + breakout_boost + momentum_boost - vix_penalty
        return max(0.0, min(1.0, final_position))

    def run_backtest(self) -> Dict:
        """运行滚动回测"""
        print("🚀 开始NVDA HMM V6 Ultimate策略滚动回测...")
        feature_data = self.download_and_prepare_data()
        backtest_start = pd.to_datetime("2021-01-01")
        backtest_end = pd.to_datetime(self.end_date)
        all_signals = []

        for year in range(backtest_start.year, backtest_end.year + 1):
            train_end_date = f"{year-1}-12-31"
            predict_start_date = f"{year}-01-01"
            predict_end_date = f"{year}-12-31"
            print(f"\n训练年度 {year} (数据至 {train_end_date})...")
            train_df = feature_data.loc[:train_end_date]
            predict_df = feature_data.loc[predict_start_date:predict_end_date]
            if len(train_df) < 252 or len(predict_df) == 0: continue
            
            self.train_hmm(train_df)
            
            clean_predict_data = predict_df[self.features].dropna()
            if not clean_predict_data.empty:
                X_predict_scaled = self.scaler.transform(clean_predict_data)
                raw_states = self.model.predict(X_predict_scaled)
                signals_df = pd.DataFrame(index=clean_predict_data.index)
                signals_df['state'] = pd.Series(raw_states, index=clean_predict_data.index).map(self.state_mapping)
                all_signals.append(signals_df)

        if not all_signals: raise ValueError("无法生成任何交易信号")
            
        backtest_results = pd.concat(all_signals)
        backtest_results = feature_data.join(backtest_results, how='right')
        backtest_results.dropna(subset=['state'], inplace=True)

        positions = []
        for idx, row in backtest_results.iterrows():
            features = {f: row.get(f, 0.0) for f in self.features}
            position = self.calculate_position(row['state'], features)
            positions.append(position)
        backtest_results['position'] = positions

        backtest_results['trade'] = backtest_results['position'].diff().fillna(0).abs()
        backtest_results['strategy_return'] = (backtest_results['position'].shift(1).fillna(0) * backtest_results['log_return'])
        backtest_results['strategy_return'] -= backtest_results['trade'] * self.transaction_cost
        
        return self._calculate_performance_metrics(backtest_results)

    def _calculate_performance_metrics(self, results: pd.DataFrame) -> Dict:
        """计算性能指标"""
        if results.empty: return {}
        results['strategy_cumulative'] = np.exp(results['strategy_return'].cumsum())
        results['benchmark_cumulative'] = np.exp(results['log_return'].cumsum())
        days = 252
        annual_return = np.exp(results['strategy_return'].mean() * days) - 1
        annual_vol = results['strategy_return'].std() * np.sqrt(days)
        sharpe = annual_return / annual_vol if annual_vol != 0 else 0
        bench_annual_return = np.exp(results['log_return'].mean() * days) - 1
        running_max = results['strategy_cumulative'].cummax()
        drawdown = (results['strategy_cumulative'] - running_max) / running_max
        return {
            'strategy_annual_return': annual_return,
            'benchmark_annual_return': bench_annual_return,
            'strategy_sharpe': sharpe,
            'strategy_max_drawdown': drawdown.min(),
            'win_rate': (results['strategy_return'] > 0).mean(),
            'total_trades': (results['trade'] > 0).sum(),
            'results_data': results
        }

    def display_results(self, metrics: Dict):
        """显示回测结果"""
        if not metrics: return
        print("\n" + "="*80)
        print("📈 NVDA HMM V6 Ultimate (滚动评估版) 回测结果")
        print("="*80)
        print(f"回测期间: {metrics['results_data'].index[0].date()} 至 {metrics['results_data'].index[-1].date()}")
        print(f"{ '指标':<25} | {'NVDA策略':<15} | {'买入持有':<15}")
        print("-"*80)
        print(f"{ '年化收益率':<25} | {metrics['strategy_annual_return']:14.2%} | {metrics['benchmark_annual_return']:14.2%}")
        print(f"{ '夏普比率':<25} | {metrics['strategy_sharpe']:14.2f} | {'N/A':<15}")
        print(f"{ '最大回撤':<25} | {metrics['strategy_max_drawdown']:14.2%} | {'N/A':<15}")
        print(f"{ '胜率':<25} | {metrics['win_rate']:14.2%} | {'N/A':<15}")
        print(f"{ '总交易次数':<25} | {metrics['total_trades']:14.0f} | {'N/A':<15}")
        print("="*80)

def main():
    """主程序"""
    print("🚀 NVDA HMM V6 Ultimate (滚动评估版)")
    print("=" * 60)
    try:
        strategy = NVDAHMMUltimateStrategy()
        metrics = strategy.run_backtest()
        strategy.display_results(metrics)
    except Exception as e:
        print(f"❌ V6 Ultimate策略运行失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
