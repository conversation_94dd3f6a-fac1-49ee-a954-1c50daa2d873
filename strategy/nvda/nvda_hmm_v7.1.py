#!/usr/bin/env python3
"""
NVDA HMM V8 Regime-Switching Strategy - 终极混合策略
======================================================

基于V3(震荡市专家)和V7(趋势市专家)的深度反思，构建的终极版策略。

核心设计原则:
1. 构建一个元模型(Meta-Model)来判断当前市场状态(Trending vs. Ranging)。
2. 在Trending状态下，调用V7的趋势跟踪引擎。
3. 在Ranging状态下，调用V3的震荡市引擎。
4. 旨在实现两种策略的优势互补，达成1+1>2的效果。

作者：基于V1-V7完整演进的最终思考
日期：2025年
版本：V8.0 (Regime-Switching Hybrid)
"""

import warnings
warnings.filterwarnings('ignore')

import pandas as pd
import numpy as np
import yfinance as yf
from datetime import datetime
import matplotlib.pyplot as plt
from typing import Dict, Tuple
from sklearn.preprocessing import StandardScaler
from hmmlearn.hmm import GaussianHMM

# 中文字体配置
plt.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'SimHei', 'Heiti TC', 'PingFang SC', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

# --- 策略引擎定义 ---

class V3_Ranging_Engine:
    """V3震荡市引擎"""
    def __init__(self):
        self.scaler = StandardScaler()
        self.model = None
        self.state_mapping = {}
        self.features = [
            'log_return', 'volatility', 'dist_from_ma20', 'dist_from_ma200',
            'momentum_ratio', 'rsi', 'bb_position', 'nvda_qqq_ratio', 'vix_signal'
        ]

    def train(self, features_data: pd.DataFrame):
        clean_data = features_data[self.features].dropna()
        if len(clean_data) < 200: raise ValueError(f"V3引擎训练数据不足: {len(clean_data)}")
        X_scaled = self.scaler.fit_transform(clean_data)
        self.model = GaussianHMM(n_components=3, covariance_type="spherical", n_iter=200, random_state=42, tol=1e-2, init_params='st', params='stmc')
        self.model.fit(X_scaled)
        states = self.model.predict(X_scaled)
        state_analysis = {}
        for i in range(3):
            mask = (states == i)
            if mask.sum() > 0:
                state_analysis[i] = {'mean_return': clean_data.loc[mask, 'log_return'].mean()}
        sorted_states = sorted(state_analysis.items(), key=lambda x: x[1]['mean_return'])
        self.state_mapping = {sorted_states[0][0]: "Bear", sorted_states[1][0]: "Neutral", sorted_states[2][0]: "Bull"}

    def predict_state(self, features_data: pd.DataFrame) -> pd.Series:
        clean_data = features_data[self.features].dropna()
        if clean_data.empty: return pd.Series(dtype=str)
        X_scaled = self.scaler.transform(clean_data)
        raw_states = self.model.predict(X_scaled)
        return pd.Series(raw_states, index=clean_data.index).map(self.state_mapping)

    def calculate_position(self, state: str, features: Dict) -> float:
        base_pos = {'Bull': 1.0, 'Neutral': 0.6, 'Bear': 0.0}.get(state, 0.0)
        return max(0.0, min(1.0, base_pos)) # V3使用简化仓位

class V7_Trend_Engine:
    """V7趋势市引擎"""
    def __init__(self):
        self.scaler = StandardScaler()
        self.model = None
        self.state_mapping = {}
        self.features = [
            'log_return', 'volatility', 'dist_from_ma20', 'dist_from_ma200',
            'momentum_ratio', 'rsi', 'bb_position', 'nvda_qqq_ratio', 'vix_signal',
            'dist_from_ma50', 'momentum_5d', 'trend_strength', 'breakout_20',
            'price_rank_60', 'momentum_acceleration', 'vix_regime', 'pin_bar_score'
        ]

    def train(self, features_data: pd.DataFrame):
        clean_data = features_data[self.features].dropna()
        if len(clean_data) < 200: raise ValueError(f"V7引擎训练数据不足: {len(clean_data)}")
        X_scaled = self.scaler.fit_transform(clean_data)
        self.model = GaussianHMM(n_components=3, covariance_type="spherical", n_iter=200, random_state=42, tol=1e-2, init_params='st', params='stmc')
        self.model.fit(X_scaled)
        states = self.model.predict(X_scaled)
        state_analysis = {}
        for i in range(3):
            mask = (states == i)
            if mask.sum() > 0:
                state_data = clean_data.loc[mask]
                state_analysis[i] = {
                    'mean_return': state_data['log_return'].mean(),
                    'trend_strength': state_data['trend_strength'].mean(),
                    'breakout_ratio': state_data['breakout_20'].mean(),
                    'ma200_dist': state_data['dist_from_ma200'].mean(),
                }
        def state_score(analysis): return (analysis['mean_return'] * 0.6 + analysis['trend_strength'] * 0.2 + analysis['breakout_ratio'] * 0.1 + max(0, analysis['ma200_dist']) * 0.1)
        sorted_states = sorted(state_analysis.items(), key=lambda x: state_score(x[1]))
        self.state_mapping = {sorted_states[0][0]: "Bear", sorted_states[1][0]: "Neutral", sorted_states[2][0]: "Bull"}

    def predict_state(self, features_data: pd.DataFrame) -> pd.Series:
        clean_data = features_data[self.features].dropna()
        if clean_data.empty: return pd.Series(dtype=str)
        X_scaled = self.scaler.transform(clean_data)
        raw_states = self.model.predict(X_scaled)
        return pd.Series(raw_states, index=clean_data.index).map(self.state_mapping)

    def calculate_position(self, state: str, features: Dict) -> float:
        base_pos = {'Bull': 1.0, 'Neutral': 0.6, 'Bear': 0.0}.get(state, 0.0)
        tech_adjustment = min(0.1, (features.get('nvda_qqq_ratio', 1.0) - 1.0) * 1.0) if features.get('nvda_qqq_ratio', 1.0) > 1.1 else 0.0
        trend_adjustment = min(0.05, features.get('dist_from_ma200', 0.0) * 0.25) if state == 'Bull' and 0.05 <= features.get('dist_from_ma200', 0.0) <= 0.2 else 0.0
        vix_penalty = min(base_pos * 0.2, features.get('vix_signal', 0.0) * 0.1) if features.get('vix_signal', 0.0) > 0.3 else 0.0
        trend_boost = 0.05 if state in ['Bull', 'Neutral'] and features.get('trend_strength', 0.0) >= 2 else 0.0
        breakout_boost = min(0.05, features.get('breakout_20', 0) * 0.025) if state == 'Bull' and features.get('breakout_20', 0) > 0 else 0.0
        momentum_boost = min(0.03, (features.get('momentum_acceleration', 1.0) - 1.0) * 0.1) if state == 'Bull' and features.get('momentum_acceleration', 1.0) > 1.1 else 0.0
        final_position = base_pos + tech_adjustment + trend_adjustment + trend_boost + breakout_boost + momentum_boost - vix_penalty
        return max(0.0, min(1.0, final_position))

# --- 主策略 ---

class NVDAHMMV8Strategy:
    """NVDA HMM V8 状态切换混合策略"""

    def __init__(self, start_date="2019-01-01", end_date=None, transaction_cost=0.001):
        self.start_date = start_date
        self.end_date = end_date or datetime.now().strftime('%Y-%m-%d')
        self.transaction_cost = transaction_cost
        self.v3_engine = V3_Ranging_Engine()
        self.v7_engine = V7_Trend_Engine()

    def _calculate_adx(self, high, low, close, window):
        plus_dm = high.diff()
        minus_dm = low.diff()
        plus_dm[plus_dm < 0] = 0
        minus_dm[minus_dm > 0] = 0
        
        tr1 = pd.DataFrame(high - low)
        tr2 = pd.DataFrame(abs(high - close.shift(1)))
        tr3 = pd.DataFrame(abs(low - close.shift(1)))
        frames = [tr1, tr2, tr3]
        tr = pd.concat(frames, axis = 1, join = 'inner').max(axis = 1)
        atr = tr.rolling(window).mean()
        
        plus_di = 100 * (plus_dm.ewm(alpha = 1/window).mean() / atr)
        minus_di = abs(100 * (minus_dm.ewm(alpha = 1/window).mean() / atr))
        dx = (abs(plus_di - minus_di) / abs(plus_di + minus_di)) * 100
        adx = ((dx.shift(1) * (window - 1)) + dx) / window
        return adx.fillna(0)

    def download_and_prepare_data(self) -> pd.DataFrame:
        print("📊 下载V8所需全部数据...")
        symbols = ["NVDA", "QQQ", "^VIX"]
        data = yf.download(symbols, start=self.start_date, end=self.end_date, progress=False)
        if isinstance(data.columns, pd.MultiIndex): data.columns = data.columns.to_series().apply(lambda x: f"{x[0]}_{x[1]}")
        print("✅ 数据下载完成")

        print("🔧 V8全特征工程...")
        result = data.copy()
        result['price'] = result['Close_NVDA']
        result['log_return'] = np.log(result['price'] / result['price'].shift(1))
        result['volatility'] = result['log_return'].rolling(20).std()

        # V3/V7 通用特征
        for window in [20, 50, 200]:
            result[f'ma_{window}'] = result['price'].rolling(window).mean()
            result[f'dist_from_ma{window}'] = (result['price'] - result[f'ma_{window}']) / result[f'ma_{window}']
        result['momentum_ratio'] = result['price'] / result['price'].shift(20)
        delta = result['price'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(14).mean(); loss = (-delta.where(delta < 0, 0)).rolling(14).mean()
        rs = gain / loss; result['rsi'] = 100 - (100 / (1 + rs))
        bb_mid = result['price'].rolling(20).mean(); bb_std = result['price'].rolling(20).std()
        result['bb_position'] = (result['price'] - (bb_mid - 2 * bb_std)) / (4 * bb_std)
        result['nvda_qqq_ratio'] = result['Close_NVDA'] / result['Close_QQQ']
        vix_ma = result['Close_^VIX'].rolling(20).mean(); result['vix_signal'] = result['Close_^VIX'] / vix_ma - 1

        # V7 专属特征
        result['momentum_5d'] = result['price'] / result['price'].shift(5)
        result['trend_strength'] = (result['ma_20'] > result['ma_50']).astype(int) + (result['ma_50'] > result['ma_200']).astype(int)
        result['breakout_20'] = (result['price'] > result['price'].rolling(20).max().shift(1)).astype(int)
        result['price_rank_60'] = result['price'].rolling(60).rank(pct=True)
        result['momentum_acceleration'] = result['momentum_5d'] / result['momentum_5d'].shift(5)
        result['vix_regime'] = (result['Close_^VIX'] < 25).astype(int)
        open_p, high_p, low_p, close_p = result['Open_NVDA'], result['High_NVDA'], result['Low_NVDA'], result['Close_NVDA']
        body = abs(close_p - open_p); total_range = high_p - low_p; total_range[total_range == 0] = 1e-6
        upper_wick = high_p - np.maximum(open_p, close_p); lower_wick = np.minimum(open_p, close_p) - low_p
        bullish_pin = (lower_wick / total_range > 0.6) & (body / total_range < 0.2)
        bearish_pin = (upper_wick / total_range > 0.6) & (body / total_range < 0.2)
        result['pin_bar_score'] = np.where(bullish_pin, 1, np.where(bearish_pin, -1, 0))

        # V8 新增状态判断特征
        print("   计算市场状态(Regime)特征...")
        result['adx'] = self._calculate_adx(result['High_NVDA'], result['Low_NVDA'], result['Close_NVDA'], 14)
        result['vix_level'] = result['Close_^VIX']
        result['regime'] = np.where((result['adx'] > 25) & (result['vix_level'] > 20), "Trending", "Ranging")
        print("✅ V8全特征工程完成")
        return result

    def run_backtest(self) -> Dict:
        print("🚀 开始NVDA HMM V8混合策略滚动回测...")
        feature_data = self.download_and_prepare_data()
        backtest_start = pd.to_datetime("2021-01-01")
        backtest_end = pd.to_datetime(self.end_date)
        all_positions = []

        for year in range(backtest_start.year, backtest_end.year + 1):
            train_end_date = f"{year-1}-12-31"
            predict_start_date = f"{year}-01-01"
            predict_end_date = f"{year}-12-31"
            print(f"\n训练年度 {year} (数据至 {train_end_date})...")
            train_df = feature_data.loc[:train_end_date]
            predict_df = feature_data.loc[predict_start_date:predict_end_date]
            if len(train_df) < 252 or len(predict_df) == 0: continue

            print("   训练V3(震荡)和V7(趋势)双引擎...")
            self.v3_engine.train(train_df)
            self.v7_engine.train(train_df)

            positions = pd.Series(index=predict_df.index, dtype=float)
            v3_states = self.v3_engine.predict_state(predict_df)
            v7_states = self.v7_engine.predict_state(predict_df)

            for date, row in predict_df.iterrows():
                regime = row['regime']
                features_dict = row.to_dict()
                if regime == "Trending":
                    state = v7_states.get(date)
                    if state: positions[date] = self.v7_engine.calculate_position(state, features_dict)
                else: # Ranging
                    state = v3_states.get(date)
                    if state: positions[date] = self.v3_engine.calculate_position(state, features_dict)
            
            all_positions.append(positions.ffill())

        if not all_positions: raise ValueError("无法生成任何交易信号")
            
        final_positions = pd.concat(all_positions)
        backtest_results = feature_data.join(final_positions.to_frame('position'), how='right')
        backtest_results['position'].fillna(0, inplace=True)

        backtest_results['trade'] = backtest_results['position'].diff().fillna(0).abs()
        backtest_results['strategy_return'] = (backtest_results['position'].shift(1).fillna(0) * backtest_results['log_return'])
        backtest_results['strategy_return'] -= backtest_results['trade'] * self.transaction_cost
        
        return self._calculate_performance_metrics(backtest_results)

    def _calculate_performance_metrics(self, results: pd.DataFrame) -> Dict:
        if results.empty: return {}
        results['strategy_cumulative'] = np.exp(results['strategy_return'].cumsum())
        results['benchmark_cumulative'] = np.exp(results['log_return'].cumsum())
        days = 252
        annual_return = np.exp(results['strategy_return'].mean() * days) - 1
        annual_vol = results['strategy_return'].std() * np.sqrt(days)
        sharpe = annual_return / annual_vol if annual_vol != 0 else 0
        bench_annual_return = np.exp(results['log_return'].mean() * days) - 1
        running_max = results['strategy_cumulative'].cummax()
        drawdown = (results['strategy_cumulative'] - running_max) / running_max
        calmar = annual_return / abs(drawdown.min()) if drawdown.min() != 0 else 0
        return {
            'strategy_annual_return': annual_return, 'benchmark_annual_return': bench_annual_return,
            'strategy_sharpe': sharpe, 'strategy_max_drawdown': drawdown.min(),
            'win_rate': (results['strategy_return'] > 0).mean(),
            'total_trades': (results['trade'] > 0).sum(), 'calmar_ratio': calmar,
            'results_data': results
        }

    def display_results(self, metrics: Dict):
        if not metrics: return
        print("\n" + "="*80)
        print("📈 NVDA HMM V8 Regime-Switching (滚动评估版) 回测结果")
        print("="*80)
        print("回测期间: " + str(metrics['results_data'].index[0].date()) + " 至 " + str(metrics['results_data'].index[-1].date()))
        print("{:<25} | {:<15}".format('指标', 'V8混合策略'))
        print("-"*80)
        print("{:<25} | {:<15.2%}".format('年化收益率', metrics['strategy_annual_return']))
        print("{:<25} | {:<15.2f}".format('夏普比率', metrics['strategy_sharpe']))
        print("{:<25} | {:<15.2f}".format('Calmar比率', metrics['calmar_ratio']))
        print("{:<25} | {:<15.2%}".format('最大回撤', metrics['strategy_max_drawdown']))
        print("{:<25} | {:<15.2%}".format('胜率', metrics['win_rate']))
        print("{:<25} | {:<15.0f}".format('总交易次数', metrics['total_trades']))
        print("="*80)

def main():
    """主程序"""
    print("🚀 NVDA HMM V8 Regime-Switching Strategy")
    print("=" * 60)
    try:
        strategy = NVDAHMMV8Strategy()
        metrics = strategy.run_backtest()
        strategy.display_results(metrics)
    except Exception as e:
        print(f"❌ V8策略运行失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
