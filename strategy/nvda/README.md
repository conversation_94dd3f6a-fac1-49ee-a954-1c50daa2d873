# 🚀 NVDA HMM 策略演进分析 (V1-V7)

本文档记录了针对NVDA的HMM（隐马尔可夫模型）量化交易策略，从V1基础版到V7最终版的完整演进、测试与分析过程。所有策略均在统一的滚动回测（Walk-Forward）框架下进行评估，以确保结果的公平性和可靠性。

---

## 🏆 最终性能总览：冠军加冕

下表汇总了所有核心版本在统一滚动回测标准下的性能指标。**V7 Pinbar版**被证明是综合表现最优的冠军策略。

| 策略版本 | 年化收益率 | **夏普比率** (越高越好) | **最大回撤** (越小越好) | Calmar比率 | 备注 |
| :--- | :--- | :--- | :--- | :--- | :--- |
| V1 基础版 | 42.94% | 1.44 | -23.82% | 1.80 | 良好的性能基准 |
| V2 优化版 | 43.40% | 1.11 | -46.12% | 0.94 | 失败的复杂化尝试 |
| V3 精细版 | 57.44% | 1.55 | -51.62% | 1.11 | 收益提升，但风控恶化 |
| V5 (原V6) | 59.80% | 1.97 | -27.85% | 2.15 | V3的成功继承者 |
| V6 终极版 | 65.76% | 2.54 | -14.85% | 4.43 | 接近完美的平衡 |
| **V7 Pinbar版** | **76.81%** | **2.81** | **-14.54%** | **5.28** | **🏆 总冠军** |
| V7.1 (切换模型) | 58.22% | 1.92 | -27.85% | 2.09 | 未能超越V7的混合策略 |
| V7.2 (专家特征) | 64.31% | 2.25 | -26.01% | 2.47 | 未能超越V7的特征增强 |

*注：V4（过度工程化）、V9（失败的融合）版本由于其探索的失败性质，已从最终策略库中移除。*

---

## 👣 策略的优化路径与最终教训：大道至简

整个项目的演进揭示了一条从探索，到迷失，再回归并最终升华的清晰路径。

*   **探索与崛起 (V1 → V3 → V5)**：我们找到了一个简洁、稳健的策略核心，证明了HMM模型在趋势捕捉上的有效性。

*   **迷失于复杂 (V2, V4)**：我们见证了盲目增加特征（V2）和过度工程化（V4）所带来的灾难。这是策略研发中最常见的陷阱。

*   **到达巅峰 (V6 → V7)**：我们通过在一个稳固的核心上，增加一个高质量、高相关性的**正交特征（Pin Bar）**，最终达到了性能的巅峰。V7的成功在于**专注**和**精要**。

*   **最终的教训 (V7.1, V7.2)**：我们最后的两次尝试（V7.1的混合引擎，V7.2的专家特征）最终证明，试图通过增加更多复杂性来“锦上添花”，反而会破坏已有的优秀平衡，导致性能下降。这为我们提供了最宝贵的教训：**不要对一个已经足够优秀的策略进行过度优化**。

### 盈利模式：趋势跟踪

值得注意的是，最优的V7策略胜率极低（约22%），但盈亏比极高。这是一个典型的**趋势跟踪**策略的特征。它不追求频繁的小胜，而是通过放弃大部分模糊不清的机会，换取在抓住大的趋势性行情时获得巨额回报。**低胜率和高夏普率是其成熟的标志，而非矛盾。**

---

## 👑 最优版本：V7 Pinbar 策略

经过全面、公平的滚动回测评估，**`nvda_hmm_v7_pinbar.py` 是当之无愧的最优版本**。

它不仅实现了最高的年化收益率，更重要的是，它在所有版本中拥有**最高的夏普比率**和**几乎最低的最大回撤**。这标志着它在盈利能力和风险控制之间取得了近乎完美的平衡，是所有版本中最稳健、最高效的策略。

## 🚀 如何运行

所有脚本均已重构为可以直接运行的滚动回测模式。要运行最优版本或查看其它版本的表现，请执行：

```bash
# 运行总冠军策略
python strategy/nvda/nvda_hmm_v7_pinbar.py

# 运行V7.1（切换模型）
python strategy/nvda/nvda_hmm_v7.1.py

# 运行V7.2（专家特征模型）
python strategy/nvda/nvda_hmm_v7.2.py
```

---

**免责声明**: 本策略及文档仅供学习研究使用，不构成任何投资建议。