#!/usr/bin/env python3
"""
NVDA HMM V10 Fused Expert Strategy - V7框架的专家特征增强版
================================================================

基于V9的失败反思，验证一个核心假设：V9的“专家特征”本身是否有价值？
本策略将V9的新增特征（Hurst, MACD, Ichimoku）应用在V7成功的
三状态趋势跟踪框架上，以进行控制变量测试。

核心优化:
1. 继承V7的全部成功逻辑（三状态HMM, 趋势评分，动态仓位）。
2. 将V9的三个专家特征融入V7的特征矩阵。

作者：基于V1-V9完整演进的最终测试
日期：2025年
版本：V10.0 (Enriched Trend)
"""

import warnings
warnings.filterwarnings('ignore')

import pandas as pd
import numpy as np
import yfinance as yf
from datetime import datetime
import matplotlib.pyplot as plt
from typing import Dict, Tuple
from sklearn.preprocessing import StandardScaler
from hmmlearn.hmm import GaussianHMM

# 中文字体配置
plt.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'SimHei', 'Heiti TC', 'PingFang SC', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class NVDAHMMV10Strategy:
    """NVDA HMM V10 专家特征增强策略"""

    def __init__(self, start_date="2019-01-01", end_date=None, transaction_cost=0.001):
        self.start_date = start_date
        self.end_date = end_date or datetime.now().strftime('%Y-%m-%d')
        self.transaction_cost = transaction_cost
        self.scaler = StandardScaler()
        self.model = None
        self.state_mapping = {}
        
        # V7特征 + V9专家特征
        self.features = [
            'log_return', 'volatility', 'dist_from_ma200', 'rsi', 'bb_position',
            'nvda_qqq_ratio', 'vix_signal', 'trend_strength', 'breakout_20',
            'pin_bar_score', 'adx', 'bb_width', 'hurst', 'macd_hist', 'price_vs_cloud'
        ]

    def _calculate_adx(self, high, low, close, window):
        plus_dm = high.diff(); minus_dm = low.diff()
        plus_dm[plus_dm < 0] = 0; minus_dm[minus_dm > 0] = 0
        tr = pd.concat([high - low, abs(high - close.shift(1)), abs(low - close.shift(1))], axis=1).max(axis=1)
        atr = tr.rolling(window).mean()
        plus_di = 100 * (plus_dm.ewm(alpha=1/window).mean() / atr)
        minus_di = abs(100 * (minus_dm.ewm(alpha=1/window).mean() / atr))
        dx = (abs(plus_di - minus_di) / abs(plus_di + minus_di)) * 100
        adx = ((dx.shift(1) * (window - 1)) + dx) / window
        return adx.fillna(20)

    def _calculate_hurst(self, series, max_lag=100):
        lags = range(2, max_lag)
        tau = [np.sqrt(np.std(np.subtract(series[lag:], series[:-lag]))) for lag in lags]
        poly = np.polyfit(np.log(lags), np.log(tau), 1)
        return poly[0] * 2.0

    def download_and_prepare_data(self) -> pd.DataFrame:
        print("📊 下载V10所需全部数据...")
        symbols = ["NVDA", "QQQ", "^VIX"]
        data = yf.download(symbols, start=self.start_date, end=self.end_date, progress=False)
        if isinstance(data.columns, pd.MultiIndex): data.columns = data.columns.to_series().apply(lambda x: f"{x[0]}_{x[1]}")
        print("✅ 数据下载完成")

        print("🔧 V10专家特征工程...")
        result = data.copy()
        result['price'] = result['Close_NVDA']
        result['log_return'] = np.log(result['price'] / result['price'].shift(1))
        result['volatility'] = result['log_return'].rolling(20).std()

        # 基础特征
        for window in [20, 50, 200]: result[f'ma_{window}'] = result['price'].rolling(window).mean()
        result['dist_from_ma200'] = (result['price'] - result['ma_200']) / result['ma_200']
        delta = result['price'].diff(); gain = (delta.where(delta > 0, 0)).rolling(14).mean(); loss = (-delta.where(delta < 0, 0)).rolling(14).mean()
        rs = gain / loss; result['rsi'] = 100 - (100 / (1 + rs))
        bb_mid = result['price'].rolling(20).mean(); bb_std = result['price'].rolling(20).std()
        result['bb_position'] = (result['price'] - (bb_mid - 2 * bb_std)) / (4 * bb_std)
        result['nvda_qqq_ratio'] = result['Close_NVDA'] / result['Close_QQQ']
        vix_ma = result['Close_^VIX'].rolling(20).mean(); result['vix_signal'] = result['Close_^VIX'] / vix_ma - 1
        result['trend_strength'] = (result['ma_20'] > result['ma_50']).astype(int) + (result['ma_50'] > result['ma_200']).astype(int)
        result['breakout_20'] = (result['price'] > result['price'].rolling(20).max().shift(1)).astype(int)
        open_p, high_p, low_p, close_p = result['Open_NVDA'], result['High_NVDA'], result['Low_NVDA'], result['Close_NVDA']
        body = abs(close_p - open_p); total_range = high_p - low_p; total_range[total_range == 0] = 1e-6
        upper_wick = high_p - np.maximum(open_p, close_p); lower_wick = np.minimum(open_p, close_p) - low_p
        bullish_pin = (lower_wick / total_range > 0.6) & (body / total_range < 0.2); bearish_pin = (upper_wick / total_range > 0.6) & (body / total_range < 0.2)
        result['pin_bar_score'] = np.where(bullish_pin, 1, np.where(bearish_pin, -1, 0))
        result['adx'] = self._calculate_adx(result['High_NVDA'], result['Low_NVDA'], result['Close_NVDA'], 14)
        result['bb_width'] = (4 * bb_std) / bb_mid

        # V9/V10 新增专家特征
        print("   计算V10新增专家特征(Hurst, MACD, Ichimoku)...")
        result['hurst'] = result['log_return'].rolling(100).apply(self._calculate_hurst, raw=False).fillna(0.5)
        exp12 = result['price'].ewm(span=12, adjust=False).mean(); exp26 = result['price'].ewm(span=26, adjust=False).mean()
        macd = exp12 - exp26; macd_signal = macd.ewm(span=9, adjust=False).mean()
        result['macd_hist'] = macd - macd_signal
        high_9 = result['High_NVDA'].rolling(9).max(); low_9 = result['Low_NVDA'].rolling(9).min()
        result['tenkan_sen'] = (high_9 + low_9) / 2
        high_26 = result['High_NVDA'].rolling(26).max(); low_26 = result['Low_NVDA'].rolling(26).min()
        result['kijun_sen'] = (high_26 + low_26) / 2
        result['senkou_span_a'] = ((result['tenkan_sen'] + result['kijun_sen']) / 2).shift(26)
        high_52 = result['High_NVDA'].rolling(52).max(); low_52 = result['Low_NVDA'].rolling(52).min()
        result['senkou_span_b'] = ((high_52 + low_52) / 2).shift(26)
        result['price_vs_cloud'] = np.where(result['price'] > result['senkou_span_a'], 1, np.where(result['price'] < result['senkou_span_b'], -1, 0))

        print("✅ V10专家特征工程完成")
        return result

    def train_hmm(self, features_data: pd.DataFrame):
        """训练三状态HMM模型"""
        clean_data = features_data[self.features].dropna()
        if len(clean_data) < 252: raise ValueError(f"训练数据不足: {len(clean_data)}")
        X_scaled = self.scaler.fit_transform(clean_data)
        self.model = GaussianHMM(n_components=3, covariance_type="spherical", n_iter=200, random_state=42, tol=1e-2, init_params='st', params='stmc')
        self.model.fit(X_scaled)
        states = self.model.predict(X_scaled)
        
        # V7 状态映射逻辑
        state_analysis = {}
        for i in range(3):
            mask = (states == i)
            if mask.sum() > 0:
                sd = clean_data.loc[mask]
                state_analysis[i] = {'mean_return': sd['log_return'].mean(), 'trend_strength': sd['trend_strength'].mean(), 'breakout_ratio': sd['breakout_20'].mean(), 'ma200_dist': sd['dist_from_ma200'].mean()}
        def state_score(analysis): return (analysis['mean_return'] * 0.6 + analysis['trend_strength'] * 0.2 + analysis['breakout_ratio'] * 0.1 + max(0, analysis['ma200_dist']) * 0.1)
        sorted_states = sorted(state_analysis.items(), key=lambda x: state_score(x[1]))
        self.state_mapping = {sorted_states[0][0]: "Bear", sorted_states[1][0]: "Neutral", sorted_states[2][0]: "Bull"}
        print("三状态映射完成:", self.state_mapping)

    def calculate_position(self, state: str, features: Dict) -> float:
        """V7风格仓位计算"""
        base_pos = {'Bull': 1.0, 'Neutral': 0.6, 'Bear': 0.0}.get(state, 0.0)
        tech_adjustment = min(0.1, (features.get('nvda_qqq_ratio', 1.0) - 1.0) * 1.0) if features.get('nvda_qqq_ratio', 1.0) > 1.1 else 0.0
        trend_adjustment = min(0.05, features.get('dist_from_ma200', 0.0) * 0.25) if state == 'Bull' and 0.05 <= features.get('dist_from_ma200', 0.0) <= 0.2 else 0.0
        vix_penalty = min(base_pos * 0.2, features.get('vix_signal', 0.0) * 0.1) if features.get('vix_signal', 0.0) > 0.3 else 0.0
        trend_boost = 0.05 if state in ['Bull', 'Neutral'] and features.get('trend_strength', 0.0) >= 2 else 0.0
        breakout_boost = min(0.05, features.get('breakout_20', 0) * 0.025) if state == 'Bull' and features.get('breakout_20', 0) > 0 else 0.0
        momentum_boost = min(0.03, (features.get('momentum_acceleration', 1.0) - 1.0) * 0.1) if state == 'Bull' and features.get('momentum_acceleration', 1.0) > 1.1 else 0.0
        final_position = base_pos + tech_adjustment + trend_adjustment + trend_boost + breakout_boost + momentum_boost - vix_penalty
        return max(0.0, min(1.0, final_position))

    def run_backtest(self) -> Dict:
        print("🚀 开始NVDA HMM V10专家特征策略滚动回测...")
        feature_data = self.download_and_prepare_data()
        backtest_start = pd.to_datetime("2021-01-01"); backtest_end = pd.to_datetime(self.end_date)
        all_positions = []
        for year in range(backtest_start.year, backtest_end.year + 1):
            train_end_date = f"{year-1}-12-31"; predict_start_date = f"{year}-01-01"; predict_end_date = f"{year}-12-31"
            print(f"\n训练年度 {year} (数据至 {train_end_date})...")
            train_df = feature_data.loc[:train_end_date]; predict_df = feature_data.loc[predict_start_date:predict_end_date]
            if len(train_df) < 252 or len(predict_df) == 0: continue
            self.train_hmm(train_df)
            states = self.predict_states(predict_df)
            positions = pd.Series(index=predict_df.index, dtype=float)
            for date, row in predict_df.iterrows():
                state = states.get(date)
                if state: positions[date] = self.calculate_position(state, row.to_dict())
            all_positions.append(positions.ffill())
        if not all_positions: raise ValueError("无信号")
        final_positions = pd.concat(all_positions)
        backtest_results = feature_data.join(final_positions.to_frame('position'), how='right')
        backtest_results['position'].fillna(0, inplace=True)
        backtest_results['trade'] = backtest_results['position'].diff().fillna(0).abs()
        backtest_results['strategy_return'] = (backtest_results['position'].shift(1).fillna(0) * backtest_results['log_return'])
        backtest_results['strategy_return'] -= backtest_results['trade'] * self.transaction_cost
        return self._calculate_performance_metrics(backtest_results)

    def predict_states(self, features_data: pd.DataFrame) -> pd.Series:
        clean_data = features_data[self.features].dropna()
        if clean_data.empty: return pd.Series(dtype=str)
        X_scaled = self.scaler.transform(clean_data)
        raw_states = self.model.predict(X_scaled)
        return pd.Series(raw_states, index=clean_data.index).map(self.state_mapping)

    def _calculate_performance_metrics(self, results: pd.DataFrame) -> Dict:
        if results.empty: return {}
        results['strategy_cumulative'] = np.exp(results['strategy_return'].cumsum())
        days = 252
        annual_return = np.exp(results['strategy_return'].mean() * days) - 1
        annual_vol = results['strategy_return'].std() * np.sqrt(days)
        sharpe = annual_return / annual_vol if annual_vol != 0 else 0
        running_max = results['strategy_cumulative'].cummax()
        drawdown = (results['strategy_cumulative'] - running_max) / running_max
        calmar = annual_return / abs(drawdown.min()) if drawdown.min() != 0 else 0
        return {
            'strategy_annual_return': annual_return, 'strategy_sharpe': sharpe, 
            'strategy_max_drawdown': drawdown.min(), 'win_rate': (results['strategy_return'] > 0).mean(),
            'total_trades': (results['trade'] > 0).sum(), 'calmar_ratio': calmar,
            'results_data': results
        }

    def display_results(self, metrics: Dict):
        if not metrics: return
        print("\n" + "="*80)
        print("📈 NVDA HMM V10 Enriched Trend (滚动评估版) 回测结果")
        print("="*80)
        print("回测期间: " + str(metrics['results_data'].index[0].date()) + " 至 " + str(metrics['results_data'].index[-1].date()))
        print("{:<25} | {:<15}".format('指标', 'V10专家特征策略'))
        print("-"*80)
        print("{:<25} | {:<15.2%}".format('年化收益率', metrics['strategy_annual_return']))
        print("{:<25} | {:<15.2f}".format('夏普比率', metrics['strategy_sharpe']))
        print("{:<25} | {:<15.2f}".format('Calmar比率', metrics['calmar_ratio']))
        print("{:<25} | {:<15.2%}".format('最大回撤', metrics['strategy_max_drawdown']))
        print("{:<25} | {:<15.2%}".format('胜率', metrics['win_rate']))
        print("{:<25} | {:<15.0f}".format('总交易次数', metrics['total_trades']))
        print("="*80)

def main():
    """主程序"""
    print("🚀 NVDA HMM V10 Enriched Trend Strategy")
    print("=" * 60)
    try:
        strategy = NVDAHMMV10Strategy()
        metrics = strategy.run_backtest()
        strategy.display_results(metrics)
    except Exception as e:
        print(f"❌ V10策略运行失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
