#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HMM趋势状态检测分析
对比原始波动状态检测与趋势状态检测的差异
"""

import warnings
warnings.filterwarnings('ignore')

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from datetime import datetime, timedelta
import yfinance as yf
from hmmlearn.hmm import GaussianHMM
from sklearn.cluster import AgglomerativeClustering
from sklearn.mixture import GaussianMixture
import plotly.graph_objects as go
from plotly.subplots import make_subplots

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

# ----------------------------- #
# 1. 数据获取
# ----------------------------- #
TICKER = "^GSPC"  # S&P 500指数
START_DATE = "2000-01-01"
END_DATE = datetime.now().strftime('%Y-%m-%d')

print(f"正在获取 {TICKER} 数据 ({START_DATE} 到 {END_DATE})...")
prices = yf.download(TICKER, start=START_DATE, end=END_DATE)[['Close']]
prices.columns = [TICKER]
prices.columns.name = TICKER
print(f"数据获取完成，共 {len(prices)} 个交易日")

# ----------------------------- #
# 2. 特征工程 - 多种特征对比
# ----------------------------- #
def prepare_volatility_features(prices_df: pd.DataFrame, ma: int = 7):
    """
    原始方法：基于波动的特征（7日移动平均的对数回报率）
    """
    inst = prices_df.columns.name
    prices_df[f'{inst}_ma'] = prices_df[inst].rolling(ma).mean()
    prices_df[f'{inst}_log_return'] = (
        np.log(prices_df[f'{inst}_ma'] / prices_df[f'{inst}_ma'].shift(1))
    )
    prices_df.dropna(inplace=True)
    X = prices_df[[f'{inst}_log_return']].values
    return prices_df, X

def prepare_trend_features(prices_df: pd.DataFrame, short_ma: int = 7, long_ma: int = 30):
    """
    趋势方法：基于趋势的特征组合
    """
    inst = prices_df.columns.name
    
    # 1. 短期和长期移动平均
    prices_df[f'{inst}_ma_short'] = prices_df[inst].rolling(short_ma).mean()
    prices_df[f'{inst}_ma_long'] = prices_df[inst].rolling(long_ma).mean()
    
    # 2. 价格相对于移动平均的位置（趋势强度）
    prices_df[f'{inst}_price_ma_ratio'] = prices_df[inst] / prices_df[f'{inst}_ma_long']
    
    # 3. 移动平均的斜率（趋势方向）
    prices_df[f'{inst}_ma_slope'] = (
        prices_df[f'{inst}_ma_short'] / prices_df[f'{inst}_ma_short'].shift(5) - 1
    )
    
    # 4. 短期与长期移动平均的比值（趋势确认）
    prices_df[f'{inst}_ma_ratio'] = prices_df[f'{inst}_ma_short'] / prices_df[f'{inst}_ma_long']
    
    # 5. 价格动量
    prices_df[f'{inst}_momentum'] = prices_df[inst] / prices_df[inst].shift(10) - 1
    
    prices_df.dropna(inplace=True)
    
    # 组合特征
    feature_cols = [
        f'{inst}_price_ma_ratio',
        f'{inst}_ma_slope', 
        f'{inst}_ma_ratio',
        f'{inst}_momentum'
    ]
    X = prices_df[feature_cols].values
    return prices_df, X

# ----------------------------- #
# 3. RegimeDetection 类
# ----------------------------- #
class RegimeDetection:
    def get_regimes_hmm(self, X, params):
        model = self.initialise_model(GaussianHMM(), params).fit(X)
        return model

    def get_regimes_clustering(self, params):
        return self.initialise_model(AgglomerativeClustering(), params)

    def get_regimes_gmm(self, X, params):
        return self.initialise_model(GaussianMixture(), params).fit(X)

    @staticmethod
    def initialise_model(model, params):
        for k, v in params.items():
            setattr(model, k, v)
        return model

# ----------------------------- #
# 4. 可视化对比函数
# ----------------------------- #
def plot_regime_comparison(states_volatility, states_trend, price_data, title_suffix=""):
    """
    对比波动状态检测与趋势状态检测的结果
    """
    fig, axes = plt.subplots(3, 1, figsize=(15, 12), sharex=True)
    
    # 确保数据长度一致
    min_len = min(len(states_volatility), len(states_trend), len(price_data))
    states_vol = states_volatility[:min_len]
    states_tr = states_trend[:min_len]
    price_slice = price_data.iloc[:min_len]
    
    # 1. 价格走势
    axes[0].plot(price_slice.index, price_slice.iloc[:, 0], 'k-', alpha=0.8, linewidth=1.5)
    axes[0].set_ylabel('价格', fontsize=12)
    axes[0].set_title(f'S&P 500价格走势{title_suffix}', fontsize=14, fontweight='bold')
    axes[0].grid(True, alpha=0.3)
    
    # 2. 波动状态检测（原始方法）
    colors_vol = {0: 'red', 1: 'green'}
    labels_vol = {0: '高波动/崩盘', 1: '正常波动'}
    
    for state in [0, 1]:
        mask = (states_vol == state)
        if np.any(mask):
            state_changes = np.diff(np.concatenate(([False], mask, [False])).astype(int))
            starts = np.where(state_changes == 1)[0]
            ends = np.where(state_changes == -1)[0]
            
            for start, end in zip(starts, ends):
                if start < len(price_slice) and end <= len(price_slice):
                    axes[1].axvspan(price_slice.index[start], price_slice.index[end-1], 
                                   alpha=0.3, color=colors_vol[state], 
                                   label=f'{labels_vol[state]}' if start == starts[0] else "")
    
    axes[1].plot(price_slice.index, price_slice.iloc[:, 0], 'k-', alpha=0.6, linewidth=1)
    axes[1].set_ylabel('价格', fontsize=12)
    axes[1].set_title('波动状态检测（基于对数回报率）', fontsize=12)
    axes[1].grid(True, alpha=0.3)
    
    # 移除重复图例
    handles1, labels1 = axes[1].get_legend_handles_labels()
    by_label1 = dict(zip(labels1, handles1))
    axes[1].legend(by_label1.values(), by_label1.keys(), loc='upper left')
    
    # 3. 趋势状态检测（改进方法）
    colors_trend = {0: 'red', 1: 'green'}
    labels_trend = {0: '下跌趋势', 1: '上涨趋势'}
    
    for state in [0, 1]:
        mask = (states_tr == state)
        if np.any(mask):
            state_changes = np.diff(np.concatenate(([False], mask, [False])).astype(int))
            starts = np.where(state_changes == 1)[0]
            ends = np.where(state_changes == -1)[0]
            
            for start, end in zip(starts, ends):
                if start < len(price_slice) and end <= len(price_slice):
                    axes[2].axvspan(price_slice.index[start], price_slice.index[end-1], 
                                   alpha=0.3, color=colors_trend[state], 
                                   label=f'{labels_trend[state]}' if start == starts[0] else "")
    
    axes[2].plot(price_slice.index, price_slice.iloc[:, 0], 'k-', alpha=0.6, linewidth=1)
    axes[2].set_ylabel('价格', fontsize=12)
    axes[2].set_xlabel('日期', fontsize=12)
    axes[2].set_title('趋势状态检测（基于趋势特征）', fontsize=12)
    axes[2].grid(True, alpha=0.3)
    
    # 移除重复图例
    handles2, labels2 = axes[2].get_legend_handles_labels()
    by_label2 = dict(zip(labels2, handles2))
    axes[2].legend(by_label2.values(), by_label2.keys(), loc='upper left')
    
    # 添加统计信息
    vol_counts = np.bincount(states_vol)
    trend_counts = np.bincount(states_tr)
    
    vol_info = f"高波动: {vol_counts[0]}天 ({vol_counts[0]/len(states_vol)*100:.1f}%)\n正常: {vol_counts[1]}天 ({vol_counts[1]/len(states_vol)*100:.1f}%)"
    trend_info = f"下跌: {trend_counts[0]}天 ({trend_counts[0]/len(states_tr)*100:.1f}%)\n上涨: {trend_counts[1]}天 ({trend_counts[1]/len(states_tr)*100:.1f}%)"
    
    axes[1].text(0.02, 0.98, vol_info, transform=axes[1].transAxes, 
                verticalalignment='top', bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
    axes[2].text(0.02, 0.98, trend_info, transform=axes[2].transAxes, 
                verticalalignment='top', bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
    
    plt.tight_layout()
    plt.show()
    
    return vol_counts, trend_counts

# ----------------------------- #
# 5. 主要分析流程
# ----------------------------- #
print("\n=== 开始特征工程对比分析 ===")

# 准备两种不同的特征
print("\n1. 准备波动特征（原始方法）...")
prices_vol = prices.copy()
prices_vol, X_volatility = prepare_volatility_features(prices_vol, ma=7)

print("2. 准备趋势特征（改进方法）...")
prices_trend = prices.copy()
prices_trend, X_trend = prepare_trend_features(prices_trend, short_ma=7, long_ma=30)

# 确保数据长度一致
min_len = min(len(prices_vol), len(prices_trend))
prices_vol = prices_vol.iloc[:min_len]
prices_trend = prices_trend.iloc[:min_len]
X_volatility = X_volatility[:min_len]
X_trend = X_trend[:min_len]

print(f"数据准备完成，共 {min_len} 个有效交易日")

# ----------------------------- #
# 6. HMM模型训练与对比
# ----------------------------- #
regime_detection = RegimeDetection()
params_hmm = dict(n_components=2, covariance_type='full', random_state=100)

print("\n3. 训练HMM模型...")

# 波动状态检测
print("   - 波动状态HMM训练中...")
hmm_volatility = regime_detection.get_regimes_hmm(X_volatility, params_hmm)
states_volatility = hmm_volatility.predict(X_volatility)

# 趋势状态检测
print("   - 趋势状态HMM训练中...")
hmm_trend = regime_detection.get_regimes_hmm(X_trend, params_hmm)
states_trend = hmm_trend.predict(X_trend)

print("\n=== 模型训练完成 ===")

# ----------------------------- #
# 7. 结果分析与可视化
# ----------------------------- #
print("\n=== 状态检测结果对比 ===")

vol_counts = np.bincount(states_volatility)
trend_counts = np.bincount(states_trend)

print(f"\n波动状态检测结果:")
print(f"  状态0 (高波动/崩盘): {vol_counts[0]} 天 ({vol_counts[0]/len(states_volatility)*100:.1f}%)")
print(f"  状态1 (正常波动): {vol_counts[1]} 天 ({vol_counts[1]/len(states_volatility)*100:.1f}%)")

print(f"\n趋势状态检测结果:")
print(f"  状态0 (下跌趋势): {trend_counts[0]} 天 ({trend_counts[0]/len(states_trend)*100:.1f}%)")
print(f"  状态1 (上涨趋势): {trend_counts[1]} 天 ({trend_counts[1]/len(states_trend)*100:.1f}%)")

print("\n正在生成对比可视化图表...")
vol_counts_plot, trend_counts_plot = plot_regime_comparison(
    states_volatility, states_trend, prices_vol[[TICKER]], 
    title_suffix=f" ({prices_vol.index[0].date()} 至 {prices_vol.index[-1].date()})"
)

# ----------------------------- #
# 8. 特征重要性分析
# ----------------------------- #
print("\n=== 特征分析 ===")

# 分析趋势特征的统计特性
trend_features = ['price_ma_ratio', 'ma_slope', 'ma_ratio', 'momentum']
feature_names = [f'{TICKER}_{feat}' for feat in trend_features]

print("\n趋势特征统计:")
for i, feat_name in enumerate(feature_names):
    if feat_name in prices_trend.columns:
        feat_data = prices_trend[feat_name]
        print(f"  {feat_name}:")
        print(f"    均值: {feat_data.mean():.4f}, 标准差: {feat_data.std():.4f}")
        print(f"    最小值: {feat_data.min():.4f}, 最大值: {feat_data.max():.4f}")

# ----------------------------- #
# 9. 总结与建议
# ----------------------------- #
print("\n" + "="*60)
print("=== 分析总结 ===")
print("\n1. 波动状态检测（原始方法）:")
print("   - 基于7日移动平均的对数回报率")
print("   - 主要识别市场的异常波动期vs正常期")
print(f"   - 异常波动期占比: {vol_counts[0]/len(states_volatility)*100:.1f}%")
print("   - 适合风险管理和危机识别")

print("\n2. 趋势状态检测（改进方法）:")
print("   - 基于多维趋势特征组合")
print("   - 主要识别市场的上涨趋势vs下跌趋势")
print(f"   - 下跌趋势期占比: {trend_counts[0]/len(states_trend)*100:.1f}%")
print("   - 更适合趋势跟踪策略")

print("\n3. 关键发现:")
if vol_counts[0] < trend_counts[0]:
    print("   ✓ 趋势方法识别出更多的市场转换期")
else:
    print("   ✓ 波动方法更保守，主要关注重大危机")

print("\n4. 应用建议:")
print("   - 风险管理: 使用波动状态检测")
print("   - 趋势跟踪: 使用趋势状态检测")
print("   - 组合策略: 结合两种方法的信号")

print("\n=== 分析完成 ===")
print("\n注意: 本分析仅用于学术研究，不构成投资建议")
print("="*60)