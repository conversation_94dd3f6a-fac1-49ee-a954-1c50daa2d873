"""
优化版HMM策略演示
Optimized HMM Strategy Demo

降低交易频率，控制成本，实现正超额收益
"""

import numpy as np
import pandas as pd
import yfinance as yf
from hmmlearn.hmm import GaussianHMM
from sklearn.preprocessing import StandardScaler
import matplotlib.pyplot as plt
import warnings
warnings.filterwarnings('ignore')

class OptimizedHMMStrategy:
    """优化版HMM投资策略"""
    
    def __init__(self, n_components=3, transaction_cost=0.0005, min_holding_days=5):
        self.n_components = n_components
        self.transaction_cost = transaction_cost  # 降低到0.05%
        self.min_holding_days = min_holding_days  # 最小持仓天数
        self.hmm_model = None
        self.scaler = StandardScaler()
        
    def download_data(self, symbol, period="2y"):
        """下载数据"""
        print(f"正在下载 {symbol} 数据...")
        try:
            data = yf.download(symbol, period=period, progress=False)
            if len(data) > 100:
                print(f"✓ {symbol}: {len(data)} 条数据")
                return data
            else:
                print(f"✗ {symbol}: 数据不足")
                return None
        except Exception as e:
            print(f"✗ {symbol}: 下载失败")
            return None
    
    def create_features(self, data):
        """创建技术特征"""
        df = data.copy()
        features = pd.DataFrame(index=df.index)
        
        # 基础特征
        features['return'] = df['Close'].pct_change()
        
        # 多周期动量（减少特征数量）
        for period in [10, 20]:
            features[f'momentum_{period}d'] = df['Close'].pct_change(period)
        
        # 波动率特征
        for window in [10, 20]:
            features[f'volatility_{window}d'] = features['return'].rolling(window).std() * np.sqrt(252)
        
        # RSI（简化版）
        delta = df['Close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / loss
        features['rsi'] = 100 - (100 / (1 + rs))
        
        # 移动平均线
        ma_20 = df['Close'].rolling(20).mean()
        features['ma_20_ratio'] = df['Close'] / ma_20 - 1
        
        # 成交量比率
        features['volume_ratio'] = df['Volume'] / df['Volume'].rolling(20).mean()
        
        # 价格位置（类似布林带位置）
        rolling_max = df['High'].rolling(20).max()
        rolling_min = df['Low'].rolling(20).min()
        features['price_position'] = (df['Close'] - rolling_min) / (rolling_max - rolling_min)
        
        # 清理数据
        features_clean = features.dropna()
        print(f"特征工程: {len(features_clean)} 条记录, {len(features_clean.columns)} 个特征")
        
        return features_clean
    
    def train_hmm(self, features):
        """训练HMM模型"""
        # 使用核心特征
        core_features = [
            'return', 'momentum_10d', 'momentum_20d',
            'volatility_10d', 'volatility_20d',
            'rsi', 'ma_20_ratio', 'volume_ratio', 'price_position'
        ]
        
        available_features = [f for f in core_features if f in features.columns]
        print(f"使用 {len(available_features)} 个特征训练HMM")
        
        X = features[available_features].values
        X_scaled = self.scaler.fit_transform(X)
        
        # 训练HMM
        self.hmm_model = GaussianHMM(
            n_components=self.n_components,
            covariance_type="diag",  # 使用对角协方差减少过拟合
            n_iter=500,              # 减少迭代次数
            random_state=42
        )
        
        self.hmm_model.fit(X_scaled)
        states = self.hmm_model.predict(X_scaled)
        
        print(f"HMM训练完成，收敛分数: {self.hmm_model.score(X_scaled):.2f}")
        
        return states
    
    def analyze_regimes(self, features, states):
        """分析市场环境"""
        analysis = {}
        
        print(f"\n市场环境分析:")
        print("=" * 50)
        
        for regime in range(self.n_components):
            mask = states == regime
            regime_data = features[mask]
            
            if len(regime_data) < 10:
                continue
            
            returns = regime_data['return']
            
            stats = {
                'frequency': len(regime_data) / len(features) * 100,
                'avg_daily_return': returns.mean() * 100,
                'annualized_return': returns.mean() * 252 * 100,
                'volatility': returns.std() * np.sqrt(252) * 100,
                'sharpe_ratio': (returns.mean() / returns.std() * np.sqrt(252)) if returns.std() > 0 else 0,
                'max_drawdown': self._max_drawdown(returns) * 100,
                'win_rate': (returns > 0).mean() * 100
            }
            
            analysis[f'regime_{regime}'] = stats
            
            print(f"\n环境 {regime} ({stats['frequency']:.1f}%):")
            print(f"  年化收益: {stats['annualized_return']:>6.1f}%")
            print(f"  年化波动: {stats['volatility']:>6.1f}%")
            print(f"  夏普比率: {stats['sharpe_ratio']:>6.3f}")
            print(f"  胜率: {stats['win_rate']:>6.1f}%")
        
        return analysis
    
    def _max_drawdown(self, returns):
        """计算最大回撤"""
        cumulative = (1 + returns).cumprod()
        rolling_max = cumulative.expanding().max()
        drawdown = (cumulative - rolling_max) / rolling_max
        return drawdown.min()
    
    def create_optimized_signals(self, features, states, analysis):
        """创建优化的交易信号"""
        signals = pd.DataFrame(index=features.index)
        signals['regime'] = states
        signals['raw_position'] = 0.5
        signals['position'] = 0.5
        
        print(f"\n优化交易策略:")
        print("-" * 40)
        
        # 第一步：基于环境设定目标仓位
        for regime in range(self.n_components):
            if f'regime_{regime}' not in analysis:
                continue
            
            mask = states == regime
            stats = analysis[f'regime_{regime}']
            
            sharpe = stats['sharpe_ratio']
            annual_return = stats['annualized_return']
            
            # 更保守的仓位设定
            if sharpe > 0.8 and annual_return > 8:  # 优质环境
                target_position = 0.8  # 80%仓位
                strategy = "积极配置"
            elif sharpe > 0.3 and annual_return > 0:  # 中性环境
                target_position = 0.5  # 50%仓位
                strategy = "均衡配置"
            else:  # 恶劣环境
                target_position = 0.2  # 20%仓位
                strategy = "保守防御"
            
            signals.loc[mask, 'raw_position'] = target_position
            
            print(f"环境{regime}: {strategy} (目标仓位{target_position*100:.0f}%)")
        
        # 第二步：应用交易优化规则
        signals['position'] = self._apply_trading_rules(signals['raw_position'])
        
        return signals
    
    def _apply_trading_rules(self, raw_positions):
        """应用交易优化规则"""
        optimized_positions = raw_positions.copy()
        
        # 规则1：最小仓位变化阈值
        min_change_threshold = 0.1  # 10%
        
        # 规则2：最小持仓期约束
        last_change_date = None
        
        for i in range(1, len(optimized_positions)):
            current_pos = optimized_positions.iloc[i]
            previous_pos = optimized_positions.iloc[i-1]
            
            # 计算仓位变化
            position_change = abs(current_pos - previous_pos)
            
            # 如果变化小于阈值，保持前一期仓位
            if position_change < min_change_threshold:
                optimized_positions.iloc[i] = previous_pos
            
            # 最小持仓期约束（简化实现）
            elif last_change_date is not None:
                days_since_change = i - last_change_date
                if days_since_change < self.min_holding_days:
                    optimized_positions.iloc[i] = previous_pos
                else:
                    last_change_date = i
            else:
                last_change_date = i
        
        return optimized_positions
    
    def backtest_optimized_strategy(self, features, signals):
        """回测优化策略"""
        print(f"\n=== 优化策略回测 ===")
        
        backtest = pd.DataFrame(index=features.index)
        backtest['return'] = features['return']
        backtest['position'] = signals['position']
        backtest['regime'] = signals['regime']
        
        # 计算实际的仓位变化
        backtest['position_change'] = backtest['position'].diff().abs()
        
        # 只对显著的仓位变化收取交易成本
        significant_changes = backtest['position_change'] > 0.05  # 5%以上变化
        backtest['transaction_cost'] = 0.0
        backtest.loc[significant_changes, 'transaction_cost'] = (
            backtest.loc[significant_changes, 'position_change'] * self.transaction_cost
        )
        
        # 策略收益
        backtest['strategy_return'] = (
            backtest['position'].shift(1) * backtest['return'] - 
            backtest['transaction_cost']
        )
        
        # 累积收益
        backtest['cum_benchmark'] = (1 + backtest['return']).cumprod()
        backtest['cum_strategy'] = (1 + backtest['strategy_return']).cumprod()
        
        # 计算绩效指标
        benchmark_total = (backtest['cum_benchmark'].iloc[-1] - 1) * 100
        strategy_total = (backtest['cum_strategy'].iloc[-1] - 1) * 100
        absolute_excess = strategy_total - benchmark_total
        relative_excess = (backtest['cum_strategy'].iloc[-1] / backtest['cum_benchmark'].iloc[-1] - 1) * 100
        
        strategy_returns = backtest['strategy_return'].dropna()
        benchmark_returns = backtest['return'].dropna()
        
        # 计算实际换手率
        actual_turnover = backtest.loc[significant_changes, 'position_change'].sum() / len(backtest) * 252 * 100
        total_cost = backtest['transaction_cost'].sum() * 100
        
        performance = {
            'benchmark_total_return': benchmark_total,
            'strategy_total_return': strategy_total,
            'absolute_excess_return': absolute_excess,
            'relative_excess_return': relative_excess,
            'strategy_sharpe': (strategy_returns.mean() / strategy_returns.std() * np.sqrt(252)) if strategy_returns.std() > 0 else 0,
            'benchmark_sharpe': (benchmark_returns.mean() / benchmark_returns.std() * np.sqrt(252)) if benchmark_returns.std() > 0 else 0,
            'strategy_volatility': strategy_returns.std() * np.sqrt(252) * 100,
            'benchmark_volatility': benchmark_returns.std() * np.sqrt(252) * 100,
            'strategy_max_drawdown': self._max_drawdown(strategy_returns) * 100,
            'benchmark_max_drawdown': self._max_drawdown(benchmark_returns) * 100,
            'win_rate': (strategy_returns > 0).mean() * 100,
            'average_position': backtest['position'].mean() * 100,
            'actual_turnover_rate': actual_turnover,
            'total_transaction_cost': total_cost,
            'trading_days': len(backtest.loc[significant_changes]),
            'total_days': len(backtest)
        }
        
        # 打印详细结果
        print(f"\n优化后的超额收益:")
        print(f"基准总收益:     {benchmark_total:>8.2f}%")
        print(f"策略总收益:     {strategy_total:>8.2f}%") 
        print(f"绝对超额收益:   {absolute_excess:>8.2f}%")
        print(f"相对超额收益:   {relative_excess:>8.2f}%")
        print(f"实际交易天数:   {performance['trading_days']:>8.0f} / {performance['total_days']}")
        print(f"实际年化换手:   {actual_turnover:>8.1f}%")
        print(f"总交易成本:     {total_cost:>8.3f}%")
        
        return backtest, performance
    
    def visualize_optimized_results(self, backtest, performance, symbol):
        """可视化优化结果"""
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        
        plt.rcParams['font.sans-serif'] = ['Arial', 'DejaVu Sans']
        
        # 1. 累积收益对比
        ax1 = axes[0, 0]
        ax1.plot(backtest.index, backtest['cum_benchmark'], 
                label=f'Buy & Hold ({performance["benchmark_total_return"]:.1f}%)', 
                alpha=0.8, linewidth=2)
        ax1.plot(backtest.index, backtest['cum_strategy'], 
                label=f'Optimized HMM ({performance["strategy_total_return"]:.1f}%)', 
                alpha=0.8, linewidth=2)
        ax1.set_title(f'{symbol} - Optimized Strategy Performance')
        ax1.set_ylabel('Cumulative Return')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # 2. 仓位变化（更平滑）
        ax2 = axes[0, 1]
        ax2.plot(backtest.index, backtest['position'] * 100, alpha=0.8, linewidth=2)
        ax2.set_title('Optimized Position Over Time')
        ax2.set_ylabel('Position (%)')
        ax2.grid(True, alpha=0.3)
        
        # 3. 超额收益
        ax3 = axes[1, 0]
        excess_returns = (backtest['cum_strategy'] / backtest['cum_benchmark'] - 1) * 100
        ax3.plot(backtest.index, excess_returns, color='green', alpha=0.8, linewidth=2)
        ax3.axhline(y=0, color='red', linestyle='--', alpha=0.5)
        ax3.set_title('Cumulative Excess Return')
        ax3.set_ylabel('Excess Return (%)')
        ax3.grid(True, alpha=0.3)
        
        # 4. 滚动夏普比率对比
        ax4 = axes[1, 1]
        window = 60
        strategy_rolling_sharpe = backtest['strategy_return'].rolling(window).mean() / backtest['strategy_return'].rolling(window).std() * np.sqrt(252)
        benchmark_rolling_sharpe = backtest['return'].rolling(window).mean() / backtest['return'].rolling(window).std() * np.sqrt(252)
        
        ax4.plot(backtest.index, strategy_rolling_sharpe, label='Strategy Sharpe', alpha=0.8)
        ax4.plot(backtest.index, benchmark_rolling_sharpe, label='Benchmark Sharpe', alpha=0.8)
        ax4.set_title('Rolling Sharpe Ratio (60d)')
        ax4.set_ylabel('Sharpe Ratio')
        ax4.legend()
        ax4.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.show()
    
    def analyze_optimized_stock(self, symbol):
        """分析单只股票（优化版）"""
        print(f"\n{'='*60}")
        print(f"优化版HMM策略分析: {symbol}")
        print('='*60)
        
        # 1-6. 常规步骤
        data = self.download_data(symbol)
        if data is None:
            return None
        
        features = self.create_features(data)
        if len(features) < 100:
            return None
        
        states = self.train_hmm(features)
        analysis = self.analyze_regimes(features, states)
        signals = self.create_optimized_signals(features, states, analysis)
        backtest, performance = self.backtest_optimized_strategy(features, signals)
        
        # 7. 可视化
        self.visualize_optimized_results(backtest, performance, symbol)
        
        # 8. 详细分析
        print(f"\n{symbol} 优化策略绩效总结:")
        print("-" * 50)
        print(f"超额收益:         {performance['absolute_excess_return']:>8.2f}%")
        print(f"策略夏普比率:     {performance['strategy_sharpe']:>8.3f}")
        print(f"基准夏普比率:     {performance['benchmark_sharpe']:>8.3f}")
        print(f"胜率:             {performance['win_rate']:>8.1f}%")
        print(f"策略最大回撤:     {performance['strategy_max_drawdown']:>8.2f}%")
        print(f"基准最大回撤:     {performance['benchmark_max_drawdown']:>8.2f}%")
        print(f"平均仓位:         {performance['average_position']:>8.1f}%")
        print(f"实际年化换手率:   {performance['actual_turnover_rate']:>8.1f}%")
        
        return {
            'symbol': symbol,
            'performance': performance,
            'features': features,
            'signals': signals,
            'backtest': backtest
        }


def main():
    """主函数"""
    # 创建优化策略
    strategy = OptimizedHMMStrategy(
        n_components=3, 
        transaction_cost=0.0005,  # 0.05%
        min_holding_days=5
    )
    
    # 分析股票
    symbols = ['AAPL', 'MSFT', 'GOOGL']
    results = []
    
    for symbol in symbols:
        try:
            result = strategy.analyze_optimized_stock(symbol)
            if result:
                results.append(result)
        except Exception as e:
            print(f"✗ {symbol} 分析失败: {str(e)}")
    
    # 汇总优化后的结果
    if results:
        print(f"\n{'='*80}")
        print("优化后策略绩效汇总")
        print('='*80)
        
        summary_data = []
        for result in results:
            perf = result['performance']
            summary_data.append({
                'Symbol': result['symbol'],
                'Excess Return (%)': perf['absolute_excess_return'],
                'Strategy Sharpe': perf['strategy_sharpe'],
                'Benchmark Sharpe': perf['benchmark_sharpe'],
                'Win Rate (%)': perf['win_rate'],
                'Strategy Drawdown (%)': perf['strategy_max_drawdown'],
                'Actual Turnover (%)': perf['actual_turnover_rate']
            })
        
        summary_df = pd.DataFrame(summary_data)
        print(summary_df.to_string(index=False, float_format='%.2f'))
        
        # 计算改进效果
        avg_excess = summary_df['Excess Return (%)'].mean()
        avg_strategy_sharpe = summary_df['Strategy Sharpe'].mean()
        avg_benchmark_sharpe = summary_df['Benchmark Sharpe'].mean()
        avg_turnover = summary_df['Actual Turnover (%)'].mean()
        
        print(f"\n优化效果:")
        print(f"平均超额收益:     {avg_excess:>8.2f}%")
        print(f"策略平均夏普:     {avg_strategy_sharpe:>8.3f}")
        print(f"基准平均夏普:     {avg_benchmark_sharpe:>8.3f}")
        print(f"夏普比率提升:     {avg_strategy_sharpe - avg_benchmark_sharpe:>8.3f}")
        print(f"平均年化换手率:   {avg_turnover:>8.1f}%")
        
        if avg_excess > 0:
            print(f"\n✅ 优化成功！HMM策略实现了正超额收益")
            print(f"✅ 通过降低交易频率和成本控制实现改进")
        else:
            print(f"\n⚠️  仍需进一步优化")


if __name__ == "__main__":
    main()