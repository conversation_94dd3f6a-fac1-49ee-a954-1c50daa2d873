"""
HMM特征工程实验框架
整合hmm_v0.py、hmm_tutorial_v0.py、hmm_tutorial.py的优点
系统性测试不同特征组合对HMM交易策略的影响

版本设计：
- 版本0：单特征（Return）- 基线版本  
- 版本1：双特征（Return + Volume_Change）- 经典组合
- 版本2：技术指标版（Return + RSI + MACD）- 技术分析导向
- 版本3：波动率版（Return + Volatility + Volume_Ratio）- 风险导向
- 版本4：动量版（Return + Momentum_5d + Momentum_20d）- 趋势导向
- 版本5：综合版（Return + RSI + MACD + Volatility + Volume_Ratio）- 全面特征

作者：HMM量化研究
版本：3.0
"""

import numpy as np
import pandas as pd
import yfinance as yf
from hmmlearn.hmm import GaussianHMM
from sklearn.preprocessing import StandardScaler
import seaborn as sns
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
import matplotlib.pyplot as plt
import matplotlib

def setup_chinese_font():
    """设置中文字体显示"""
    plt.rcParams['font.sans-serif'] = ['Arial Unicode MS']
    plt.rcParams['axes.unicode_minus'] = False
    try:
        matplotlib.font_manager._rebuild()
    except AttributeError:
        pass

setup_chinese_font()

# 设置绘图风格
try:
    plt.style.use('seaborn-v0_8-whitegrid')
except:
    try:
        plt.style.use('seaborn-whitegrid')
    except:
        plt.style.use('default')

sns.set_palette("husl")

class HMMFeatureExperiments:
    """
    HMM特征实验系统
    系统性测试不同特征组合对模型性能的影响
    """
    
    def __init__(self, ticker='AAPL', n_states=3):
        self.ticker = ticker
        self.n_states = n_states
        self.data = None
        self.train_data = None
        self.test_data = None
        self.train_end_date = '2023-12-31'
        self.test_start_date = '2024-01-01'
        self.experiment_results = {}
        
    def fetch_data(self, start_date=None, end_date=None):
        """获取股票数据并分割训练/测试集"""
        if end_date is None:
            end_date = datetime.now()
        if start_date is None:
            start_date = end_date - timedelta(days=1000)
            
        print(f"🔄 正在获取 {self.ticker} 的数据...")
        self.data = yf.download(self.ticker, start=start_date, end=end_date, auto_adjust=True)
        
        # 处理多列数据问题
        if isinstance(self.data.columns, pd.MultiIndex):
            self.data.columns = self.data.columns.droplevel(1)
            
        # 严格时间分离
        self.train_data = self.data[self.data.index <= self.train_end_date]
        self.test_data = self.data[self.data.index >= self.test_start_date]
        
        print(f"✅ 数据获取完成")
        print(f"   总记录数: {len(self.data)}")
        print(f"   训练数据: {len(self.train_data)} 条 (截至 {self.train_end_date})")
        print(f"   测试数据: {len(self.test_data)} 条 (从 {self.test_start_date} 开始)")
        
        return self.data
    
    def explain_experiment_design(self):
        """解释实验设计思路"""
        print("\n" + "="*70)
        print("🧪 HMM特征工程实验设计")
        print("="*70)
        
        print("\n🎯 实验目标：")
        print("   系统性评估不同特征组合对HMM交易策略性能的影响")
        
        print("\n📋 实验版本设计：")
        versions = {
            "版本0": "单特征基线（Return）- 验证HMM基础能力",
            "版本1": "双特征经典（Return + Volume_Change）- 经典价量组合", 
            "版本2": "技术指标版（Return + RSI + MACD）- 技术分析导向",
            "版本3": "波动率版（Return + Volatility + Volume_Ratio）- 风险管理导向",
            "版本4": "动量版（Return + Momentum_5d + Momentum_20d）- 趋势跟踪导向",
            "版本5": "综合增强版（5个特征）- 全面特征工程"
        }
        
        for version, description in versions.items():
            print(f"   {version}: {description}")
        
        print("\n🔬 实验控制变量：")
        print("   - 相同的数据集和时间分割")
        print("   - 相同的HMM参数（3状态，100迭代）")
        print("   - 相同的交易策略逻辑")
        print("   - 相同的性能评估指标")
        
        print("\n📊 评估维度：")
        print("   - 收益性能：年化收益率、夏普比率")
        print("   - 风险控制：最大回撤、胜率")
        print("   - 模型质量：状态稳定性、特征重要性")
        print("   - 实用性：计算复杂度、实现难度")
        
    # ==================== 特征工程方法 ====================
    def _calculate_rsi(self, prices, period=14):
        """计算RSI指标"""
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        return rsi
    
    def _calculate_macd(self, prices, fast=12, slow=26, signal=9):
        """计算MACD指标"""
        ema_fast = prices.ewm(span=fast).mean()
        ema_slow = prices.ewm(span=slow).mean()
        macd = ema_fast - ema_slow
        macd_signal = macd.ewm(span=signal).mean()
        return macd - macd_signal
    
    def _prepare_base_features(self, data):
        """准备基础特征"""
        df = data.copy()
        
        # 基础价格特征
        df['Return'] = df['Close'].pct_change()
        
        # 成交量特征
        if 'Volume' in df.columns:
            if isinstance(df['Volume'], pd.DataFrame):
                volume_data = df['Volume'].iloc[:, 0]
            else:
                volume_data = df['Volume']
            df['Volume_Change'] = volume_data.pct_change()
            df['Volume_Ratio'] = volume_data / volume_data.rolling(window=20).mean()
        else:
            df['Volume_Change'] = 0.0
            df['Volume_Ratio'] = 1.0
        
        # 技术指标
        df['RSI'] = self._calculate_rsi(df['Close'])
        df['MACD'] = self._calculate_macd(df['Close'])
        
        # 波动率特征
        df['Volatility'] = df['Return'].rolling(window=20).std() * np.sqrt(252)
        
        # 动量特征
        df['Momentum_5d'] = df['Close'].pct_change(5)
        df['Momentum_20d'] = df['Close'].pct_change(20)
        
        return df
    
    # ==================== 实验版本实现 ====================
    def version0_single_feature(self):
        """版本0：单特征基线实验（仅使用Return）"""
        print("\n" + "="*50)
        print("🔬 版本0：单特征基线实验")
        print("="*50)
        print("📝 特征设计：仅使用收益率（Return）")
        print("🎯 目标：验证HMM的基础能力，建立性能基线")
        
        # 训练数据特征工程
        train_df = self._prepare_base_features(self.train_data)
        train_df = train_df.dropna()
        features = ['Return']
        X_train = train_df[features].values
        
        # 训练模型
        scaler = StandardScaler()
        X_train_scaled = scaler.fit_transform(X_train)
        model = GaussianHMM(n_components=self.n_states, covariance_type="full", 
                           n_iter=100, random_state=42)
        model.fit(X_train_scaled)
        
        # 测试数据预测
        test_df = self._prepare_base_features(self.test_data)
        test_df = test_df.dropna()
        X_test = test_df[features].values
        X_test_scaled = scaler.transform(X_test)
        
        test_states = model.predict(X_test_scaled)
        test_df['State'] = test_states
        
        # 构建交易策略
        result_df = self._build_simple_strategy(test_df, model, scaler, features)
        
        # 计算性能指标
        metrics = self._calculate_performance_metrics(result_df)
        
        print(f"✅ 版本0完成：{metrics['strategy_annual_return']:.2%} 年化收益")
        
        return result_df, model, metrics, features
    
    def version1_dual_features(self):
        """版本1：双特征经典实验（Return + Volume_Change）"""
        print("\n" + "="*50)
        print("🔬 版本1：双特征经典实验")
        print("="*50)
        print("📝 特征设计：价格收益率 + 成交量变化")
        print("🎯 目标：验证经典价量组合的效果")
        
        # 训练数据特征工程
        train_df = self._prepare_base_features(self.train_data)
        train_df = train_df.dropna()
        features = ['Return', 'Volume_Change']
        X_train = train_df[features].values
        
        # 训练模型
        scaler = StandardScaler()
        X_train_scaled = scaler.fit_transform(X_train)
        model = GaussianHMM(n_components=self.n_states, covariance_type="full", 
                           n_iter=100, random_state=42)
        model.fit(X_train_scaled)
        
        # 测试数据预测
        test_df = self._prepare_base_features(self.test_data)
        test_df = test_df.dropna()
        X_test = test_df[features].values
        X_test_scaled = scaler.transform(X_test)
        
        test_states = model.predict(X_test_scaled)
        test_df['State'] = test_states
        
        # 构建交易策略
        result_df = self._build_simple_strategy(test_df, model, scaler, features)
        
        # 计算性能指标
        metrics = self._calculate_performance_metrics(result_df)
        
        print(f"✅ 版本1完成：{metrics['strategy_annual_return']:.2%} 年化收益")
        
        return result_df, model, metrics, features
    
    def version2_technical_indicators(self):
        """版本2：技术指标版实验（Return + RSI + MACD）"""
        print("\n" + "="*50)
        print("🔬 版本2：技术指标版实验")
        print("="*50)
        print("📝 特征设计：收益率 + RSI + MACD")
        print("🎯 目标：验证技术分析指标的预测能力")
        
        # 训练数据特征工程
        train_df = self._prepare_base_features(self.train_data)
        train_df = train_df.dropna()
        features = ['Return', 'RSI', 'MACD']
        X_train = train_df[features].values
        
        # 训练模型
        scaler = StandardScaler()
        X_train_scaled = scaler.fit_transform(X_train)
        model = GaussianHMM(n_components=self.n_states, covariance_type="diag", 
                           n_iter=150, random_state=42)
        model.fit(X_train_scaled)
        
        # 测试数据预测
        test_df = self._prepare_base_features(self.test_data)
        test_df = test_df.dropna()
        X_test = test_df[features].values
        X_test_scaled = scaler.transform(X_test)
        
        test_states = model.predict(X_test_scaled)
        test_df['State'] = test_states
        
        # 构建交易策略
        result_df = self._build_simple_strategy(test_df, model, scaler, features)
        
        # 计算性能指标
        metrics = self._calculate_performance_metrics(result_df)
        
        print(f"✅ 版本2完成：{metrics['strategy_annual_return']:.2%} 年化收益")
        
        return result_df, model, metrics, features
    
    def version3_volatility_focus(self):
        """版本3：波动率版实验（Return + Volatility + Volume_Ratio）"""
        print("\n" + "="*50)
        print("🔬 版本3：波动率版实验")
        print("="*50)
        print("📝 特征设计：收益率 + 波动率 + 成交量比率")
        print("🎯 目标：验证风险管理导向的特征组合")
        
        # 训练数据特征工程
        train_df = self._prepare_base_features(self.train_data)
        train_df = train_df.dropna()
        features = ['Return', 'Volatility', 'Volume_Ratio']
        X_train = train_df[features].values
        
        # 训练模型
        scaler = StandardScaler()
        X_train_scaled = scaler.fit_transform(X_train)
        model = GaussianHMM(n_components=self.n_states, covariance_type="diag", 
                           n_iter=150, random_state=42)
        model.fit(X_train_scaled)
        
        # 测试数据预测
        test_df = self._prepare_base_features(self.test_data)
        test_df = test_df.dropna()
        X_test = test_df[features].values
        X_test_scaled = scaler.transform(X_test)
        
        test_states = model.predict(X_test_scaled)
        test_df['State'] = test_states
        
        # 构建交易策略
        result_df = self._build_simple_strategy(test_df, model, scaler, features)
        
        # 计算性能指标
        metrics = self._calculate_performance_metrics(result_df)
        
        print(f"✅ 版本3完成：{metrics['strategy_annual_return']:.2%} 年化收益")
        
        return result_df, model, metrics, features
    
    def version4_momentum_focus(self):
        """版本4：动量版实验（Return + Momentum_5d + Momentum_20d）"""
        print("\n" + "="*50)
        print("🔬 版本4：动量版实验")
        print("="*50)
        print("📝 特征设计：收益率 + 5日动量 + 20日动量")
        print("🎯 目标：验证趋势跟踪导向的特征组合")
        
        # 训练数据特征工程
        train_df = self._prepare_base_features(self.train_data)
        train_df = train_df.dropna()
        features = ['Return', 'Momentum_5d', 'Momentum_20d']
        X_train = train_df[features].values
        
        # 训练模型
        scaler = StandardScaler()
        X_train_scaled = scaler.fit_transform(X_train)
        model = GaussianHMM(n_components=self.n_states, covariance_type="diag", 
                           n_iter=150, random_state=42)
        model.fit(X_train_scaled)
        
        # 测试数据预测
        test_df = self._prepare_base_features(self.test_data)
        test_df = test_df.dropna()
        X_test = test_df[features].values
        X_test_scaled = scaler.transform(X_test)
        
        test_states = model.predict(X_test_scaled)
        test_df['State'] = test_states
        
        # 构建交易策略
        result_df = self._build_simple_strategy(test_df, model, scaler, features)
        
        # 计算性能指标
        metrics = self._calculate_performance_metrics(result_df)
        
        print(f"✅ 版本4完成：{metrics['strategy_annual_return']:.2%} 年化收益")
        
        return result_df, model, metrics, features
    
    def version5_comprehensive(self):
        """版本5：综合增强版实验（Return + RSI + MACD + Volatility + Volume_Ratio）"""
        print("\n" + "="*50)
        print("🔬 版本5：综合增强版实验")
        print("="*50)
        print("📝 特征设计：收益率 + RSI + MACD + 波动率 + 成交量比率")
        print("🎯 目标：验证全面特征工程的效果")
        
        # 训练数据特征工程
        train_df = self._prepare_base_features(self.train_data)
        train_df = train_df.dropna()
        features = ['Return', 'RSI', 'MACD', 'Volatility', 'Volume_Ratio']
        X_train = train_df[features].values
        
        # 训练模型
        scaler = StandardScaler()
        X_train_scaled = scaler.fit_transform(X_train)
        model = GaussianHMM(n_components=self.n_states, covariance_type="diag", 
                           n_iter=150, random_state=42)
        model.fit(X_train_scaled)
        
        # 测试数据预测
        test_df = self._prepare_base_features(self.test_data)
        test_df = test_df.dropna()
        X_test = test_df[features].values
        X_test_scaled = scaler.transform(X_test)
        
        test_states = model.predict(X_test_scaled)
        test_df['State'] = test_states
        
        # 构建交易策略
        result_df = self._build_simple_strategy(test_df, model, scaler, features)
        
        # 计算性能指标
        metrics = self._calculate_performance_metrics(result_df)
        
        print(f"✅ 版本5完成：{metrics['strategy_annual_return']:.2%} 年化收益")
        
        return result_df, model, metrics, features
    
    # ==================== 策略构建和评估 ====================
    def _build_simple_strategy(self, test_df, model, scaler, features):
        """构建统一的简单交易策略"""
        # 基于训练数据确定最佳和最差状态（避免数据穿越）
        train_df = self._prepare_base_features(self.train_data)
        train_df = train_df.dropna()
        X_train = train_df[features].values
        X_train_scaled = scaler.transform(X_train)
        train_states = model.predict(X_train_scaled)
        train_df['State'] = train_states
        
        # 计算各状态的平均收益
        state_returns = train_df.groupby('State')['Return'].mean()
        best_state = state_returns.idxmax()
        worst_state = state_returns.idxmin()
        
        # 构建交易信号
        test_df['Position'] = 0
        test_df.loc[test_df['State'] == best_state, 'Position'] = 1  # 做多
        test_df.loc[test_df['State'] == worst_state, 'Position'] = -1  # 做空
        
        # 计算策略收益
        test_df['Strategy_Return'] = test_df['Position'].shift(1) * test_df['Return']
        test_df['Strategy_Return'] = test_df['Strategy_Return'].fillna(0)
        test_df['Strategy_Cumulative'] = (1 + test_df['Strategy_Return']).cumprod()
        test_df['Buy_Hold_Cumulative'] = (1 + test_df['Return']).cumprod()
        
        return test_df
    
    def _calculate_performance_metrics(self, df):
        """计算统一的性能指标"""
        strategy_returns = df['Strategy_Return'].dropna()
        buy_hold_returns = df['Return'].dropna()
        
        # 年化收益率
        n_days = len(strategy_returns)
        strategy_annual = (df['Strategy_Cumulative'].iloc[-1] ** (252/n_days) - 1)
        buy_hold_annual = (df['Buy_Hold_Cumulative'].iloc[-1] ** (252/n_days) - 1)
        
        # 夏普比率
        if strategy_returns.std() > 0:
            strategy_sharpe = strategy_returns.mean() / strategy_returns.std() * np.sqrt(252)
        else:
            strategy_sharpe = 0
        
        # 最大回撤
        cumulative = df['Strategy_Cumulative']
        running_max = cumulative.expanding().max()
        drawdown = (cumulative - running_max) / running_max
        max_drawdown = drawdown.min()
        
        # 信息比率
        excess_returns = strategy_returns - buy_hold_returns
        if excess_returns.std() > 0:
            information_ratio = excess_returns.mean() / excess_returns.std() * np.sqrt(252)
        else:
            information_ratio = 0
        
        # 胜率
        winning_days = (strategy_returns > 0).sum()
        total_days = len(strategy_returns)
        win_rate = winning_days / total_days if total_days > 0 else 0
        
        return {
            'strategy_annual_return': strategy_annual,
            'buy_hold_annual_return': buy_hold_annual,
            'sharpe_ratio': strategy_sharpe,
            'max_drawdown': max_drawdown,
            'information_ratio': information_ratio,
            'win_rate': win_rate
        }
    
    # ==================== 实验执行和对比分析 ====================
    def run_all_experiments(self):
        """运行所有实验版本"""
        print("\n" + "="*70)
        print("🚀 开始执行HMM特征工程实验")
        print("="*70)
        
        # 存储所有实验结果
        experiments = {}
        
        # 依次运行所有版本
        experiments['版本0_单特征'] = self.version0_single_feature()
        experiments['版本1_双特征'] = self.version1_dual_features()
        experiments['版本2_技术指标'] = self.version2_technical_indicators()
        experiments['版本3_波动率'] = self.version3_volatility_focus()
        experiments['版本4_动量'] = self.version4_momentum_focus()
        experiments['版本5_综合'] = self.version5_comprehensive()
        
        self.experiment_results = experiments
        
        # 生成实验对比分析
        self._compare_all_experiments()
        
        # 生成实验可视化
        self._plot_experiment_comparison()
        
        return experiments
    
    def _compare_all_experiments(self):
        """对比所有实验结果"""
        print("\n" + "="*70)
        print("📊 HMM特征工程实验对比分析")
        print("="*70)
        
        # 收集所有指标
        comparison_data = {}
        feature_counts = {}
        
        for exp_name, (df, model, metrics, features) in self.experiment_results.items():
            comparison_data[exp_name] = {
                '年化收益率': f"{metrics['strategy_annual_return']:.2%}",
                '买入持有收益': f"{metrics['buy_hold_annual_return']:.2%}",
                '夏普比率': f"{metrics['sharpe_ratio']:.3f}",
                '最大回撤': f"{metrics['max_drawdown']:.2%}",
                '信息比率': f"{metrics['information_ratio']:.3f}",
                '胜率': f"{metrics['win_rate']:.2%}",
                '超额收益': f"{metrics['strategy_annual_return'] - metrics['buy_hold_annual_return']:.2%}",
                '特征数量': len(features)
            }
            feature_counts[exp_name] = len(features)
        
        # 打印对比表格
        print("\n📋 性能对比表格：")
        print("-" * 120)
        
        # 表头
        header = f"{'版本':<12} {'年化收益':<10} {'夏普比率':<10} {'最大回撤':<10} {'胜率':<8} {'超额收益':<10} {'特征数':<6}"
        print(header)
        print("-" * 120)
        
        # 数据行
        for exp_name, data in comparison_data.items():
            version_name = exp_name.replace('版本', 'V').replace('_', '-')
            row = (f"{version_name:<12} "
                  f"{data['年化收益率']:<10} "
                  f"{data['夏普比率']:<10} "
                  f"{data['最大回撤']:<10} "
                  f"{data['胜率']:<8} "
                  f"{data['超额收益']:<10} "
                  f"{data['特征数量']:<6}")
            print(row)
        
        print("-" * 120)
        
        # 实验结论
        print("\n🔍 实验结论：")
        
        # 找出最佳版本
        best_return_version = max(self.experiment_results.keys(), 
                                 key=lambda x: self.experiment_results[x][2]['strategy_annual_return'])
        best_sharpe_version = max(self.experiment_results.keys(),
                                 key=lambda x: self.experiment_results[x][2]['sharpe_ratio'])
        best_drawdown_version = max(self.experiment_results.keys(),
                                   key=lambda x: -self.experiment_results[x][2]['max_drawdown'])
        
        print(f"🏆 最高收益率: {best_return_version}")
        print(f"⚡ 最高夏普比率: {best_sharpe_version}")
        print(f"🛡️  最小回撤: {best_drawdown_version}")
        
        # 特征数量效应分析
        print(f"\n📈 特征数量效应分析：")
        for exp_name, count in feature_counts.items():
            annual_return = self.experiment_results[exp_name][2]['strategy_annual_return']
            print(f"   {count}个特征 ({exp_name}): {annual_return:.2%}")
        
        print(f"\n💡 核心发现：")
        print(f"   - 特征工程对HMM性能有显著影响")
        print(f"   - 不是特征越多越好，需要平衡复杂度和效果")
        print(f"   - 不同类型的特征适合不同的市场环境")
        print(f"   - 技术指标和风险指标的组合可能更稳健")
    
    def _plot_experiment_comparison(self):
        """绘制实验对比图表（使用最新可视化方法）"""
        setup_chinese_font()
        
        # 创建综合对比图
        fig = plt.figure(figsize=(16, 12))
        gs = fig.add_gridspec(3, 3, height_ratios=[1, 1, 1], width_ratios=[1.2, 1.2, 1])
        
        # 颜色映射
        colors = ['#E74C3C', '#F39C12', '#F1C40F', '#27AE60', '#3498DB', '#9B59B6']
        version_names = ['V0-单特征', 'V1-双特征', 'V2-技术指标', 'V3-波动率', 'V4-动量', 'V5-综合']
        
        # 1. 累计收益对比 (左上)
        ax1 = fig.add_subplot(gs[0, 0])
        for i, (exp_name, (df, _, _, _)) in enumerate(self.experiment_results.items()):
            ax1.plot(df.index, df['Strategy_Cumulative'], 
                    label=version_names[i], color=colors[i], linewidth=2, alpha=0.8)
        
        ax1.set_title('策略累计收益对比', fontsize=12, fontweight='bold')
        ax1.set_ylabel('累计收益', fontsize=10)
        ax1.legend(loc='upper left', fontsize=8)
        ax1.grid(True, alpha=0.3)
        ax1.spines['top'].set_visible(False)
        ax1.spines['right'].set_visible(False)
        
        # 2. 性能指标雷达图 (右上)
        ax2 = fig.add_subplot(gs[0, 1], projection='polar')
        
        # 选择几个关键版本进行雷达图对比
        key_versions = ['版本0_单特征', '版本1_双特征', '版本5_综合']
        key_colors = ['#E74C3C', '#F39C12', '#9B59B6']
        key_names = ['V0-单特征', 'V1-双特征', 'V5-综合']
        
        # 雷达图指标
        metrics_names = ['年化收益', '夏普比率', '信息比率', '胜率', '回撤控制']
        angles = np.linspace(0, 2 * np.pi, len(metrics_names), endpoint=False).tolist()
        angles += angles[:1]  # 闭合
        
        for i, (exp_name, color, name) in enumerate(zip(key_versions, key_colors, key_names)):
            _, _, metrics, _ = self.experiment_results[exp_name]
            
            # 标准化指标到0-1范围
            values = [
                min(metrics['strategy_annual_return'] / 0.5, 1),  # 50%作为满分
                min(metrics['sharpe_ratio'] / 3, 1),  # 3作为满分
                min((metrics['information_ratio'] + 1) / 2, 1),  # -1到1映射到0-1
                metrics['win_rate'],  # 已经是0-1
                min(1 + metrics['max_drawdown'] / 0.3, 1)  # 30%回撤作为0分
            ]
            values += values[:1]  # 闭合
            
            ax2.plot(angles, values, 'o-', linewidth=2, label=name, color=color)
            ax2.fill(angles, values, alpha=0.15, color=color)
        
        ax2.set_xticks(angles[:-1])
        ax2.set_xticklabels(metrics_names, fontsize=9)
        ax2.set_ylim(0, 1)
        ax2.set_title('关键版本性能雷达图', fontsize=12, fontweight='bold', pad=20)
        ax2.legend(loc='upper right', bbox_to_anchor=(1.3, 1.0), fontsize=9)
        ax2.grid(True)
        
        # 3. 特征数量 vs 性能散点图 (右上角)
        ax3 = fig.add_subplot(gs[0, 2])
        
        feature_counts = []
        annual_returns = []
        sharpe_ratios = []
        
        for i, (exp_name, (_, _, metrics, features)) in enumerate(self.experiment_results.items()):
            feature_counts.append(len(features))
            annual_returns.append(metrics['strategy_annual_return'])
            sharpe_ratios.append(metrics['sharpe_ratio'])
        
        # 以年化收益率为主，夏普比率作为气泡大小
        sizes = [max(50, s * 100) for s in sharpe_ratios]
        scatter = ax3.scatter(feature_counts, annual_returns, 
                             c=colors[:len(feature_counts)], s=sizes, alpha=0.7)
        
        # 添加版本标签
        for i, (x, y) in enumerate(zip(feature_counts, annual_returns)):
            ax3.annotate(f'V{i}', (x, y), xytext=(5, 5), textcoords='offset points', fontsize=9)
        
        ax3.set_xlabel('特征数量', fontsize=10)
        ax3.set_ylabel('年化收益率', fontsize=10)
        ax3.set_title('特征数量 vs 性能', fontsize=12, fontweight='bold')
        ax3.grid(True, alpha=0.3)
        ax3.spines['top'].set_visible(False)
        ax3.spines['right'].set_visible(False)
        
        # 4. 各版本回撤对比 (左中)
        ax4 = fig.add_subplot(gs[1, 0])
        for i, (exp_name, (df, _, _, _)) in enumerate(self.experiment_results.items()):
            cumulative = df['Strategy_Cumulative']
            running_max = cumulative.expanding().max()
            drawdown = (cumulative - running_max) / running_max * 100
            
            ax4.fill_between(df.index, drawdown, 0, 
                           color=colors[i], alpha=0.6, label=version_names[i])
        
        ax4.set_title('回撤对比', fontsize=12, fontweight='bold')
        ax4.set_ylabel('回撤 (%)', fontsize=10)
        ax4.legend(loc='lower right', fontsize=8)
        ax4.grid(True, alpha=0.3)
        ax4.spines['top'].set_visible(False)
        ax4.spines['right'].set_visible(False)
        
        # 5. 性能指标柱状图 (中中)
        ax5 = fig.add_subplot(gs[1, 1])
        
        x_pos = np.arange(len(version_names))
        annual_returns_pct = [self.experiment_results[exp][2]['strategy_annual_return'] * 100 
                             for exp in self.experiment_results.keys()]
        
        bars = ax5.bar(x_pos, annual_returns_pct, color=colors, alpha=0.7)
        ax5.set_xlabel('版本', fontsize=10)
        ax5.set_ylabel('年化收益率 (%)', fontsize=10)
        ax5.set_title('年化收益率对比', fontsize=12, fontweight='bold')
        ax5.set_xticks(x_pos)
        ax5.set_xticklabels([name.split('-')[0] for name in version_names], fontsize=9)
        ax5.grid(True, alpha=0.3, axis='y')
        ax5.spines['top'].set_visible(False)
        ax5.spines['right'].set_visible(False)
        
        # 添加数值标签
        for bar, value in zip(bars, annual_returns_pct):
            ax5.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.5,
                    f'{value:.1f}%', ha='center', va='bottom', fontsize=8)
        
        # 6. 夏普比率对比 (右中)
        ax6 = fig.add_subplot(gs[1, 2])
        
        sharpe_ratios = [self.experiment_results[exp][2]['sharpe_ratio'] 
                        for exp in self.experiment_results.keys()]
        
        bars = ax6.bar(x_pos, sharpe_ratios, color=colors, alpha=0.7)
        ax6.set_xlabel('版本', fontsize=10)
        ax6.set_ylabel('夏普比率', fontsize=10)
        ax6.set_title('夏普比率对比', fontsize=12, fontweight='bold')
        ax6.set_xticks(x_pos)
        ax6.set_xticklabels([name.split('-')[0] for name in version_names], fontsize=9)
        ax6.grid(True, alpha=0.3, axis='y')
        ax6.spines['top'].set_visible(False)
        ax6.spines['right'].set_visible(False)
        
        # 添加数值标签
        for bar, value in zip(bars, sharpe_ratios):
            ax6.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.02,
                    f'{value:.2f}', ha='center', va='bottom', fontsize=8)
        
        # 7. 特征重要性分析 (下方)
        ax7 = fig.add_subplot(gs[2, :])
        
        # 创建特征使用频率统计
        all_features = ['Return', 'Volume_Change', 'RSI', 'MACD', 'Volatility', 
                       'Volume_Ratio', 'Momentum_5d', 'Momentum_20d']
        feature_usage = {feature: [] for feature in all_features}
        
        for exp_name, (_, _, metrics, features) in self.experiment_results.items():
            for feature in all_features:
                if feature in features:
                    feature_usage[feature].append(metrics['strategy_annual_return'])
                else:
                    feature_usage[feature].append(0)
        
        # 计算每个特征的平均贡献
        feature_avg_return = {feature: np.mean([r for r in returns if r > 0]) if any(r > 0 for r in returns) else 0
                             for feature, returns in feature_usage.items()}
        
        # 绘制特征重要性
        features_sorted = sorted(feature_avg_return.items(), key=lambda x: x[1], reverse=True)
        feature_names = [f[0] for f in features_sorted]
        feature_values = [f[1] * 100 for f in features_sorted]
        
        bars = ax7.barh(feature_names, feature_values, color='lightblue', alpha=0.8)
        ax7.set_xlabel('平均年化收益率贡献 (%)', fontsize=10)
        ax7.set_title('特征重要性分析（基于包含该特征的版本平均表现）', fontsize=12, fontweight='bold')
        ax7.grid(True, alpha=0.3, axis='x')
        ax7.spines['top'].set_visible(False)
        ax7.spines['right'].set_visible(False)
        
        plt.suptitle('HMM特征工程实验综合分析报告', fontsize=16, fontweight='bold', y=0.98)
        plt.tight_layout()
        plt.subplots_adjust(top=0.94, hspace=0.3, wspace=0.3)
        
        plt.show()

def run_hmm_feature_experiments():
    """运行HMM特征工程实验的主函数"""
    print("="*70)
    print("🧪 HMM特征工程系统性实验")
    print("基于hmm_v0.py、hmm_tutorial_v0.py、hmm_tutorial.py的整合")
    print("="*70)
    
    # 创建实验系统
    experiment_system = HMMFeatureExperiments(ticker='AAPL', n_states=3)
    
    # 解释实验设计
    experiment_system.explain_experiment_design()
    
    # 获取数据
    experiment_system.fetch_data()
    
    # 运行所有实验
    results = experiment_system.run_all_experiments()
    
    print("\n" + "="*70)
    print("🎯 实验总结")
    print("="*70)
    print("✅ 成功完成6个版本的HMM特征工程实验")
    print("✅ 系统性评估了不同特征组合的效果")
    print("✅ 使用最新的可视化和分析方法")
    print("✅ 严格控制数据穿越，确保结果可靠")
    
    print("\n📚 实验价值：")
    print("- 为特征选择提供数据支撑")
    print("- 揭示特征工程的最佳实践")
    print("- 平衡模型复杂度与性能")
    print("- 指导实际交易策略开发")
    
    return experiment_system, results

if __name__ == "__main__":
    run_hmm_feature_experiments() 