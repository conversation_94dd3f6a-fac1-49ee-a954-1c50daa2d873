"""
隐马尔可夫模型因子投资系统
Hidden Markov Model for Factor Investment

基于隐马尔可夫模型的智能因子配置系统，能够识别不同市场环境并动态调整因子暴露。
"""

import numpy as np
import pandas as pd
import yfinance as yf
from hmmlearn.hmm import GaussianHMM
from sklearn.preprocessing import StandardScaler
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

class HMMFactorInvestment:
    """隐马尔可夫模型因子投资类"""
    
    def __init__(self, n_components=3, random_state=42):
        """
        初始化HMM因子投资模型
        
        Args:
            n_components: 隐藏状态数量（市场环境数量）
            random_state: 随机种子
        """
        self.n_components = n_components
        self.random_state = random_state
        self.hmm_model = None
        self.scaler = StandardScaler()
        self.feature_data = None
        self.features = None
        
    def download_data(self, ticker, days=1000):
        """
        下载股票数据
        
        Args:
            ticker: 股票代码
            days: 历史数据天数
            
        Returns:
            pandas.DataFrame: 股票价格数据
        """
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)
        
        print(f"正在下载 {ticker} 股票数据...")
        data = yf.download(ticker, start=start_date, end=end_date)
        print(f"成功下载 {len(data)} 条数据记录")
        
        return data
    
    def create_advanced_features(self, data):
        """
        创建高级技术指标特征
        
        Args:
            data: 原始价格数据
            
        Returns:
            pandas.DataFrame: 包含技术指标的特征数据
        """
        df = data.copy()
        
        # 基础回报率特征
        df['Return'] = df['Close'].pct_change()
        df['Log_Return'] = np.log(df['Close'] / df['Close'].shift(1))
        
        # 动量因子 (多时间框架)
        df['Momentum_5d'] = df['Close'].pct_change(5)
        df['Momentum_10d'] = df['Close'].pct_change(10) 
        df['Momentum_20d'] = df['Close'].pct_change(20)
        
        # 波动率因子 (多时间框架)
        df['Volatility_5d'] = df['Return'].rolling(window=5).std()
        df['Volatility_10d'] = df['Return'].rolling(window=10).std()
        df['Volatility_20d'] = df['Return'].rolling(window=20).std()
        
        # 价格相对位置 (类似RSI)
        for window in [5, 10, 20]:
            high_roll = df['High'].rolling(window).max()
            low_roll = df['Low'].rolling(window).min()
            df[f'Position_{window}d'] = (df['Close'] - low_roll) / (high_roll - low_roll)
        
        # 交易量特征
        df['Volume_Change'] = df['Volume'].pct_change()
        df['Volume_MA_Ratio'] = df['Volume'] / df['Volume'].rolling(window=20).mean()
        
        # 价格gap特征
        df['Gap'] = (df['Open'] - df['Close'].shift(1)) / df['Close'].shift(1)
        
        # 振幅特征
        df['Range'] = (df['High'] - df['Low']) / df['Close']
        
        # 移动平均线特征
        for window in [5, 10, 20]:
            ma_col = df['Close'].rolling(window).mean()
            df[f'MA_{window}'] = ma_col
            df[f'Price_to_MA_{window}'] = df['Close'] / ma_col - 1
        
        # 布林带特征
        sma_20 = df['Close'].rolling(window=20).mean()
        std_20 = df['Close'].rolling(window=20).std()
        bb_upper = sma_20 + (std_20 * 2)
        bb_lower = sma_20 - (std_20 * 2)
        df['BB_Upper'] = bb_upper
        df['BB_Lower'] = bb_lower
        df['BB_Position'] = (df['Close'] - bb_lower) / (bb_upper - bb_lower)
        
        # MACD特征
        exp1 = df['Close'].ewm(span=12).mean()
        exp2 = df['Close'].ewm(span=26).mean()
        macd_line = exp1 - exp2
        df['MACD'] = macd_line
        macd_signal = macd_line.ewm(span=9).mean()
        df['MACD_Signal'] = macd_signal
        df['MACD_Histogram'] = macd_line - macd_signal
        
        # 删除包含NaN的行
        df = df.dropna()
        
        return df
    
    def prepare_features(self, data):
        """
        准备用于HMM训练的特征
        
        Args:
            data: 包含技术指标的数据
            
        Returns:
            numpy.ndarray: 标准化后的特征矩阵
        """
        # 选择关键特征用于HMM训练
        self.features = [
            'Return', 'Log_Return',
            'Momentum_5d', 'Momentum_10d', 'Momentum_20d',
            'Volatility_5d', 'Volatility_10d', 'Volatility_20d',
            'Position_5d', 'Position_10d', 'Position_20d',
            'Volume_Change', 'Volume_MA_Ratio',
            'Gap', 'Range',
            'Price_to_MA_5', 'Price_to_MA_10', 'Price_to_MA_20',
            'BB_Position',
            'MACD', 'MACD_Signal', 'MACD_Histogram'
        ]
        
        X = data[self.features].values
        
        # 标准化特征
        X_scaled = self.scaler.fit_transform(X)
        
        return X_scaled
    
    def train_hmm(self, X_scaled):
        """
        训练隐马尔可夫模型
        
        Args:
            X_scaled: 标准化后的特征矩阵
        """
        print(f"正在训练具有 {self.n_components} 个隐藏状态的HMM模型...")
        
        # 初始化高斯HMM模型
        self.hmm_model = GaussianHMM(
            n_components=self.n_components,
            covariance_type="full",
            n_iter=1000,
            random_state=self.random_state,
            verbose=False
        )
        
        # 训练模型
        self.hmm_model.fit(X_scaled)
        
        print("HMM模型训练完成!")
        print(f"收敛分数: {self.hmm_model.score(X_scaled):.2f}")
        
    def predict_regimes(self, X_scaled):
        """
        预测市场环境（隐藏状态）
        
        Args:
            X_scaled: 标准化后的特征矩阵
            
        Returns:
            numpy.ndarray: 预测的隐藏状态序列
        """
        hidden_states = self.hmm_model.predict(X_scaled)
        return hidden_states
    
    def analyze_regimes(self, data, hidden_states):
        """
        分析不同市场环境的特征
        
        Args:
            data: 原始特征数据
            hidden_states: 预测的隐藏状态
            
        Returns:
            pandas.DataFrame: 环境分析结果
        """
        # 添加市场环境到数据中
        data_with_regime = data.copy()
        data_with_regime['Market_Regime'] = hidden_states
        
        # 分析每个环境的特征
        regime_analysis = pd.DataFrame()
        
        for i in range(self.n_components):
            regime_data = data_with_regime[data_with_regime['Market_Regime'] == i]
            
            if len(regime_data) > 0:
                # 计算基础统计指标
                stats = {
                    'Count': len(regime_data),
                    'Percentage': len(regime_data) / len(data_with_regime) * 100,
                    'Avg_Return': regime_data['Return'].mean() * 100,
                    'Return_Std': regime_data['Return'].std() * 100,
                    'Sharpe_Ratio': (regime_data['Return'].mean() / regime_data['Return'].std() * np.sqrt(252)) if regime_data['Return'].std() > 0 else 0,
                    'Max_Drawdown': self._calculate_max_drawdown(regime_data['Return']),
                    'Avg_Volatility': regime_data['Volatility_10d'].mean() * 100,
                    'Avg_Momentum_10d': regime_data['Momentum_10d'].mean() * 100,
                    'Avg_Volume_Change': regime_data['Volume_Change'].mean() * 100,
                    'BB_Position_Avg': regime_data['BB_Position'].mean(),
                    'MACD_Avg': regime_data['MACD'].mean()
                }
                
                regime_analysis[f'Regime_{i}'] = pd.Series(stats)
        
        return regime_analysis
    
    def _calculate_max_drawdown(self, returns):
        """计算最大回撤"""
        cumulative = (1 + returns).cumprod()
        running_max = cumulative.expanding().max()
        drawdown = (cumulative - running_max) / running_max
        return drawdown.min() * 100
    
    def visualize_regimes(self, data, hidden_states, ticker):
        """
        可视化不同市场环境下的表现
        
        Args:
            data: 价格数据
            hidden_states: 隐藏状态
            ticker: 股票代码
        """
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        
        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS']
        plt.rcParams['axes.unicode_minus'] = False
        
        # 颜色映射
        colors = ['red', 'blue', 'green', 'orange', 'purple'][:self.n_components]
        
        # 1. 股价与市场环境
        ax1 = axes[0, 0]
        for i in range(self.n_components):
            idx = (hidden_states == i)
            if np.any(idx):
                ax1.scatter(data.index[idx], data['Close'][idx], 
                           c=colors[i], alpha=0.6, s=10, label=f'环境 {i}')
        
        ax1.set_title(f'{ticker} 股价在不同市场环境下的分布')
        ax1.set_xlabel('日期')
        ax1.set_ylabel('收盘价')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # 2. 回报率分布
        ax2 = axes[0, 1]
        for i in range(self.n_components):
            regime_returns = data['Return'][hidden_states == i] * 100
            if len(regime_returns) > 0:
                ax2.hist(regime_returns, bins=30, alpha=0.6, 
                        color=colors[i], label=f'环境 {i}', density=True)
        
        ax2.set_title('不同环境下的日回报率分布')
        ax2.set_xlabel('日回报率 (%)')
        ax2.set_ylabel('密度')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        # 3. 波动率对比
        ax3 = axes[1, 0]
        volatility_data = []
        regime_labels = []
        
        for i in range(self.n_components):
            regime_vol = data['Volatility_10d'][hidden_states == i] * 100
            if len(regime_vol) > 0:
                volatility_data.append(regime_vol.dropna())
                regime_labels.append(f'环境 {i}')
        
        if volatility_data:
            ax3.boxplot(volatility_data, labels=regime_labels)
            ax3.set_title('不同环境下的波动率分布')
            ax3.set_ylabel('10日波动率 (%)')
            ax3.grid(True, alpha=0.3)
        
        # 4. 环境转换时间序列
        ax4 = axes[1, 1]
        ax4.plot(data.index, hidden_states, linewidth=2, alpha=0.8)
        ax4.set_title('市场环境时间序列')
        ax4.set_xlabel('日期')  
        ax4.set_ylabel('市场环境')
        ax4.set_yticks(range(self.n_components))
        ax4.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.show()
    
    def generate_factor_allocation_strategy(self, current_regime, regime_analysis):
        """
        基于当前市场环境生成因子配置策略
        
        Args:
            current_regime: 当前市场环境
            regime_analysis: 环境分析结果
            
        Returns:
            dict: 因子配置建议
        """
        current_stats = regime_analysis[f'Regime_{current_regime}']
        
        # 基于环境特征制定策略
        strategy = {
            'current_regime': current_regime,
            'regime_characteristics': {}
        }
        
        # 分析当前环境特征
        avg_return = current_stats['Avg_Return']
        volatility = current_stats['Avg_Volatility'] 
        momentum = current_stats['Avg_Momentum_10d']
        sharpe = current_stats['Sharpe_Ratio']
        
        strategy['regime_characteristics'] = {
            'return_level': 'high' if avg_return > 0.05 else 'low' if avg_return < -0.05 else 'moderate',
            'volatility_level': 'high' if volatility > 2.0 else 'low' if volatility < 1.0 else 'moderate', 
            'momentum_strength': 'strong' if momentum > 1.0 else 'weak' if momentum < -1.0 else 'moderate',
            'risk_adjusted_return': 'excellent' if sharpe > 1.0 else 'poor' if sharpe < 0 else 'moderate'
        }
        
        # 因子配置建议
        factor_weights = {}
        
        if avg_return > 0.05 and momentum > 0.5:  # 牛市环境
            factor_weights = {
                'momentum_factor': 0.4,  # 高动量配置
                'growth_factor': 0.3,    # 成长因子
                'small_cap_factor': 0.2, # 小盘股因子
                'value_factor': 0.1      # 低价值因子配置
            }
            strategy['market_type'] = '牛市环境'
            strategy['description'] = '强势上涨行情，适合配置动量和成长因子'
            
        elif avg_return < -0.05 and volatility > 2.0:  # 熊市环境
            factor_weights = {
                'low_volatility_factor': 0.4,  # 低波动因子
                'dividend_factor': 0.3,         # 股息因子
                'value_factor': 0.2,            # 价值因子
                'momentum_factor': 0.1          # 低动量配置
            }
            strategy['market_type'] = '熊市环境'
            strategy['description'] = '市场下跌，适合防御性因子配置'
            
        else:  # 震荡市环境
            factor_weights = {
                'value_factor': 0.3,      # 价值因子
                'quality_factor': 0.25,   # 质量因子
                'momentum_factor': 0.2,   # 适中动量
                'low_volatility_factor': 0.25  # 低波动
            }
            strategy['market_type'] = '震荡市环境'
            strategy['description'] = '横盘整理行情，适合平衡配置'
        
        strategy['factor_allocation'] = factor_weights
        
        return strategy
    
    def backtest_strategy(self, data, hidden_states, regime_analysis):
        """
        简单的策略回测
        
        Args:
            data: 价格数据
            hidden_states: 隐藏状态序列
            regime_analysis: 环境分析结果
            
        Returns:
            dict: 回测结果
        """
        # 计算基准收益（买入持有）
        benchmark_returns = data['Return'].fillna(0)
        benchmark_cumulative = (1 + benchmark_returns).cumprod()
        
        # 模拟策略收益（基于环境切换）
        strategy_returns = []
        
        for i, regime in enumerate(hidden_states):
            base_return = data['Return'].iloc[i] if not pd.isna(data['Return'].iloc[i]) else 0
            
            # 根据环境调整收益（简化模拟）
            regime_stats = regime_analysis[f'Regime_{regime}']
            sharpe_ratio = regime_stats['Sharpe_Ratio']
            
            # 如果是好的环境（高夏普比率），增强收益
            if sharpe_ratio > 0.5:
                adjusted_return = base_return * 1.1  # 10%增强
            elif sharpe_ratio < 0:
                adjusted_return = base_return * 0.9  # 10%保护 
            else:
                adjusted_return = base_return
                
            strategy_returns.append(adjusted_return)
        
        strategy_returns = pd.Series(strategy_returns, index=data.index)
        strategy_cumulative = (1 + strategy_returns).cumprod()
        
        # 计算绩效指标
        total_return_benchmark = benchmark_cumulative.iloc[-1] - 1
        total_return_strategy = strategy_cumulative.iloc[-1] - 1
        
        benchmark_sharpe = benchmark_returns.mean() / benchmark_returns.std() * np.sqrt(252)
        strategy_sharpe = strategy_returns.mean() / strategy_returns.std() * np.sqrt(252)
        
        max_dd_benchmark = self._calculate_max_drawdown(benchmark_returns)
        max_dd_strategy = self._calculate_max_drawdown(strategy_returns)
        
        results = {
            'benchmark_total_return': total_return_benchmark * 100,
            'strategy_total_return': total_return_strategy * 100,
            'excess_return': (total_return_strategy - total_return_benchmark) * 100,
            'benchmark_sharpe': benchmark_sharpe,
            'strategy_sharpe': strategy_sharpe,
            'benchmark_max_drawdown': max_dd_benchmark,
            'strategy_max_drawdown': max_dd_strategy,
            'benchmark_cumulative': benchmark_cumulative,
            'strategy_cumulative': strategy_cumulative
        }
        
        return results
    
    def run_complete_analysis(self, ticker, days=1000):
        """
        运行完整的HMM因子投资分析
        
        Args:
            ticker: 股票代码
            days: 历史数据天数
            
        Returns:
            dict: 完整分析结果
        """
        print("=" * 60)
        print("隐马尔可夫模型因子投资分析")
        print("=" * 60)
        
        # 1. 数据准备
        raw_data = self.download_data(ticker, days)
        self.feature_data = self.create_advanced_features(raw_data)
        
        # 2. 特征准备
        X_scaled = self.prepare_features(self.feature_data)
        print(f"准备了 {len(self.features)} 个特征用于分析")
        
        # 3. 训练HMM模型
        self.train_hmm(X_scaled)
        
        # 4. 预测市场环境
        hidden_states = self.predict_regimes(X_scaled)
        
        # 5. 分析不同环境
        regime_analysis = self.analyze_regimes(self.feature_data, hidden_states)
        
        # 6. 生成投资策略
        current_regime = hidden_states[-1]
        strategy = self.generate_factor_allocation_strategy(current_regime, regime_analysis)
        
        # 7. 策略回测
        backtest_results = self.backtest_strategy(self.feature_data, hidden_states, regime_analysis)
        
        # 8. 可视化结果
        self.visualize_regimes(self.feature_data, hidden_states, ticker)
        
        # 返回完整结果
        results = {
            'ticker': ticker,
            'data_points': len(self.feature_data),
            'features_used': self.features,
            'regime_analysis': regime_analysis,
            'current_strategy': strategy,
            'backtest_results': backtest_results,
            'model': self.hmm_model
        }
        
        return results


def main():
    """主函数演示"""
    # 创建HMM因子投资模型
    hmm_investor = HMMFactorInvestment(n_components=3, random_state=42)
    
    # 分析多只股票
    tickers = ['AAPL', 'MSFT', 'GOOGL']
    
    for ticker in tickers:
        print(f"\n正在分析 {ticker}...")
        try:
            results = hmm_investor.run_complete_analysis(ticker, days=800)
            
            # 打印结果摘要
            print(f"\n{ticker} 分析结果摘要:")
            print("-" * 40)
            print(f"数据点数: {results['data_points']}")
            print(f"当前市场环境: {results['current_strategy']['market_type']}")
            print(f"策略描述: {results['current_strategy']['description']}")
            
            # 打印环境分析
            print("\n环境分析:")
            regime_df = results['regime_analysis']
            for col in regime_df.columns:
                regime_data = regime_df[col]
                print(f"\n{col}:")
                print(f"  出现频率: {regime_data['Percentage']:.1f}%")
                print(f"  平均日收益: {regime_data['Avg_Return']:.3f}%")
                print(f"  夏普比率: {regime_data['Sharpe_Ratio']:.3f}")
                print(f"  最大回撤: {regime_data['Max_Drawdown']:.2f}%")
            
            # 打印因子配置建议
            print(f"\n推荐因子配置:")
            for factor, weight in results['current_strategy']['factor_allocation'].items():
                print(f"  {factor}: {weight*100:.1f}%")
            
            # 打印回测结果
            backtest = results['backtest_results']
            print(f"\n回测绩效:")
            print(f"  基准总收益: {backtest['benchmark_total_return']:.2f}%")
            print(f"  策略总收益: {backtest['strategy_total_return']:.2f}%")
            print(f"  超额收益: {backtest['excess_return']:.2f}%")
            print(f"  策略夏普比率: {backtest['strategy_sharpe']:.3f}")
            
        except Exception as e:
            print(f"分析 {ticker} 时出错: {str(e)}")
        
        print("=" * 60)


if __name__ == "__main__":
    main()