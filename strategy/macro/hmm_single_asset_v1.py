"""
可运行的HMM投资策略演示
Working HMM Investment Strategy Demo

修复所有错误，展示实际的超额收益计算
"""

import numpy as np
import pandas as pd
import yfinance as yf
from hmmlearn.hmm import GaussianHMM
from sklearn.preprocessing import StandardScaler
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

class WorkingHMMStrategy:
    """可工作的HMM投资策略"""
    
    def __init__(self, n_components=3, transaction_cost=0.001):
        self.n_components = n_components
        self.transaction_cost = transaction_cost
        self.hmm_model = None
        self.scaler = StandardScaler()
        
    def download_data(self, symbol, period="2y"):
        """下载单只股票数据"""
        print(f"正在下载 {symbol} 数据...")
        try:
            data = yf.download(symbol, period=period, progress=False)
            if len(data) > 100:
                print(f"✓ {symbol}: {len(data)} 条数据")
                return data
            else:
                print(f"✗ {symbol}: 数据不足")
                return None
        except Exception as e:
            print(f"✗ {symbol}: 下载失败 - {str(e)}")
            return None
    
    def create_features(self, data):
        """创建技术特征（修复版本）"""
        df = data.copy()
        features = pd.DataFrame(index=df.index)
        
        # 基础价格特征
        features['return'] = df['Close'].pct_change()
        features['log_return'] = np.log(df['Close'] / df['Close'].shift(1))
        
        # 多周期动量
        for period in [5, 10, 20]:
            features[f'momentum_{period}d'] = df['Close'].pct_change(period)
        
        # 波动率特征
        for window in [5, 10, 20]:
            features[f'volatility_{window}d'] = features['return'].rolling(window).std() * np.sqrt(252)
        
        # RSI指标
        delta = df['Close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / loss
        features['rsi'] = 100 - (100 / (1 + rs))
        
        # 移动平均线特征
        for window in [10, 20, 50]:
            ma = df['Close'].rolling(window).mean()
            features[f'ma_{window}_ratio'] = df['Close'] / ma - 1
        
        # 布林带特征（修复版本）
        sma_20 = df['Close'].rolling(20).mean()
        std_20 = df['Close'].rolling(20).std()
        bb_upper = sma_20 + 2 * std_20
        bb_lower = sma_20 - 2 * std_20
        features['bb_upper'] = bb_upper
        features['bb_lower'] = bb_lower
        features['bb_width'] = (bb_upper - bb_lower) / sma_20
        features['bb_position'] = (df['Close'] - bb_lower) / (bb_upper - bb_lower)
        
        # MACD特征（修复版本）
        exp1 = df['Close'].ewm(span=12).mean()
        exp2 = df['Close'].ewm(span=26).mean()
        macd_line = exp1 - exp2
        features['macd'] = macd_line
        macd_signal = macd_line.ewm(span=9).mean()
        features['macd_signal'] = macd_signal
        features['macd_histogram'] = macd_line - macd_signal
        
        # 成交量特征
        features['volume_change'] = df['Volume'].pct_change()
        features['volume_ma_ratio'] = df['Volume'] / df['Volume'].rolling(20).mean()
        
        # 价格形态特征
        features['hl_ratio'] = (df['High'] - df['Low']) / df['Close']
        features['gap'] = (df['Open'] - df['Close'].shift(1)) / df['Close'].shift(1)
        
        # 清理数据
        features_clean = features.dropna()
        print(f"特征工程完成: 原始{len(features)}条 -> 清理后{len(features_clean)}条, {len(features_clean.columns)}个特征")
        
        return features_clean
    
    def train_hmm(self, features):
        """训练HMM模型"""
        # 选择核心特征
        core_features = [
            'return', 'momentum_10d', 'momentum_20d',
            'volatility_10d', 'volatility_20d',
            'rsi', 'ma_20_ratio',
            'bb_position', 'bb_width',
            'macd', 'macd_histogram',
            'volume_change', 'hl_ratio'
        ]
        
        # 确保所有特征都存在
        available_features = [f for f in core_features if f in features.columns]
        print(f"使用 {len(available_features)} 个特征训练HMM")
        
        X = features[available_features].values
        X_scaled = self.scaler.fit_transform(X)
        
        # 训练HMM
        self.hmm_model = GaussianHMM(
            n_components=self.n_components,
            covariance_type="full",
            n_iter=1000,
            random_state=42
        )
        
        self.hmm_model.fit(X_scaled)
        states = self.hmm_model.predict(X_scaled)
        
        print(f"HMM训练完成，收敛分数: {self.hmm_model.score(X_scaled):.2f}")
        
        return states
    
    def analyze_regimes(self, features, states):
        """分析市场环境"""
        analysis = {}
        
        print(f"\n市场环境分析:")
        print("=" * 50)
        
        for regime in range(self.n_components):
            mask = states == regime
            regime_data = features[mask]
            
            if len(regime_data) < 10:
                continue
            
            returns = regime_data['return']
            
            stats = {
                'frequency': len(regime_data) / len(features) * 100,
                'avg_daily_return': returns.mean() * 100,
                'annualized_return': returns.mean() * 252 * 100,
                'volatility': returns.std() * np.sqrt(252) * 100,
                'sharpe_ratio': (returns.mean() / returns.std() * np.sqrt(252)) if returns.std() > 0 else 0,
                'max_drawdown': self._max_drawdown(returns) * 100,
                'win_rate': (returns > 0).mean() * 100
            }
            
            # 技术指标特征
            if 'volatility_10d' in regime_data.columns:
                stats['avg_vol'] = regime_data['volatility_10d'].mean()
            if 'rsi' in regime_data.columns:
                stats['avg_rsi'] = regime_data['rsi'].mean()
            if 'momentum_10d' in regime_data.columns:
                stats['avg_momentum'] = regime_data['momentum_10d'].mean() * 100
            
            analysis[f'regime_{regime}'] = stats
            
            print(f"\n环境 {regime} ({stats['frequency']:.1f}%):")
            print(f"  年化收益: {stats['annualized_return']:>6.1f}%")
            print(f"  年化波动: {stats['volatility']:>6.1f}%")
            print(f"  夏普比率: {stats['sharpe_ratio']:>6.3f}")
            print(f"  最大回撤: {stats['max_drawdown']:>6.1f}%")
            print(f"  胜率: {stats['win_rate']:>6.1f}%")
        
        return analysis
    
    def _max_drawdown(self, returns):
        """计算最大回撤"""
        cumulative = (1 + returns).cumprod()
        rolling_max = cumulative.expanding().max()
        drawdown = (cumulative - rolling_max) / rolling_max
        return drawdown.min()
    
    def create_trading_signals(self, features, states, analysis):
        """创建交易信号"""
        signals = pd.DataFrame(index=features.index)
        signals['regime'] = states
        signals['position'] = 0.5  # 默认50%仓位
        
        print(f"\n交易策略:")
        print("-" * 30)
        
        for regime in range(self.n_components):
            if f'regime_{regime}' not in analysis:
                continue
            
            mask = states == regime
            stats = analysis[f'regime_{regime}']
            
            sharpe = stats['sharpe_ratio']
            annual_return = stats['annualized_return']
            
            # 基于环境特征设计策略
            if sharpe > 1.0 and annual_return > 10:  # 优质环境
                position = 1.0
                strategy = "趋势跟踪(满仓)"
            elif sharpe < 0 or annual_return < -5:  # 恶劣环境
                position = 0.1
                strategy = "保守防御(轻仓)"
            else:  # 中性环境
                position = 0.6
                strategy = "均衡配置(中仓)"
            
            signals.loc[mask, 'position'] = position
            
            print(f"环境{regime}: {strategy} (仓位{position*100:.0f}%)")
        
        return signals
    
    def backtest_strategy(self, features, signals):
        """回测策略 - 详细的超额收益计算"""
        print(f"\n=== 详细回测分析 ===")
        
        backtest = pd.DataFrame(index=features.index)
        backtest['return'] = features['return']
        backtest['position'] = signals['position']
        backtest['regime'] = signals['regime']
        
        # 计算交易成本
        backtest['position_change'] = backtest['position'].diff().abs()
        backtest['transaction_cost'] = backtest['position_change'] * self.transaction_cost
        
        # 策略收益 = 仓位 × 资产收益 - 交易成本
        backtest['strategy_return'] = (
            backtest['position'].shift(1) * backtest['return'] - 
            backtest['transaction_cost']
        )
        
        # 累积收益
        backtest['cum_benchmark'] = (1 + backtest['return']).cumprod()
        backtest['cum_strategy'] = (1 + backtest['strategy_return']).cumprod()
        
        # 计算超额收益
        benchmark_total = (backtest['cum_benchmark'].iloc[-1] - 1) * 100
        strategy_total = (backtest['cum_strategy'].iloc[-1] - 1) * 100
        absolute_excess = strategy_total - benchmark_total
        relative_excess = (backtest['cum_strategy'].iloc[-1] / backtest['cum_benchmark'].iloc[-1] - 1) * 100
        
        # 其他绩效指标
        strategy_returns = backtest['strategy_return'].dropna()
        benchmark_returns = backtest['return'].dropna()
        
        performance = {
            'benchmark_total_return': benchmark_total,
            'strategy_total_return': strategy_total,
            'absolute_excess_return': absolute_excess,
            'relative_excess_return': relative_excess,
            'strategy_sharpe': (strategy_returns.mean() / strategy_returns.std() * np.sqrt(252)) if strategy_returns.std() > 0 else 0,
            'benchmark_sharpe': (benchmark_returns.mean() / benchmark_returns.std() * np.sqrt(252)) if benchmark_returns.std() > 0 else 0,
            'strategy_volatility': strategy_returns.std() * np.sqrt(252) * 100,
            'benchmark_volatility': benchmark_returns.std() * np.sqrt(252) * 100,
            'strategy_max_drawdown': self._max_drawdown(strategy_returns) * 100,
            'benchmark_max_drawdown': self._max_drawdown(benchmark_returns) * 100,
            'win_rate': (strategy_returns > 0).mean() * 100,
            'average_position': backtest['position'].mean() * 100,
            'turnover_rate': backtest['position_change'].sum() / len(backtest) * 252 * 100,
            'total_transaction_cost': backtest['transaction_cost'].sum() * 100
        }
        
        # 打印详细结果
        print(f"\n超额收益计算:")
        print(f"基准总收益:     {benchmark_total:>8.2f}%")
        print(f"策略总收益:     {strategy_total:>8.2f}%")
        print(f"绝对超额收益:   {absolute_excess:>8.2f}%")
        print(f"相对超额收益:   {relative_excess:>8.2f}%")
        print(f"交易成本:       {performance['total_transaction_cost']:>8.3f}%")
        print(f"年化换手率:     {performance['turnover_rate']:>8.1f}%")
        
        return backtest, performance
    
    def visualize_results(self, backtest, performance, symbol):
        """可视化结果"""
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        
        # 设置字体
        plt.rcParams['font.sans-serif'] = ['Arial', 'DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False
        
        # 1. 累积收益对比
        ax1 = axes[0, 0]
        ax1.plot(backtest.index, backtest['cum_benchmark'], 
                label=f'Buy & Hold ({performance["benchmark_total_return"]:.1f}%)', 
                alpha=0.8, linewidth=2)
        ax1.plot(backtest.index, backtest['cum_strategy'], 
                label=f'HMM Strategy ({performance["strategy_total_return"]:.1f}%)', 
                alpha=0.8, linewidth=2)
        ax1.set_title(f'{symbol} - Cumulative Returns Comparison')
        ax1.set_ylabel('Cumulative Return')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # 2. 环境分布
        ax2 = axes[0, 1]
        regime_counts = backtest['regime'].value_counts().sort_index()
        colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4'][:len(regime_counts)]
        ax2.pie(regime_counts.values, 
               labels=[f'Regime {i}' for i in regime_counts.index],
               colors=colors, autopct='%1.1f%%', startangle=90)
        ax2.set_title('Market Regime Distribution')
        
        # 3. 仓位变化
        ax3 = axes[1, 0]
        ax3.plot(backtest.index, backtest['position'] * 100, alpha=0.7)
        ax3.fill_between(backtest.index, backtest['position'] * 100, alpha=0.3)
        ax3.set_title('Position Over Time')
        ax3.set_ylabel('Position (%)')
        ax3.grid(True, alpha=0.3)
        
        # 4. 超额收益
        ax4 = axes[1, 1]
        excess_returns = (backtest['cum_strategy'] / backtest['cum_benchmark'] - 1) * 100
        ax4.plot(backtest.index, excess_returns, color='green', alpha=0.8)
        ax4.axhline(y=0, color='red', linestyle='--', alpha=0.5)
        ax4.set_title('Cumulative Excess Return')
        ax4.set_ylabel('Excess Return (%)')
        ax4.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.show()
    
    def analyze_stock(self, symbol):
        """分析单只股票"""
        print(f"\n{'='*60}")
        print(f"分析 {symbol}")
        print('='*60)
        
        # 1. 下载数据
        data = self.download_data(symbol)
        if data is None:
            return None
        
        # 2. 特征工程
        features = self.create_features(data)
        if len(features) < 100:
            print(f"✗ {symbol}: 有效数据不足")
            return None
        
        # 3. 训练HMM
        states = self.train_hmm(features)
        
        # 4. 分析环境
        analysis = self.analyze_regimes(features, states)
        
        # 5. 生成信号
        signals = self.create_trading_signals(features, states, analysis)
        
        # 6. 回测策略
        backtest, performance = self.backtest_strategy(features, signals)
        
        # 7. 可视化
        self.visualize_results(backtest, performance, symbol)
        
        # 8. 打印摘要
        print(f"\n{symbol} 策略绩效摘要:")
        print("-" * 40)
        print(f"超额收益:       {performance['absolute_excess_return']:>8.2f}%")
        print(f"策略夏普比率:   {performance['strategy_sharpe']:>8.3f}")
        print(f"胜率:           {performance['win_rate']:>8.1f}%")
        print(f"最大回撤:       {performance['strategy_max_drawdown']:>8.2f}%")
        
        return {
            'symbol': symbol,
            'features': features,
            'states': states,
            'analysis': analysis,
            'signals': signals,
            'backtest': backtest,
            'performance': performance
        }


def main():
    """主函数"""
    # 创建策略实例
    strategy = WorkingHMMStrategy(n_components=3, transaction_cost=0.001)
    
    # 分析股票列表
    symbols = ['AAPL', 'MSFT', 'GOOGL']
    
    results = []
    
    for symbol in symbols:
        try:
            result = strategy.analyze_stock(symbol)
            if result:
                results.append(result)
        except Exception as e:
            print(f"✗ {symbol} 分析失败: {str(e)}")
    
    # 汇总结果
    if results:
        print(f"\n{'='*80}")
        print("所有股票策略绩效汇总")
        print('='*80)
        
        summary_data = []
        for result in results:
            perf = result['performance']
            summary_data.append({
                'Symbol': result['symbol'],
                'Excess Return (%)': perf['absolute_excess_return'],
                'Strategy Sharpe': perf['strategy_sharpe'],
                'Win Rate (%)': perf['win_rate'],
                'Max Drawdown (%)': perf['strategy_max_drawdown'],
                'Turnover (%)': perf['turnover_rate']
            })
        
        summary_df = pd.DataFrame(summary_data)
        print(summary_df.to_string(index=False, float_format='%.2f'))
        
        # 计算平均表现
        avg_excess = summary_df['Excess Return (%)'].mean()
        avg_sharpe = summary_df['Strategy Sharpe'].mean()
        
        print(f"\n平均绩效:")
        print(f"平均超额收益: {avg_excess:.2f}%")
        print(f"平均夏普比率: {avg_sharpe:.3f}")
        
        # 解释结果
        print(f"\n结果解释:")
        if avg_excess > 0:
            print(f"✓ HMM策略成功实现正超额收益")
            print(f"✓ 策略通过识别市场环境动态调整仓位")
        else:
            print(f"! HMM策略未能实现正超额收益")
            print(f"! 可能原因：交易成本过高或环境识别不准确")


if __name__ == "__main__":
    main()