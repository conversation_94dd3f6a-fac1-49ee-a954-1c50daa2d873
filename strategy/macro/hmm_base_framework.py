"""
HMM策略基础类 - 统一架构
HMM Strategy Base Class - Unified Architecture

为所有HMM策略提供统一的基础设施，确保apple to apple比较
"""

import numpy as np
import pandas as pd
import yfinance as yf
from hmmlearn.hmm import GaussianHMM
from sklearn.preprocessing import StandardScaler
import matplotlib.pyplot as plt
import warnings
from abc import ABC, abstractmethod
from typing import Dict, List, Tuple, Optional
from datetime import datetime, timedelta

warnings.filterwarnings('ignore')

class HMMStrategyBase(ABC):
    """HMM策略基础类 - 所有策略的统一基础设施"""
    
    # 统一的默认参数
    DEFAULT_PARAMS = {
        'n_components': 3,
        'covariance_type': 'diag',
        'n_iter': 500,
        'random_state': 42,
        'train_start': '2020-01-01',
        'train_end': '2023-12-31', 
        'test_start': '2024-01-01',
        'transaction_cost': 0.001,  # 0.1%
        'risk_free_rate': 0.0
    }
    
    def __init__(self, **kwargs):
        """初始化基础策略"""
        # 合并用户参数和默认参数
        self.params = {**self.DEFAULT_PARAMS, **kwargs}
        
        # 核心组件
        self.hmm_model: Optional[GaussianHMM] = None
        self.scaler = StandardScaler()
        self.data: Optional[pd.DataFrame] = None
        self.features: Optional[pd.DataFrame] = None
        self.states: Optional[np.ndarray] = None
        
        # 结果存储
        self.train_results: Optional[Dict] = None
        self.test_results: Optional[Dict] = None
        self.performance_metrics: Optional[Dict] = None
        
        print(f"🔧 初始化{self.__class__.__name__}")
        print(f"   参数: {self.params}")
    
    # ==================== 数据处理模块 ====================
    
    def download_data(self, symbols: List[str], **kwargs) -> Dict[str, pd.DataFrame]:
        """统一的数据下载方法"""
        print(f"📥 下载数据: {symbols}")
        
        # 使用统一的时间范围
        start_date = kwargs.get('start_date', self.params['train_start'])
        end_date = kwargs.get('end_date', datetime.now().strftime('%Y-%m-%d'))
        
        data = {}
        successful = 0
        
        for symbol in symbols:
            try:
                raw_data = yf.download(
                    symbol, 
                    start=start_date, 
                    end=end_date,
                    progress=False, 
                    auto_adjust=True
                )
                
                if len(raw_data) > 100:  # 确保有足够数据
                    data[symbol] = raw_data
                    successful += 1
                    print(f"   ✅ {symbol}: {len(raw_data)} 条记录")
                else:
                    print(f"   ❌ {symbol}: 数据不足")
                    
            except Exception as e:
                print(f"   ❌ {symbol}: 下载失败 - {str(e)}")
        
        print(f"   📊 成功下载 {successful}/{len(symbols)} 个资产")
        return data
    
    def process_data_frequency(self, data: pd.DataFrame, frequency: str = 'D') -> pd.DataFrame:
        """统一的数据频率处理"""
        if frequency == 'M':
            # 月末数据
            return data.resample('ME').last()
        elif frequency == 'W':
            # 周末数据
            return data.resample('W').last()
        else:
            # 日频数据（默认）
            return data
    
    def create_time_splits(self, data: pd.DataFrame) -> Tuple[pd.DataFrame, pd.DataFrame]:
        """统一的时间分割"""
        train_end = self.params['train_end']
        test_start = self.params['test_start']
        
        train_data = data[data.index <= train_end].copy()
        test_data = data[data.index >= test_start].copy()
        
        print(f"📅 时间分割:")
        print(f"   训练期: {train_data.index[0].date()} 到 {train_data.index[-1].date()} ({len(train_data)} 条)")
        print(f"   测试期: {test_data.index[0].date()} 到 {test_data.index[-1].date()} ({len(test_data)} 条)")
        
        return train_data, test_data
    
    # ==================== 特征工程模块 ====================
    
    @abstractmethod
    def create_features(self, data: Dict[str, pd.DataFrame]) -> pd.DataFrame:
        """抽象方法：子类必须实现特征工程"""
        pass
    
    def validate_features(self, features: pd.DataFrame) -> pd.DataFrame:
        """验证和清理特征数据"""
        print(f"🔍 特征验证:")
        print(f"   原始特征: {features.shape}")
        
        # 移除NaN
        features_clean = features.dropna()
        print(f"   清理后: {features_clean.shape}")
        
        # 检查数据质量
        if len(features_clean) < 100:
            raise ValueError(f"清理后数据不足: {len(features_clean)} < 100")
        
        # 检查特征值分布
        for col in features_clean.columns:
            if features_clean[col].std() == 0:
                print(f"   ⚠️  警告: 特征 {col} 无变化")
        
        return features_clean
    
    # ==================== HMM训练模块 ====================
    
    def train_hmm(self, features: pd.DataFrame) -> np.ndarray:
        """统一的HMM训练方法"""
        print(f"🤖 训练HMM模型:")
        print(f"   特征数量: {len(features.columns)}")
        print(f"   样本数量: {len(features)}")
        
        # 标准化特征
        X = features.values
        X_scaled = self.scaler.fit_transform(X)
        
        # 初始化HMM
        self.hmm_model = GaussianHMM(
            n_components=self.params['n_components'],
            covariance_type=self.params['covariance_type'],
            n_iter=self.params['n_iter'],
            random_state=self.params['random_state'],
            init_params='mc',
            params='stmc'
        )
        
        # 训练模型
        try:
            self.hmm_model.fit(X_scaled)
            convergence_score = self.hmm_model.score(X_scaled)
            print(f"   ✅ 训练完成，收敛分数: {convergence_score:.2f}")
            
            # 预测状态
            states = self.hmm_model.predict(X_scaled)
            print(f"   📊 状态分布: {np.bincount(states)}")
            
            return states
            
        except Exception as e:
            print(f"   ❌ 训练失败: {str(e)}")
            raise
    
    def analyze_states(self, features: pd.DataFrame, states: np.ndarray) -> Dict:
        """分析HMM状态特征"""
        print(f"📈 状态分析:")
        
        analysis = {}
        feature_names = list(features.columns)
        state_means = pd.DataFrame(self.hmm_model.means_, columns=feature_names)
        
        for state in range(self.params['n_components']):
            mask = states == state
            state_data = features[mask]
            
            if len(state_data) > 0:
                analysis[state] = {
                    'frequency': len(state_data) / len(features),
                    'duration': len(state_data),
                    'means': state_means.iloc[state].to_dict(),
                    'feature_stats': state_data.describe().to_dict()
                }
                
                print(f"   状态 {state}: {len(state_data)} 次 ({analysis[state]['frequency']:.1%})")
        
        return analysis
    
    # ==================== 策略实现模块 ====================
    
    @abstractmethod
    def generate_signals(self, features: pd.DataFrame, states: np.ndarray) -> pd.DataFrame:
        """抽象方法：子类必须实现信号生成"""
        pass
    
    def apply_transaction_costs(self, signals: pd.DataFrame) -> pd.DataFrame:
        """统一的交易成本处理"""
        if 'position' in signals.columns:
            # 计算仓位变化
            signals['position_change'] = signals['position'].diff().abs()
            signals['transaction_cost'] = signals['position_change'] * self.params['transaction_cost']
            signals.loc[0, 'transaction_cost'] = 0  # 第一期无成本
        
        return signals
    
    # ==================== 回测评估模块 ====================
    
    def backtest_strategy(self, signals: pd.DataFrame, price_data: pd.DataFrame) -> Dict:
        """统一的回测方法"""
        print(f"📊 回测分析:")
        
        # 调试信息
        print(f"   信号数据形状: {signals.shape}")
        print(f"   价格数据形状: {price_data.shape}")
        
        # 确保数据对齐
        common_index = signals.index.intersection(price_data.index)
        signals_aligned = signals.loc[common_index].copy()
        price_aligned = price_data.loc[common_index].copy()
        
        print(f"   对齐后数据长度: {len(common_index)}")
        
        # 计算收益 - 确保返回Series
        if 'Close' in price_aligned.columns:
            returns = price_aligned['Close'].pct_change().fillna(0)
        else:
            # 如果只有一列，直接使用
            if price_aligned.shape[1] == 1:
                returns = price_aligned.iloc[:, 0].pct_change().fillna(0)
            else:
                returns = price_aligned.pct_change().iloc[:, 0].fillna(0)
        
        # 策略收益计算 - 确保返回Series
        if 'position' in signals_aligned.columns:
            positions = signals_aligned['position'].shift(1).fillna(0)
        else:
            positions = signals_aligned['signal'].shift(1).fillna(0)
        
        # 确保两个都是Series才进行运算
        if isinstance(positions, pd.Series) and isinstance(returns, pd.Series):
            strategy_returns = (positions * returns).fillna(0)
        else:
            # 如果有DataFrame，需要对齐运算
            strategy_returns = pd.Series(index=common_index, dtype=float)
            for i, idx in enumerate(common_index):
                pos_val = positions.iloc[i] if isinstance(positions, pd.Series) else positions.loc[idx].iloc[0]
                ret_val = returns.iloc[i] if isinstance(returns, pd.Series) else returns.loc[idx]
                strategy_returns.iloc[i] = pos_val * ret_val
            strategy_returns = strategy_returns.fillna(0)
        
        # 扣除交易成本
        if 'transaction_cost' in signals_aligned.columns:
            strategy_returns = strategy_returns - signals_aligned['transaction_cost'].fillna(0)
        
        # 基准收益 - 确保是Series
        if isinstance(returns, pd.Series):
            benchmark_returns = returns
        elif isinstance(returns, pd.DataFrame):
            benchmark_returns = returns.iloc[:, 0]  # 取第一列
        else:
            benchmark_returns = pd.Series(returns, index=common_index)
        
        # 确保所有数据都是Series  
        if not isinstance(strategy_returns, pd.Series):
            if isinstance(strategy_returns, pd.DataFrame):
                strategy_returns = strategy_returns.iloc[:, 0]
            else:
                strategy_returns = pd.Series(strategy_returns, index=common_index)
        
        if not isinstance(benchmark_returns, pd.Series):
            if isinstance(benchmark_returns, pd.DataFrame):
                benchmark_returns = benchmark_returns.iloc[:, 0]
            else:
                benchmark_returns = pd.Series(benchmark_returns, index=common_index)
        
        # 构建结果
        results = pd.DataFrame({
            'strategy_return': strategy_returns,
            'benchmark_return': benchmark_returns,
            'strategy_cumulative': (1 + strategy_returns).cumprod(),
            'benchmark_cumulative': (1 + benchmark_returns).cumprod()
        }, index=common_index)
        
        results = results.fillna(0)
        
        print(f"   回测期间: {len(results)} 期")
        print(f"   策略累积收益: {(results['strategy_cumulative'].iloc[-1] - 1) * 100:.2f}%")
        print(f"   基准累积收益: {(results['benchmark_cumulative'].iloc[-1] - 1) * 100:.2f}%")
        
        return results
    
    def calculate_performance_metrics(self, backtest_results: pd.DataFrame) -> Dict:
        """统一的性能指标计算"""
        strategy_returns = backtest_results['strategy_return'].dropna()
        benchmark_returns = backtest_results['benchmark_return'].dropna()
        
        def _calc_metrics(returns, name):
            total_return = (1 + returns).prod() - 1
            annualized_return = (1 + returns.mean()) ** 252 - 1
            volatility = returns.std() * np.sqrt(252)
            sharpe = (annualized_return - self.params['risk_free_rate']) / volatility if volatility > 0 else 0
            
            # 最大回撤
            cumulative = (1 + returns).cumprod()
            rolling_max = cumulative.expanding().max()
            drawdown = (cumulative - rolling_max) / rolling_max
            max_drawdown = drawdown.min()
            
            return {
                f'{name}_total_return': total_return,
                f'{name}_annualized_return': annualized_return,
                f'{name}_volatility': volatility,
                f'{name}_sharpe': sharpe,
                f'{name}_max_drawdown': max_drawdown,
                f'{name}_win_rate': (returns > 0).mean()
            }
        
        # 计算指标
        metrics = {}
        metrics.update(_calc_metrics(strategy_returns, 'strategy'))
        metrics.update(_calc_metrics(benchmark_returns, 'benchmark'))
        
        # 超额收益
        metrics['excess_return'] = metrics['strategy_total_return'] - metrics['benchmark_total_return']
        metrics['excess_annualized'] = metrics['strategy_annualized_return'] - metrics['benchmark_annualized_return']
        
        # 信息比率
        excess_returns = strategy_returns - benchmark_returns
        metrics['information_ratio'] = excess_returns.mean() / excess_returns.std() * np.sqrt(252) if excess_returns.std() > 0 else 0
        
        return metrics
    
    # ==================== 可视化模块 ====================
    
    def plot_results(self, backtest_results: pd.DataFrame, title: str = "Strategy Performance"):
        """统一的结果可视化"""
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        
        # 1. 累积收益
        ax1 = axes[0, 0]
        ax1.plot(backtest_results.index, backtest_results['strategy_cumulative'], 
                label='Strategy', linewidth=2, alpha=0.8)
        ax1.plot(backtest_results.index, backtest_results['benchmark_cumulative'], 
                label='Benchmark', linewidth=2, alpha=0.8)
        ax1.set_title('Cumulative Returns')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # 2. 超额收益
        ax2 = axes[0, 1]
        excess_returns = (backtest_results['strategy_cumulative'] / backtest_results['benchmark_cumulative'] - 1) * 100
        ax2.plot(backtest_results.index, excess_returns, color='green', linewidth=2)
        ax2.axhline(y=0, color='red', linestyle='--', alpha=0.5)
        ax2.set_title('Excess Returns (%)')
        ax2.grid(True, alpha=0.3)
        
        # 3. 滚动夏普比率
        ax3 = axes[1, 0]
        window = min(60, len(backtest_results) // 4)
        if window > 10:
            strategy_rolling_sharpe = (backtest_results['strategy_return'].rolling(window).mean() / 
                                     backtest_results['strategy_return'].rolling(window).std() * np.sqrt(252))
            benchmark_rolling_sharpe = (backtest_results['benchmark_return'].rolling(window).mean() / 
                                      backtest_results['benchmark_return'].rolling(window).std() * np.sqrt(252))
            ax3.plot(backtest_results.index, strategy_rolling_sharpe, label='Strategy', alpha=0.8)
            ax3.plot(backtest_results.index, benchmark_rolling_sharpe, label='Benchmark', alpha=0.8)
            ax3.set_title(f'Rolling Sharpe Ratio ({window}d)')
            ax3.legend()
            ax3.grid(True, alpha=0.3)
        
        # 4. 回撤分析
        ax4 = axes[1, 1]
        strategy_cumulative = backtest_results['strategy_cumulative']
        strategy_running_max = strategy_cumulative.expanding().max()
        strategy_drawdown = (strategy_cumulative - strategy_running_max) / strategy_running_max * 100
        
        ax4.fill_between(backtest_results.index, strategy_drawdown, 0, alpha=0.3, color='red')
        ax4.plot(backtest_results.index, strategy_drawdown, color='red', linewidth=1)
        ax4.set_title('Strategy Drawdown (%)')
        ax4.grid(True, alpha=0.3)
        
        plt.suptitle(title, fontsize=16)
        plt.tight_layout()
        plt.show()
    
    # ==================== 主执行流程 ====================
    
    def run_strategy(self) -> Dict:
        """运行完整策略流程"""
        try:
            print(f"\n🚀 开始运行{self.__class__.__name__}")
            print("=" * 60)
            
            # 1. 数据准备
            raw_data = self.download_data(self.get_required_symbols())
            
            # 2. 特征工程
            self.features = self.create_features(raw_data)
            self.features = self.validate_features(self.features)
            
            # 3. 时间分割
            train_features, test_features = self.create_time_splits(self.features)
            
            # 4. HMM训练
            train_states = self.train_hmm(train_features)
            
            # 5. 状态分析
            state_analysis = self.analyze_states(train_features, train_states)
            
            # 6. 信号生成
            train_signals = self.generate_signals(train_features, train_states)
            train_signals = self.apply_transaction_costs(train_signals)
            
            # 7. 样本内回测
            train_backtest = self.backtest_strategy(train_signals, self.get_price_data(raw_data, 'train'))
            train_performance = self.calculate_performance_metrics(train_backtest)
            
            # 8. 样本外测试
            if len(test_features) > 0:
                test_X = self.scaler.transform(test_features.values)
                test_states = self.hmm_model.predict(test_X)
                test_signals = self.generate_signals(test_features, test_states)
                test_signals = self.apply_transaction_costs(test_signals)
                test_backtest = self.backtest_strategy(test_signals, self.get_price_data(raw_data, 'test'))
                test_performance = self.calculate_performance_metrics(test_backtest)
            else:
                test_performance = {}
                test_backtest = pd.DataFrame()
            
            # 9. 结果汇总
            results = {
                'strategy_name': self.__class__.__name__,
                'train_performance': train_performance,
                'test_performance': test_performance,
                'train_backtest': train_backtest,
                'test_backtest': test_backtest,
                'state_analysis': state_analysis,
                'parameters': self.params
            }
            
            # 10. 性能报告
            self.print_performance_report(results)
            
            # 11. 可视化
            if len(test_backtest) > 0:
                self.plot_results(test_backtest, f"{self.__class__.__name__} - Out of Sample")
            self.plot_results(train_backtest, f"{self.__class__.__name__} - In Sample")
            
            return results
            
        except Exception as e:
            print(f"❌ 策略执行失败: {str(e)}")
            raise
    
    def print_performance_report(self, results: Dict):
        """打印性能报告"""
        print(f"\n📋 {results['strategy_name']} 性能报告")
        print("=" * 50)
        
        # 样本内性能
        train_perf = results['train_performance']
        print(f"📈 样本内性能 (训练期):")
        print(f"   总收益:     {train_perf['strategy_total_return']:>8.2%}")
        print(f"   年化收益:   {train_perf['strategy_annualized_return']:>8.2%}")
        print(f"   夏普比率:   {train_perf['strategy_sharpe']:>8.3f}")
        print(f"   最大回撤:   {train_perf['strategy_max_drawdown']:>8.2%}")
        print(f"   vs 基准超额: {train_perf['excess_return']:>8.2%}")
        
        # 样本外性能
        if results['test_performance']:
            test_perf = results['test_performance']
            print(f"\n🔮 样本外性能 (测试期):")
            print(f"   总收益:     {test_perf['strategy_total_return']:>8.2%}")
            print(f"   年化收益:   {test_perf['strategy_annualized_return']:>8.2%}")
            print(f"   夏普比率:   {test_perf['strategy_sharpe']:>8.3f}")
            print(f"   最大回撤:   {test_perf['strategy_max_drawdown']:>8.2%}")
            print(f"   vs 基准超额: {test_perf['excess_return']:>8.2%}")
            print(f"   信息比率:   {test_perf['information_ratio']:>8.3f}")
    
    # ==================== 抽象方法 ====================
    
    @abstractmethod
    def get_required_symbols(self) -> List[str]:
        """抽象方法：子类必须实现所需资产列表"""
        pass
    
    @abstractmethod  
    def get_price_data(self, raw_data: Dict[str, pd.DataFrame], period: str) -> pd.DataFrame:
        """抽象方法：子类必须实现价格数据提取"""
        pass
