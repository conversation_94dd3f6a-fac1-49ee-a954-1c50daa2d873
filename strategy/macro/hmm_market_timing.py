"""
HMM市场择时策略 - 重构优化版
HMM Market Timing Strategy - Refactored & Optimized

基于统一架构的SPY择时策略，专注于市场环境识别和择时效果优化
继承HMMStrategyBase，确保与其他策略的公平比较
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from typing import Dict, List
import sys
import os

# 导入基础类
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
import importlib.util
spec = importlib.util.spec_from_file_location("hmm_strategy_base", "hmm_base_framework.py")
base_module = importlib.util.module_from_spec(spec)
spec.loader.exec_module(base_module)
HMMStrategyBase = base_module.HMMStrategyBase

class HMMMarketTimingStrategy(HMMStrategyBase):
    """
    HMM市场择时策略
    
    核心思想：
    1. 使用SPY价格和VIX恐慌指数识别市场环境
    2. 基于市场环境进行择时交易
    3. 牛市环境做多，熊市环境做空/空仓，中性环境减仓
    """
    
    def __init__(self, **kwargs):
        """初始化择时策略"""
        # 择时策略特有的默认参数
        timing_defaults = {
            'strategy_type': 'market_timing',
            'target_asset': 'SPY',
            'fear_indicator': '^VIX',
            'lookback_volatility': 10,
            'position_sizing': 'adaptive',  # adaptive, fixed, kelly
            'max_position': 1.0,
            'min_position': 0.0,
            'neutral_position': 0.5
        }
        
        # 合并参数
        combined_params = {**timing_defaults, **kwargs}
        super().__init__(**combined_params)
        
        print(f"🎯 目标: {self.params['target_asset']} 择时交易")
        print(f"📊 恐慌指标: {self.params['fear_indicator']}")
    
    def get_required_symbols(self) -> List[str]:
        """获取所需的资产代码"""
        return [self.params['target_asset'], self.params['fear_indicator']]
    
    def create_features(self, data: Dict[str, pd.DataFrame]) -> pd.DataFrame:
        """
        创建择时特征
        
        特征设计思路：
        1. log_return: 对数收益率，捕捉价格变化
        2. volatility: 滚动波动率，反映市场不确定性
        3. vix_change: VIX变化率，反映恐慌情绪变化
        """
        print(f"🔧 创建择时特征...")
        
        spy_data = data[self.params['target_asset']]
        vix_data = data[self.params['fear_indicator']]
        
        # 确保数据对齐
        common_index = spy_data.index.intersection(vix_data.index)
        spy_aligned = spy_data.loc[common_index]['Close']
        vix_aligned = vix_data.loc[common_index]['Close']
        
        # 创建特征DataFrame
        features = pd.DataFrame(index=common_index)
        
        # 1. 价格动量特征
        features['log_return'] = np.log(spy_aligned / spy_aligned.shift(1))
        features['price_momentum_5d'] = spy_aligned.pct_change(5)
        features['price_momentum_20d'] = spy_aligned.pct_change(20)
        
        # 2. 波动率特征
        lookback = self.params['lookback_volatility']
        features['volatility'] = features['log_return'].rolling(lookback).std() * np.sqrt(252)
        features['volatility_ratio'] = features['volatility'] / features['volatility'].rolling(60).mean()
        
        # 3. 恐慌情绪特征
        features['vix_level'] = vix_aligned
        features['vix_change'] = vix_aligned.pct_change()
        features['vix_momentum'] = vix_aligned.pct_change(5)
        
        # 4. 技术指标特征
        # RSI
        delta = spy_aligned.diff()
        gain = (delta.where(delta > 0, 0)).rolling(14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(14).mean()
        rs = gain / loss
        features['rsi'] = 100 - (100 / (1 + rs))
        
        # 移动平均偏离度
        ma_20 = spy_aligned.rolling(20).mean()
        ma_50 = spy_aligned.rolling(50).mean()
        features['ma_ratio'] = spy_aligned / ma_20 - 1
        features['ma_slope'] = (ma_20 - ma_20.shift(5)) / ma_20.shift(5)
        
        # 5. 市场结构特征
        features['trend_strength'] = abs(features['price_momentum_20d'])
        features['market_stress'] = features['volatility'] * features['vix_change'].abs()
        
        print(f"   ✅ 创建 {len(features.columns)} 个特征")
        print(f"   📊 特征列表: {list(features.columns)}")
        
        return features
    
    def generate_signals(self, features: pd.DataFrame, states: np.ndarray) -> pd.DataFrame:
        """
        生成择时信号
        
        信号设计思路：
        1. 分析各状态的收益特征
        2. 基于状态特征设计择时逻辑
        3. 支持自适应仓位管理
        """
        print(f"🎯 生成择时信号...")
        
        signals = pd.DataFrame(index=features.index)
        signals['state'] = states
        
        # 分析各状态的特征
        state_analysis = self._analyze_state_characteristics(features, states)
        
        # 基于状态分析设计信号
        signals['raw_position'] = 0.5  # 默认中性仓位
        
        for state, characteristics in state_analysis.items():
            mask = states == state
            
            if characteristics['avg_return'] > 0.001:  # 日收益率 > 0.1%
                if characteristics['sharpe'] > 0.5:
                    # 优质牛市环境：满仓
                    position = self.params['max_position']
                    signal_type = "强多头"
                else:
                    # 一般牛市环境：加仓
                    position = 0.8
                    signal_type = "中多头"
            elif characteristics['avg_return'] < -0.001:  # 日收益率 < -0.1%
                # 熊市环境：空仓或做空
                position = self.params['min_position']
                signal_type = "空仓"
            else:
                # 中性环境：轻仓
                position = self.params['neutral_position']
                signal_type = "中性"
            
            signals.loc[mask, 'raw_position'] = position
            
            print(f"   状态 {state}: {signal_type} (仓位 {position*100:.0f}%)")
            print(f"      平均收益: {characteristics['avg_return']*100:.3f}%")
            print(f"      夏普比率: {characteristics['sharpe']:.3f}")
            print(f"      出现频率: {characteristics['frequency']:.1%}")
        
        # 应用择时优化
        signals['position'] = self._optimize_timing_signals(
            signals['raw_position'], features, states
        )
        
        # 添加额外信息
        signals['signal_change'] = signals['position'].diff().abs()
        signals['position_strength'] = abs(signals['position'] - 0.5) * 2  # 0-1
        
        print(f"   📊 信号统计:")
        print(f"      平均仓位: {signals['position'].mean():.2f}")
        print(f"      仓位变化次数: {(signals['signal_change'] > 0.1).sum()}")
        print(f"      最大仓位: {signals['position'].max():.2f}")
        print(f"      最小仓位: {signals['position'].min():.2f}")
        
        return signals
    
    def _analyze_state_characteristics(self, features: pd.DataFrame, states: np.ndarray) -> Dict:
        """分析各状态的市场特征"""
        analysis = {}
        
        returns = features['log_return'] if 'log_return' in features.columns else features.iloc[:, 0]
        
        for state in range(self.params['n_components']):
            mask = states == state
            state_data = features[mask]
            state_returns = returns[mask]
            
            if len(state_returns) > 5:  # 至少5个观测
                analysis[state] = {
                    'frequency': len(state_data) / len(features),
                    'avg_return': state_returns.mean(),
                    'volatility': state_returns.std(),
                    'sharpe': state_returns.mean() / state_returns.std() * np.sqrt(252) if state_returns.std() > 0 else 0,
                    'max_drawdown': self._calculate_max_drawdown(state_returns),
                    'win_rate': (state_returns > 0).mean(),
                    'avg_volatility': state_data['volatility'].mean() if 'volatility' in state_data.columns else 0,
                    'avg_vix': state_data['vix_level'].mean() if 'vix_level' in state_data.columns else 0
                }
            else:
                # 数据不足的状态
                analysis[state] = {
                    'frequency': 0,
                    'avg_return': 0,
                    'volatility': 0,
                    'sharpe': 0,
                    'max_drawdown': 0,
                    'win_rate': 0.5,
                    'avg_volatility': 0,
                    'avg_vix': 0
                }
        
        return analysis
    
    def _calculate_max_drawdown(self, returns: pd.Series) -> float:
        """计算最大回撤"""
        if len(returns) == 0:
            return 0
        
        cumulative = (1 + returns).cumprod()
        rolling_max = cumulative.expanding().max()
        drawdown = (cumulative - rolling_max) / rolling_max
        return drawdown.min()
    
    def _optimize_timing_signals(self, raw_positions: pd.Series, features: pd.DataFrame, states: np.ndarray) -> pd.Series:
        """
        优化择时信号
        
        优化策略：
        1. 平滑处理：避免过度频繁交易
        2. 趋势确认：结合技术指标确认信号
        3. 风险管理：动态调整仓位
        """
        optimized = raw_positions.copy()
        
        # 1. 平滑处理：移动平均
        optimized = optimized.rolling(window=3, min_periods=1).mean()
        
        # 2. 趋势确认
        if 'ma_ratio' in features.columns and 'rsi' in features.columns:
            # 当价格明显偏离均线时调整仓位
            trend_adjustment = features['ma_ratio'].fillna(0) * 0.2  # 最大调整20%
            trend_adjustment = trend_adjustment.clip(-0.2, 0.2)
            
            # RSI过度偏离时减仓
            rsi_adjustment = 0
            extreme_rsi = (features['rsi'] > 80) | (features['rsi'] < 20)
            rsi_adjustment = np.where(extreme_rsi, -0.1, 0)  # 极端RSI时减仓10%
            
            optimized = optimized + trend_adjustment + rsi_adjustment
        
        # 3. 仓位约束
        optimized = optimized.clip(
            self.params['min_position'], 
            self.params['max_position']
        )
        
        # 4. 最小变化阈值：避免微小调整
        min_change = 0.05  # 5%
        for i in range(1, len(optimized)):
            change = abs(optimized.iloc[i] - optimized.iloc[i-1])
            if change < min_change:
                optimized.iloc[i] = optimized.iloc[i-1]
        
        return optimized
    
    def get_price_data(self, raw_data: Dict[str, pd.DataFrame], period: str) -> pd.DataFrame:
        """获取价格数据用于回测"""
        spy_data = raw_data[self.params['target_asset']]
        
        if period == 'train':
            filtered_data = spy_data[spy_data.index <= self.params['train_end']]
        else:
            filtered_data = spy_data[spy_data.index >= self.params['test_start']]
            
        # 直接返回包含Close列的DataFrame
        return filtered_data[['Close']]
    
    def run_enhanced_analysis(self) -> Dict:
        """运行增强版分析，包含择时特有的评估"""
        # 运行基础分析
        results = self.run_strategy()
        
        # 添加择时特有的分析
        timing_analysis = self._analyze_timing_effectiveness(results)
        results['timing_analysis'] = timing_analysis
        
        # 打印择时分析报告
        self._print_timing_report(timing_analysis)
        
        return results
    
    def _analyze_timing_effectiveness(self, results: Dict) -> Dict:
        """分析择时有效性"""
        timing_analysis = {}
        
        if 'test_backtest' in results and len(results['test_backtest']) > 0:
            test_data = results['test_backtest']
            
            # 择时准确率
            correct_timing = ((test_data['strategy_return'] > 0) & (test_data['benchmark_return'] > 0)) | \
                           ((test_data['strategy_return'] < 0) & (test_data['benchmark_return'] < 0))
            timing_accuracy = correct_timing.mean()
            
            # 上涨市场捕获率
            up_market = test_data['benchmark_return'] > 0
            up_capture = (test_data.loc[up_market, 'strategy_return'].sum() / 
                         test_data.loc[up_market, 'benchmark_return'].sum()) if up_market.sum() > 0 else 0
            
            # 下跌市场保护率
            down_market = test_data['benchmark_return'] < 0
            down_protection = 1 - (test_data.loc[down_market, 'strategy_return'].sum() / 
                                  test_data.loc[down_market, 'benchmark_return'].sum()) if down_market.sum() > 0 else 0
            
            timing_analysis = {
                'timing_accuracy': timing_accuracy,
                'up_market_capture': up_capture,
                'down_market_protection': down_protection,
                'net_timing_value': up_capture * 0.6 + down_protection * 0.4  # 综合择时价值
            }
        
        return timing_analysis
    
    def _print_timing_report(self, timing_analysis: Dict):
        """打印择时分析报告"""
        if timing_analysis:
            print(f"\n🎯 择时效果分析:")
            print(f"   择时准确率:     {timing_analysis['timing_accuracy']:>8.1%}")
            print(f"   上涨市场捕获:   {timing_analysis['up_market_capture']:>8.1%}")
            print(f"   下跌市场保护:   {timing_analysis['down_market_protection']:>8.1%}")
            print(f"   综合择时价值:   {timing_analysis['net_timing_value']:>8.1%}")
            
            # 择时评级
            net_value = timing_analysis['net_timing_value']
            if net_value > 0.8:
                rating = "优秀 ⭐⭐⭐"
            elif net_value > 0.6:
                rating = "良好 ⭐⭐"
            elif net_value > 0.4:
                rating = "一般 ⭐"
            else:
                rating = "较差 ❌"
            
            print(f"   择时能力评级:   {rating}")

    def plot_enhanced_timing_analysis(self, results: Dict):
        """
        创建增强版择时分析图表
        集成原始简化版市场择时脚本中的三个图表为一个综合图表
        """
        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'SimHei', 'DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False
        
        fig, axes = plt.subplots(2, 2, figsize=(20, 12))
        
        # 图1: 训练期累积收益对比 (左上)
        ax1 = axes[0, 0]
        if 'train_backtest' in results and len(results['train_backtest']) > 0:
            train_data = results['train_backtest']
            ax1.plot(train_data.index, train_data['strategy_cumulative'], 
                    label='HMM Strategy', color='blue', linewidth=2)
            ax1.plot(train_data.index, train_data['benchmark_cumulative'], 
                    label='Buy and Hold (SPY)', color='red', linewidth=2)
            ax1.set_title(f'Training Period Performance ({self.params["train_start"]} - {self.params["train_end"]})', 
                         fontsize=12, fontweight='bold')
            ax1.set_ylabel('Cumulative Return')
            ax1.legend()
            ax1.grid(True, alpha=0.3)
            
            # 添加性能标注
            final_strategy = train_data['strategy_cumulative'].iloc[-1]
            final_benchmark = train_data['benchmark_cumulative'].iloc[-1]
            ax1.text(0.02, 0.95, f'Strategy: {(final_strategy-1)*100:.1f}%\nBenchmark: {(final_benchmark-1)*100:.1f}%', 
                    transform=ax1.transAxes, bbox=dict(boxstyle="round", facecolor='wheat', alpha=0.8),
                    verticalalignment='top', fontsize=9)
        
        # 图2: 样本外测试期累积收益对比 (右上)
        ax2 = axes[0, 1]
        if 'test_backtest' in results and len(results['test_backtest']) > 0:
            test_data = results['test_backtest']
            ax2.plot(test_data.index, test_data['strategy_cumulative'], 
                    label='HMM Strategy', color='blue', linewidth=2)
            ax2.plot(test_data.index, test_data['benchmark_cumulative'], 
                    label='Buy and Hold (SPY)', color='red', linewidth=2)
            ax2.set_title(f'Out-of-Sample Performance ({self.params["test_start"]} - Present)', 
                         fontsize=12, fontweight='bold')
            ax2.set_ylabel('Cumulative Return')
            ax2.legend()
            ax2.grid(True, alpha=0.3)
            
            # 添加性能标注
            final_strategy = test_data['strategy_cumulative'].iloc[-1]
            final_benchmark = test_data['benchmark_cumulative'].iloc[-1]
            ax2.text(0.02, 0.95, f'Strategy: {(final_strategy-1)*100:.1f}%\nBenchmark: {(final_benchmark-1)*100:.1f}%', 
                    transform=ax2.transAxes, bbox=dict(boxstyle="round", facecolor='lightgreen', alpha=0.8),
                    verticalalignment='top', fontsize=9)
        
        # 图3: 价格与HMM状态叠加图 (左下)
        ax3 = axes[1, 0]
        if 'test_backtest' in results and len(results['test_backtest']) > 0 and hasattr(self, 'features'):
            # 获取测试期数据和状态
            test_data = results['test_backtest']
            
            # 重新计算状态用于可视化
            test_features = self.features[self.features.index >= self.params['test_start']]
            if len(test_features) > 0:
                X_test = self.scaler.transform(test_features.values)
                test_states = self.hmm_model.predict(X_test)
                
                # 获取价格数据
                price_data = test_data.index.to_series().map(lambda x: test_data.loc[x, 'benchmark_cumulative'] if x in test_data.index else None).dropna()
                
                # 绘制价格线
                ax3.plot(test_data.index, test_data['benchmark_cumulative'], 
                        color='black', linewidth=2, label='SPY Price')
                
                # 为不同状态添加背景色
                state_colors = {0: 'lightgreen', 1: 'lightcoral', 2: 'lightyellow'}
                state_names = {0: 'Bull State', 1: 'Bear State', 2: 'Neutral State'}
                
                # 添加状态背景
                for state in range(self.params['n_components']):
                    if state < len(test_states):
                        state_mask = (test_states == state)
                        if state_mask.sum() > 0:
                            state_indices = test_features.index[state_mask]
                            for idx in state_indices:
                                if idx in test_data.index:
                                    y_val = test_data.loc[idx, 'benchmark_cumulative']
                                    ax3.axvspan(idx, idx, alpha=0.3, color=state_colors.get(state, 'lightgray'))
                
                ax3.set_title('SPY Price with HMM Market States', fontsize=12, fontweight='bold')
                ax3.set_ylabel('Price/Cumulative Return')
                ax3.legend()
                ax3.grid(True, alpha=0.3)
        
        # 图4: 策略性能指标雷达图 (右下)
        ax4 = axes[1, 1]
        if 'test_performance' in results and results['test_performance']:
            # 创建雷达图
            from matplotlib.patches import Patch
            ax4.remove()  # 移除原来的坐标系
            ax4 = fig.add_subplot(2, 2, 4, projection='polar')
            
            # 性能指标
            metrics = ['Annualized Return', 'Sharpe Ratio', 'Information Ratio', 'Win Rate', 'Drawdown Control']
            perf = results['test_performance']
            
            # 标准化指标到0-1范围
            values = [
                min(perf.get('strategy_annualized_return', 0) / 0.3, 1),  # 30%作为满分
                min(perf.get('strategy_sharpe', 0) / 2, 1),  # 2作为满分
                min((perf.get('information_ratio', 0) + 1) / 2, 1) if perf.get('information_ratio') else 0.5,  # -1到1映射到0-1
                perf.get('strategy_win_rate', 0.5),  # 胜率
                min(1 + perf.get('strategy_max_drawdown', -0.2) / 0.2, 1)  # 20%回撤作为0分
            ]
            
            angles = np.linspace(0, 2 * np.pi, len(metrics), endpoint=False).tolist()
            values += values[:1]  # 闭合
            angles += angles[:1]  # 闭合
            
            ax4.plot(angles, values, 'o-', linewidth=2, color='blue', label='HMM Strategy')
            ax4.fill(angles, values, alpha=0.25, color='blue')
            ax4.set_xticks(angles[:-1])
            ax4.set_xticklabels(metrics, fontsize=10)
            ax4.set_ylim(0, 1)
            ax4.set_title('Strategy Performance Radar', fontsize=12, fontweight='bold', pad=20)
            ax4.grid(True)
        
        plt.suptitle('HMM Market Timing Strategy - Comprehensive Analysis Report', fontsize=16, fontweight='bold', y=0.98)
        plt.tight_layout()
        plt.subplots_adjust(top=0.94)
        plt.show()
        
        # 创建第二个图：状态转换和信号分析
        self._plot_state_transition_analysis(results)
    
    def _plot_state_transition_analysis(self, results: Dict):
        """绘制状态转换和信号分析图"""
        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'SimHei', 'DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False
        
        fig, axes = plt.subplots(2, 1, figsize=(16, 10))
        
        # 图1: 状态转换时间序列
        ax1 = axes[0]
        if hasattr(self, 'features') and hasattr(self, 'hmm_model'):
            test_features = self.features[self.features.index >= self.params['test_start']]
            if len(test_features) > 0:
                X_test = self.scaler.transform(test_features.values)
                test_states = self.hmm_model.predict(X_test)
                
                # 绘制状态时间序列
                ax1.plot(test_features.index, test_states, 'o-', linewidth=1, markersize=3, alpha=0.7)
                ax1.set_ylabel('HMM States')
                ax1.set_title('HMM State Transition Time Series', fontsize=12, fontweight='bold')
                ax1.grid(True, alpha=0.3)
                ax1.set_ylim(-0.5, self.params['n_components']-0.5)
        
        # 图2: 仓位变化和收益分析
        ax2 = axes[1]
        if 'test_backtest' in results and len(results['test_backtest']) > 0:
            test_data = results['test_backtest']
            
            # 假设有仓位数据（如果有的话）
            ax2_twin = ax2.twinx()
            
            # 绘制策略收益
            ax2.plot(test_data.index, test_data['strategy_return'], 
                    color='blue', alpha=0.6, linewidth=1, label='Strategy Daily Return')
            ax2.axhline(y=0, color='gray', linestyle='--', alpha=0.5)
            ax2.set_ylabel('Daily Return', color='blue')
            ax2.tick_params(axis='y', labelcolor='blue')
            
            # 绘制累积收益比较
            ax2_twin.plot(test_data.index, test_data['strategy_cumulative'], 
                         color='green', linewidth=2, label='Strategy Cumulative')
            ax2_twin.plot(test_data.index, test_data['benchmark_cumulative'], 
                         color='red', linewidth=2, label='Benchmark Cumulative')
            ax2_twin.set_ylabel('Cumulative Return', color='green')
            ax2_twin.tick_params(axis='y', labelcolor='green')
            
            ax2.set_title('Market Timing Signal Effectiveness Analysis', fontsize=12, fontweight='bold')
            ax2.grid(True, alpha=0.3)
            
            # 合并图例
            lines1, labels1 = ax2.get_legend_handles_labels()
            lines2, labels2 = ax2_twin.get_legend_handles_labels()
            ax2.legend(lines1 + lines2, labels1 + labels2, loc='upper left')
        
        plt.tight_layout()
        plt.show()

    def run_strategy_with_enhanced_plots(self) -> Dict:
        """运行策略并生成增强版图表"""
        # 运行基础策略
        results = self.run_enhanced_analysis()
        
        # 生成集成的可视化图表
        if results:
            print(f"\n📊 生成增强版择时分析图表...")
            self.plot_enhanced_timing_analysis(results)
        
        return results


def main():
    """主函数：运行择时策略"""
    print("🚀 启动HMM市场择时策略")
    print("=" * 60)
    
    # 创建择时策略实例
    strategy = HMMMarketTimingStrategy(
        n_components=3,
        lookback_volatility=10,
        position_sizing='adaptive',
        max_position=1.0,
        min_position=0.0,
        neutral_position=0.5
    )
    
    try:
        # 运行增强版分析并生成图表
        results = strategy.run_strategy_with_enhanced_plots()
        
        print("\n🎯 择时策略执行完成！")
        
        # 返回结果用于进一步分析
        return results
        
    except Exception as e:
        print(f"❌ 择时策略执行失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return None


if __name__ == "__main__":
    main()
