#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SPY HMM V5 简化版 - 严格无前视偏差但计算高效
================================================

基于V4，实现严格无前视偏差的回测框架，但采用更实际的重训频率：
1. 季度重训 - 避免过度频繁训练
2. 递归状态映射 - 只在重训时进行
3. 严格特征计算 - 确保无未来信息泄露
4. 实际交易模拟 - 考虑执行延迟

"""

import pandas as pd
import numpy as np
import yfinance as yf
import matplotlib.pyplot as plt
from sklearn.preprocessing import StandardScaler
from hmmlearn.hmm import GaussianHMM
import warnings
import datetime

warnings.filterwarnings('ignore')

def download_and_prepare_features_v5(symbols, start_date="2000-01-01", end_date=None):
    """V5特征工程 - 严格无前视偏差"""
    if end_date is None:
        end_date = datetime.datetime.today().strftime('%Y-%m-%d')
    
    print(f"📊 下载数据: {symbols}")
    
    data = yf.download(symbols, start=start_date, end=end_date, progress=False)['Close']
    data.rename(columns={
        symbols[0]: 'price', symbols[1]: 'vix', symbols[2]: 'tnx', 
        symbols[3]: 'rsp', symbols[4]: 'hyg', symbols[5]: 'ief'
    }, inplace=True)
    
    if data.empty:
        raise ValueError("数据下载失败")
    
    print("🔧 计算特征 (严格历史数据)...")
    
    # 基础特征
    data['log_return'] = np.log(data['price'] / data['price'].shift(1))
    data['volatility'] = data['log_return'].rolling(window=21, min_periods=21).std() * np.sqrt(252)
    
    # MA距离特征
    data['dist_from_ma55'] = data['price'] / data['price'].rolling(window=55, min_periods=55).mean() - 1
    data['dist_from_ma233'] = data['price'] / data['price'].rolling(window=233, min_periods=233).mean() - 1
    data['dist_from_ma200'] = data['price'] / data['price'].rolling(window=200, min_periods=200).mean() - 1
    
    # 其他特征
    data['tnx_change'] = data['tnx'].diff()
    data['momentum_5d'] = data['price'].pct_change(5)
    data['momentum_20d'] = data['price'].pct_change(20)
    data['market_breadth'] = data['rsp'] / data['price']
    data['credit_risk_ratio'] = data['hyg'] / data['ief']
    
    feature_columns = [
        'log_return', 'volatility', 'vix', 'tnx_change',
        'dist_from_ma55', 'dist_from_ma233', 'dist_from_ma200',
        'momentum_5d', 'momentum_20d', 'market_breadth', 'credit_risk_ratio'
    ]
    
    feature_df = data[feature_columns].dropna()
    
    return data[['price', 'log_return']].loc[feature_df.index], feature_df

def train_hmm_model(features_df, n_states_range=[2, 3, 4]):
    """训练HMM模型"""
    scaler = StandardScaler()
    X = scaler.fit_transform(features_df)
    
    best_model = None
    best_bic = np.inf
    
    for n_states in n_states_range:
        try:
            model = GaussianHMM(n_components=n_states, covariance_type="full", 
                              n_iter=1000, random_state=42)
            model.fit(X)
            bic = model.bic(X)
            
            if bic < best_bic:
                best_bic = bic
                best_model = model
        except:
            continue
    
    return best_model, scaler

def calculate_historical_state_performance(historical_data, model, scaler, features_cols):
    """计算历史状态表现 - 严格使用历史数据"""
    try:
        features = historical_data[features_cols].dropna()
        if len(features) < 252:
            return {}
        
        X = scaler.transform(features)
        states = model.predict(X)
        
        # 计算每个状态的历史表现
        state_performance = {}
        for state_id in range(model.n_components):
            state_mask = (states == state_id)
            if state_mask.sum() < 20:
                continue
            
            state_returns = features.loc[features.index[state_mask], 'log_return']
            state_vols = features.loc[features.index[state_mask], 'volatility']
            state_vix = features.loc[features.index[state_mask], 'vix']
            
            # 综合评分
            avg_return = float(state_returns.mean())
            avg_vol = float(state_vols.mean()) 
            avg_vix = float(state_vix.mean())
            
            # 风险调整收益评分
            return_score = avg_return * 1000
            risk_penalty = -avg_vol * 5
            vix_penalty = -max(0, avg_vix - 20) * 2
            
            total_score = return_score + risk_penalty + vix_penalty
            
            state_performance[state_id] = {
                'score': total_score,
                'return': avg_return,
                'vol': avg_vol,
                'vix': avg_vix,
                'frequency': float(state_mask.sum() / len(states))
            }
        
        return state_performance
    
    except Exception as e:
        print(f"历史表现计算失败: {e}")
        return {}

def map_states_to_regimes(state_performance):
    """基于历史表现映射状态到交易状态"""
    if not state_performance:
        return {}, {}
    
    # 按得分排序
    sorted_states = sorted(state_performance.items(), key=lambda x: x[1]['score'])
    
    state_map = {}
    position_map = {}
    
    n_states = len(sorted_states)
    
    if n_states == 2:
        state_map[sorted_states[0][0]] = "Bear Market"
        state_map[sorted_states[1][0]] = "Bull Market"
        position_map = {"Bear Market": 0.0, "Bull Market": 1.0}
    elif n_states == 3:
        state_map[sorted_states[0][0]] = "Bear Market"
        state_map[sorted_states[1][0]] = "Transition"
        state_map[sorted_states[2][0]] = "Bull Market"
        position_map = {"Bear Market": 0.0, "Transition": 0.8, "Bull Market": 1.0}
    else:
        # 4个或更多状态
        for idx, (state_id, perf) in enumerate(sorted_states):
            if idx == 0:
                state_map[state_id] = "Bear Market"
                position_map["Bear Market"] = 0.0
            elif idx == n_states - 1:
                state_map[state_id] = "Bull Market"
                position_map["Bull Market"] = 1.0
            else:
                label = f"Transition_{idx}"
                state_map[state_id] = label
                position_map[label] = 0.5 + 0.4 * (idx / (n_states - 1))
    
    # 打印映射结果
    print("   📊 状态映射:")
    for state_id, regime in state_map.items():
        perf = state_performance[state_id]
        position = position_map[regime]
        print(f"     状态{state_id} -> {regime} (仓位{position:.1%})")
        print(f"       收益:{perf['return']:.6f}, 波动:{perf['vol']:.4f}, "
              f"VIX:{perf['vix']:.1f}, 得分:{perf['score']:.2f}")
    
    return state_map, position_map

def run_v5_backtest(price_data, feature_data, start_date, end_date):
    """运行V5严格回测"""
    print(f"\n🚀 开始SPY HMM V5回测 (严格无前视偏差)")
    
    backtest_start = pd.to_datetime(start_date)
    backtest_end = pd.to_datetime(end_date)
    
    # 筛选回测数据
    full_data = price_data.join(feature_data.drop(columns=['log_return']))
    backtest_data = full_data.loc[backtest_start:backtest_end]
    
    results = []
    current_model = None
    current_scaler = None
    current_state_map = {}
    current_position_map = {}
    last_retrain_date = None
    
    print(f"回测期间: {backtest_data.index[0].date()} 到 {backtest_data.index[-1].date()}")
    
    for i, current_date in enumerate(backtest_data.index):
        
        # 获取历史数据 (不包含当天)
        historical_data = full_data.loc[:current_date].iloc[:-1]
        
        if len(historical_data) < 756:  # 需要至少3年历史数据
            continue
        
        # 检查是否需要重新训练 (季度重训)
        should_retrain = (
            current_model is None or
            last_retrain_date is None or
            (current_date - last_retrain_date).days >= 63  # 约3个月
        )
        
        if should_retrain and i % 50 == 0:  # 减少重训频率
            print(f"   📚 在{current_date.date()}重新训练模型...")
            
            try:
                # 使用历史数据训练模型
                train_features = historical_data[feature_data.columns.tolist()]
                train_features = train_features.dropna()
                
                if len(train_features) < 756:
                    continue
                
                # 训练HMM模型
                model, scaler = train_hmm_model(train_features)
                if model is None:
                    continue
                
                # 计算历史状态表现
                state_perf = calculate_historical_state_performance(
                    historical_data, model, scaler, feature_data.columns.tolist()
                )
                
                if not state_perf:
                    continue
                
                # 映射状态
                state_map, position_map = map_states_to_regimes(state_perf)
                
                if not state_map:
                    continue
                
                # 更新当前模型和映射
                current_model = model
                current_scaler = scaler
                current_state_map = state_map
                current_position_map = position_map
                last_retrain_date = current_date
                
            except Exception as e:
                print(f"     模型训练失败: {e}")
                continue
        
        # 使用当前模型进行预测
        if current_model is not None and current_scaler is not None:
            try:
                # 获取当日特征
                current_features = backtest_data.loc[[current_date], feature_data.columns.tolist()]
                if current_features.isnull().any().any():
                    continue
                
                # 预测状态
                X_current = current_scaler.transform(current_features)
                predicted_state = current_model.predict(X_current)[0]
                
                # 获取交易信号
                regime = current_state_map.get(predicted_state, "Unknown")
                position = current_position_map.get(regime, 0.5)
                
                results.append({
                    'date': current_date,
                    'regime': regime,
                    'position': position,
                    'predicted_state': predicted_state,
                    'log_return': backtest_data.loc[current_date, 'log_return']
                })
                
            except Exception as e:
                continue
    
    if not results:
        raise ValueError("无法生成交易信号")
    
    results_df = pd.DataFrame(results)
    results_df.set_index('date', inplace=True)
    
    print(f"✅ 生成了 {len(results_df)} 个交易信号")
    return results_df

def calculate_performance(results_df):
    """计算策略表现"""
    # 策略收益
    results_df['trade'] = results_df['position'].diff().fillna(0).abs()
    results_df['strategy_return'] = (
        results_df['position'].shift(1).fillna(0) * results_df['log_return']
    )
    results_df['strategy_return'] -= results_df['trade'] * 0.0001  # 交易成本
    
    # 累计收益
    results_df['strategy_cumulative'] = np.exp(results_df['strategy_return'].cumsum())
    results_df['buy_and_hold_cumulative'] = np.exp(results_df['log_return'].cumsum())
    
    # 年化指标
    days_in_year = 252
    
    hmm_annual_return = np.exp(results_df['strategy_return'].mean() * days_in_year) - 1
    hmm_annual_vol = results_df['strategy_return'].std() * np.sqrt(days_in_year)
    hmm_sharpe = hmm_annual_return / hmm_annual_vol if hmm_annual_vol != 0 else 0
    
    bnh_annual_return = np.exp(results_df['log_return'].mean() * days_in_year) - 1
    bnh_annual_vol = results_df['log_return'].std() * np.sqrt(days_in_year)
    bnh_sharpe = bnh_annual_return / bnh_annual_vol if bnh_annual_vol != 0 else 0
    
    # 最大回撤
    hmm_cum_max = results_df['strategy_cumulative'].cummax()
    hmm_drawdown = (results_df['strategy_cumulative'] - hmm_cum_max) / hmm_cum_max
    hmm_max_drawdown = hmm_drawdown.min()
    
    bnh_cum_max = results_df['buy_and_hold_cumulative'].cummax()
    bnh_drawdown = (results_df['buy_and_hold_cumulative'] - bnh_cum_max) / bnh_cum_max
    bnh_max_drawdown = bnh_drawdown.min()
    
    return {
        'hmm_annual_return': hmm_annual_return,
        'hmm_annual_vol': hmm_annual_vol, 
        'hmm_sharpe': hmm_sharpe,
        'hmm_max_drawdown': hmm_max_drawdown,
        'bnh_annual_return': bnh_annual_return,
        'bnh_annual_vol': bnh_annual_vol,
        'bnh_sharpe': bnh_sharpe,
        'bnh_max_drawdown': bnh_max_drawdown,
        'results_df': results_df
    }

def main():
    """主函数"""
    print("🚀 SPY HMM V5 简化版 - 严格无前视偏差")
    print("="*55)
    print("特点:")
    print("  ✅ 季度重训 - 实用高效")  
    print("  ✅ 历史状态分析 - 无未来信息")
    print("  ✅ 严格特征工程 - 时间序列正确性")
    print("  ✅ 真实交易模拟")
    print("="*55)
    
    try:
        # 配置
        SYMBOLS = ["SPY", "^VIX", "^TNX", "RSP", "HYG", "IEF"]
        DATA_START_DATE = "2000-01-01"
        BACKTEST_START_DATE = "2010-01-01"
        BACKTEST_END_DATE = datetime.datetime.today().strftime('%Y-%m-%d')
        
        # 数据准备
        price_data, feature_data = download_and_prepare_features_v5(
            SYMBOLS, DATA_START_DATE, BACKTEST_END_DATE
        )
        
        # 运行回测
        results_df = run_v5_backtest(
            price_data, feature_data, BACKTEST_START_DATE, BACKTEST_END_DATE
        )
        
        # 计算性能
        performance = calculate_performance(results_df)
        
        # 结果展示
        print("\n" + "="*70)
        print("SPY HMM V5简化版 - 严格无前视偏差回测结果")
        print(f"期间: {results_df.index[0].date()} 到 {results_df.index[-1].date()}")
        print("="*70)
        print(f"{'指标':<25} | {'V5简化版':<15} | {'买入持有':<15}")
        print("-"*55)
        print(f"{'年化收益率':<25} | {performance['hmm_annual_return']:14.2%} | {performance['bnh_annual_return']:14.2%}")
        print(f"{'年化波动率':<25} | {performance['hmm_annual_vol']:14.2%} | {performance['bnh_annual_vol']:14.2%}")
        print(f"{'夏普比率':<25} | {performance['hmm_sharpe']:14.2f} | {performance['bnh_sharpe']:14.2f}")
        print(f"{'最大回撤':<25} | {performance['hmm_max_drawdown']:14.2%} | {performance['bnh_max_drawdown']:14.2%}")
        print("="*70)
        
        # V4对比
        v4_return = 0.1365
        v4_sharpe = 1.04
        v4_drawdown = -0.2172
        
        print(f"\n📊 V5简化版 vs V4对比:")
        print(f"   收益率差异: {performance['hmm_annual_return'] - v4_return:+.2%}")
        print(f"   夏普比率差异: {performance['hmm_sharpe'] - v4_sharpe:+.2f}")
        print(f"   最大回撤差异: {performance['hmm_max_drawdown'] - v4_drawdown:+.2%}")
        
        # 策略统计
        regime_dist = results_df['regime'].value_counts()
        print(f"\n📈 市场状态分布:")
        for regime, count in regime_dist.items():
            pct = count / len(results_df) * 100
            print(f"   {regime}: {count}天 ({pct:.1f}%)")
        
        print(f"\n💰 平均仓位: {results_df['position'].mean():.1%}")
        
        # 绘制图表
        plt.style.use('seaborn-v0_8-darkgrid')
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(15, 10))
        
        # 累计收益对比
        ax1.plot(results_df.index, results_df['strategy_cumulative'], 
                label='V5简化版', color='blue', linewidth=2)
        ax1.plot(results_df.index, results_df['buy_and_hold_cumulative'], 
                label='买入持有', color='red', linestyle='--', linewidth=2)
        ax1.set_title('SPY HMM V5简化版 - 累计收益对比', fontsize=14, fontweight='bold')
        ax1.set_ylabel('累计收益')
        ax1.legend()
        ax1.grid(True)
        
        # 仓位变化
        ax2.plot(results_df.index, results_df['position'], 
                color='green', alpha=0.7, linewidth=1)
        ax2.fill_between(results_df.index, 0, results_df['position'], 
                        color='green', alpha=0.3)
        ax2.set_title('仓位变化', fontsize=12)
        ax2.set_ylabel('仓位比例')
        ax2.set_xlabel('日期')
        ax2.grid(True)
        
        plt.tight_layout()
        filename = f"spy_hmm_v5_simplified_{datetime.datetime.now().strftime('%Y%m%d')}.png"
        plt.savefig(filename, dpi=300, bbox_inches='tight')
        print(f"\n📊 图表已保存: {filename}")
        plt.close()
        
        # 严格性验证
        print(f"\n✅ V5简化版严格性验证:")
        print(f"   ✅ 特征计算: 只使用历史滚动窗口")
        print(f"   ✅ 模型训练: 季度重训，只用历史数据")
        print(f"   ✅ 状态映射: 基于历史表现，无未来信息")
        print(f"   ✅ 预测决策: 严格时间顺序")
        
        if performance['hmm_sharpe'] > v4_sharpe:
            print(f"\n🏆 成功! V5简化版夏普比率超越V4: {performance['hmm_sharpe']:.2f} vs {v4_sharpe:.2f}")
        else:
            print(f"\n📊 V5简化版夏普比率: {performance['hmm_sharpe']:.2f} (V4: {v4_sharpe:.2f})")
        
        print(f"\n✅ SPY HMM V5简化版测试完成!")
        
    except Exception as e:
        print(f"❌ 回测失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()