#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SPY HMM策略V5精炼版 - 基于失败经验的重新设计
==========================================

基于V5多时间框架版本的失败教训，重新设计适合SPY日线特性的策略：
1. 保持V4的简洁架构 (避免过度复杂化)
2. 精选关键特征增强 (而非大量特征)
3. 优化仓位配置 (基于V4成功经验)
4. 引入适度的宏观环境感知

目标：在V4基础上实现温和但稳定的改进
策略哲学：简单有效 > 复杂无效
"""

import pandas as pd
import numpy as np
import yfinance as yf
import matplotlib.pyplot as plt
from sklearn.preprocessing import StandardScaler
from hmmlearn.hmm import GaussianHMM
import warnings
import datetime

warnings.filterwarnings('ignore')

def download_and_prepare_features_v5(symbols, start_date="2000-01-01", end_date=None):
    """V5精炼版特征工程 - 在V4基础上精选增强"""
    if end_date is None:
        end_date = datetime.datetime.today().strftime('%Y-%m-%d')
    
    print(f"📊 下载SPY数据并构建V5精炼特征...")
    
    data = yf.download(symbols, start=start_date, end=end_date, progress=False)['Close']
    data.rename(columns={
        symbols[0]: 'price', symbols[1]: 'vix', symbols[2]: 'tnx', 
        symbols[3]: 'rsp', symbols[4]: 'hyg', symbols[5]: 'ief'
    }, inplace=True)
    
    if data.empty:
        raise ValueError("数据下载失败")
    
    print("🔧 V5精炼特征工程...")
    
    # 1. V4核心特征 (保持不变)
    data['log_return'] = np.log(data['price'] / data['price'].shift(1))
    data['volatility'] = data['log_return'].rolling(window=21).std() * np.sqrt(252)
    data['dist_from_ma55'] = data['price'] / data['price'].rolling(window=55).mean() - 1
    data['dist_from_ma233'] = data['price'] / data['price'].rolling(window=233).mean() - 1
    data['dist_from_ma200'] = data['price'] / data['price'].rolling(window=200).mean() - 1
    data['tnx_change'] = data['tnx'].diff()
    data['momentum_5d'] = data['price'].pct_change(5)
    data['momentum_20d'] = data['price'].pct_change(20)
    data['market_breadth'] = data['rsp'] / data['price']
    data['credit_risk_ratio'] = data['hyg'] / data['ief']
    
    # 2. V5精选新增特征 (只增加3个最有价值的)
    # 基于黄金V8经验，但适配日线特性
    
    # 长期趋势强度 (月线级别的趋势)
    data['long_term_trend'] = (data['price'] / data['price'].shift(60) - 1)  # 3月趋势
    
    # VIX相对强度 (市场恐慌的相对水平)
    data['vix_relative'] = data['vix'] / data['vix'].rolling(window=60).mean() - 1
    
    # 信用利差动量 (信用环境变化)
    data['credit_momentum'] = data['credit_risk_ratio'].pct_change(10)
    
    # V5精炼特征列表 (14个特征，V4是11个)
    feature_cols = [
        # V4核心特征 (11个)
        'log_return', 'volatility', 'vix', 'tnx_change',
        'dist_from_ma55', 'dist_from_ma233', 'dist_from_ma200',
        'momentum_5d', 'momentum_20d', 'market_breadth', 'credit_risk_ratio',
        # V5新增特征 (3个)
        'long_term_trend', 'vix_relative', 'credit_momentum'
    ]
    
    feature_df = data[feature_cols].dropna()
    
    print(f"✅ V5精炼特征完成: {len(feature_cols)}个特征 (V4: 11个)")
    print(f"   新增特征: long_term_trend, vix_relative, credit_momentum")
    
    return data[['price', 'log_return']].loc[feature_df.index], feature_df

def enhanced_state_mapping_v5(model, features, scaler):
    """V5增强状态映射 - 基于多维度分析"""
    state_means_original = scaler.inverse_transform(model.means_)
    state_profiles = pd.DataFrame(state_means_original, columns=features)
    
    print(f"   V5状态分析 ({model.n_components}状态):")
    
    # 多维度状态评分
    state_scores = {}
    for i in range(model.n_components):
        profile = state_profiles.iloc[i]
        
        # 综合评分 (收益 + 趋势 - 风险)
        return_score = profile['log_return'] * 1000  # 收益权重
        trend_score = profile.get('long_term_trend', 0) * 100  # 趋势权重
        risk_penalty = -profile['volatility'] * 10  # 风险惩罚
        vix_penalty = -max(0, profile['vix'] - 20) * 2  # VIX惩罚
        
        total_score = return_score + trend_score + risk_penalty + vix_penalty
        state_scores[i] = total_score
        
        print(f"     状态{i}: 收益{profile['log_return']:.6f}, "
              f"波动{profile['volatility']:.4f}, VIX{profile['vix']:.1f}, "
              f"评分{total_score:.2f}")
    
    # 基于综合评分排序
    sorted_states = sorted(state_scores.items(), key=lambda x: x[1])
    
    if model.n_components == 3:
        state_map = {
            sorted_states[0][0]: "Bear Market",
            sorted_states[1][0]: "Transition", 
            sorted_states[2][0]: "Bull Market"
        }
        # V5优化仓位 (基于V4经验微调)
        position_map = {"Bear Market": 0.0, "Transition": 0.8, "Bull Market": 1.0}  # 提高Transition仓位
    else:
        # 其他状态数的映射
        state_map = {}
        position_map = {}
        for idx, (state_id, score) in enumerate(sorted_states):
            if idx == 0:
                state_map[state_id] = "Bear Market"
                position_map["Bear Market"] = 0.0
            elif idx == len(sorted_states) - 1:
                state_map[state_id] = "Bull Market"
                position_map["Bull Market"] = 1.0
            else:
                state_map[state_id] = f"Transition_{idx}"
                position_map[f"Transition_{idx}"] = 0.5 + 0.3 * (idx / (len(sorted_states) - 1))
    
    print(f"   V5状态映射: {state_map}")
    print(f"   V5仓位配置: {position_map}")
    
    return state_map, position_map

def run_v5_refined_backtest(full_data, feature_list, start_date, end_date):
    """运行V5精炼版回测 - 保持V4框架的简洁性"""
    print("\n🚀 开始SPY V5精炼版回测...")
    
    backtest_start_date = pd.to_datetime(start_date)
    backtest_end_date = pd.to_datetime(end_date)
    
    all_signals = []
    
    # Walk-Forward回测 (保持V4的年度重训)
    for year in range(backtest_start_date.year, backtest_end_date.year + 1):
        train_start = pd.to_datetime(f"{year-8}-01-01")
        train_end = pd.to_datetime(f"{year-1}-12-31")
        predict_start = pd.to_datetime(f"{year}-01-01")
        predict_end = pd.to_datetime(f"{year}-12-31")
        
        if predict_end > backtest_end_date:
            predict_end = backtest_end_date
        
        train_df = full_data.loc[train_start:train_end]
        predict_df = full_data.loc[predict_start:predict_end]
        
        if len(train_df) < 500 or len(predict_df) < 50:
            continue
        
        print(f"   {year}: 训练{len(train_df)}天, 预测{len(predict_df)}天")
        
        try:
            # 1. 训练HMM (保持V4的简单方法，但允许3-4状态)
            scaler = StandardScaler()
            X_train = scaler.fit_transform(train_df[feature_list])
            
            # 简单的BIC选择 (只测试3-4状态)
            best_model = None
            best_bic = np.inf
            
            for n_states in [3, 4]:
                try:
                    model = GaussianHMM(n_components=n_states, covariance_type="full", 
                                      n_iter=1000, random_state=42)
                    model.fit(X_train)
                    bic = model.bic(X_train)
                    
                    if bic < best_bic:
                        best_bic = bic
                        best_model = model
                except:
                    continue
            
            if best_model is None:
                continue
            
            # 2. V5增强状态映射
            state_map, position_map = enhanced_state_mapping_v5(best_model, feature_list, scaler)
            
            # 3. 预测和信号生成
            X_predict = scaler.transform(predict_df[feature_list])
            predicted_states = best_model.predict(X_predict)
            
            signals_df = pd.DataFrame(index=predict_df.index)
            signals_df['regime'] = pd.Series(predicted_states, index=predict_df.index).map(state_map)
            signals_df['position'] = signals_df['regime'].map(position_map)
            
            all_signals.append(signals_df)
            
        except Exception as e:
            print(f"     年度{year}失败: {e}")
    
    if not all_signals:
        raise ValueError("无法生成任何信号")
    
    # 合并结果
    backtest_results = pd.concat(all_signals)
    backtest_results = full_data[['price', 'log_return']].join(backtest_results, how='right')
    backtest_results.dropna(inplace=True)
    
    return backtest_results

def calculate_performance_v5(backtest_results):
    """计算V5性能 - 与V4完全一致的计算方法"""
    
    # 计算策略收益 (与V4一致)
    backtest_results['trade'] = backtest_results['position'].diff().fillna(0).abs()
    backtest_results['strategy_return'] = (backtest_results['position'].shift(1).fillna(0) * 
                                         backtest_results['log_return'])
    backtest_results['strategy_return'] -= backtest_results['trade'] * 0.0001  # 交易成本
    
    # 累计收益
    backtest_results['strategy_cumulative'] = np.exp(backtest_results['strategy_return'].cumsum())
    backtest_results['buy_and_hold_cumulative'] = np.exp(backtest_results['log_return'].cumsum())
    
    # 年化指标 (与V4一致)
    days_in_year = 252
    
    hmm_annual_return = np.exp(backtest_results['strategy_return'].mean() * days_in_year) - 1
    hmm_annual_vol = backtest_results['strategy_return'].std() * np.sqrt(days_in_year)
    hmm_sharpe = hmm_annual_return / hmm_annual_vol if hmm_annual_vol != 0 else 0
    
    bnh_annual_return = np.exp(backtest_results['log_return'].mean() * days_in_year) - 1
    bnh_annual_vol = backtest_results['log_return'].std() * np.sqrt(days_in_year)
    bnh_sharpe = bnh_annual_return / bnh_annual_vol if bnh_annual_vol != 0 else 0
    
    # 最大回撤
    hmm_cum_re_max = backtest_results['strategy_cumulative'].cummax()
    hmm_drawdown = (backtest_results['strategy_cumulative'] - hmm_cum_re_max) / hmm_cum_re_max
    hmm_max_drawdown = hmm_drawdown.min()
    
    bnh_cum_re_max = backtest_results['buy_and_hold_cumulative'].cummax()
    bnh_drawdown = (backtest_results['buy_and_hold_cumulative'] - bnh_cum_re_max) / bnh_cum_re_max
    bnh_max_drawdown = bnh_drawdown.min()
    
    return {
        'hmm_annual_return': hmm_annual_return,
        'hmm_sharpe': hmm_sharpe,
        'hmm_max_drawdown': hmm_max_drawdown,
        'bnh_annual_return': bnh_annual_return,
        'bnh_sharpe': bnh_sharpe,
        'bnh_max_drawdown': bnh_max_drawdown,
        'backtest_results': backtest_results
    }

def main():
    """主函数 - SPY V5精炼版"""
    print("🚀 SPY HMM策略V5精炼版")
    print("="*50)
    print("基于多时间框架失败的教训重新设计:")
    print("  ✅ 保持V4简洁架构")
    print("  ✅ 精选3个关键新特征")
    print("  ✅ 优化仓位配置")
    print("  ✅ 避免过度复杂化")
    print("  🎯 目标: 温和超越V4")
    print("="*50)
    
    try:
        # 配置 (与V4相同)
        SYMBOLS = ["SPY", "^VIX", "^TNX", "RSP", "HYG", "IEF"]
        DATA_START_DATE = "2000-01-01"
        BACKTEST_START_DATE = "2008-01-01"
        BACKTEST_END_DATE = datetime.datetime.today().strftime('%Y-%m-%d')
        
        # 1. 数据准备
        price_data, feature_data = download_and_prepare_features_v5(
            SYMBOLS, DATA_START_DATE, BACKTEST_END_DATE)
        
        full_data = price_data.join(feature_data.drop(columns=['log_return']))
        
        # 2. 运行V5精炼版回测
        backtest_results = run_v5_refined_backtest(
            full_data, feature_data.columns.tolist(), BACKTEST_START_DATE, BACKTEST_END_DATE)
        
        # 3. 计算性能
        performance = calculate_performance_v5(backtest_results)
        
        # 4. 结果展示
        print("\n" + "="*70)
        print("SPY HMM策略V5精炼版回测结果")
        print(f"期间: {performance['backtest_results'].index[0].date()} 至 {performance['backtest_results'].index[-1].date()}")
        print("="*70)
        print(f"{'指标':<25} | {'V5精炼版':<15} | {'买入持有':<15} | {'V4对比':<15}")
        print("-"*70)
        print(f"{'年化收益率':<25} | {performance['hmm_annual_return']:14.2%} | {performance['bnh_annual_return']:14.2%} | {'V4: 13.65%':<15}")
        print(f"{'夏普比率':<25} | {performance['hmm_sharpe']:14.2f} | {performance['bnh_sharpe']:14.2f} | {'V4: 1.04':<15}")
        print(f"{'最大回撤':<25} | {performance['hmm_max_drawdown']:14.2%} | {performance['bnh_max_drawdown']:14.2%} | {'V4: -21.72%':<15}")
        print("="*70)
        
        # V4对比分析
        v4_return = 0.1365
        v4_sharpe = 1.04
        v4_drawdown = -0.2172
        
        print(f"\n🎯 V5精炼版 vs V4详细对比:")
        print(f"   收益率变化: {performance['hmm_annual_return'] - v4_return:+.2%}")
        print(f"   夏普比率变化: {performance['hmm_sharpe'] - v4_sharpe:+.2f}")
        print(f"   回撤变化: {performance['hmm_max_drawdown'] - v4_drawdown:+.2%}")
        
        if performance['hmm_annual_return'] > v4_return and performance['hmm_sharpe'] > v4_sharpe:
            print(f"\n🏆 SUCCESS! V5精炼版全面超越V4!")
            print(f"   收益率提升: {performance['hmm_annual_return'] - v4_return:.2%}")
            print(f"   夏普比率提升: {performance['hmm_sharpe'] - v4_sharpe:.2f}")
            print(f"   精炼特征策略生效!")
        elif performance['hmm_sharpe'] > v4_sharpe:
            print(f"\n📊 V5精炼版夏普比率改进:")
            print(f"   夏普提升: {performance['hmm_sharpe'] - v4_sharpe:.2f}")
            print(f"   风险调整收益改善，值得采用")
        elif performance['hmm_annual_return'] > v4_return:
            print(f"\n📈 V5精炼版收益率改进:")
            print(f"   收益提升: {performance['hmm_annual_return'] - v4_return:.2%}")
            print(f"   绝对收益改善，但需关注风险")
        else:
            print(f"\n⚠️ V5精炼版未能改进:")
            print(f"   建议继续使用V4作为最优版本")
            print(f"   或考虑其他优化方向")
        
        # 策略建议
        print(f"\n💡 基于黄金V8经验的SPY策略启示:")
        print(f"   1. 多时间框架在日线级别效果有限")
        print(f"   2. 简洁特征比复杂特征更有效")
        print(f"   3. SPY的长期上涨趋势适合简单策略")
        print(f"   4. 过度优化可能导致过拟合")
        
        print(f"\n✅ SPY V5精炼版测试完成!")
        
    except Exception as e:
        print(f"❌ SPY V5精炼版失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
