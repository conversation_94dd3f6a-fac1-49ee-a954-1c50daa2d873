#!/usr/bin/env python3
"""
本地数据加载模块
=============
功能: 从本地parquet文件加载股票数据，替代yfinance在线下载
"""

import pandas as pd
import numpy as np
from pathlib import Path
from typing import List, Dict, Optional, Tuple
import warnings

warnings.filterwarnings('ignore')

class LocalDataLoader:
    """本地数据加载器"""
    
    def __init__(self, data_dir: str = "../../data"):
        self.data_dir = Path(data_dir)
    
    def load_symbol_data(self, symbol: str, timeframe: str = "1d", start_date: str = None, end_date: str = None) -> Optional[pd.DataFrame]:
        """加载单个股票数据"""
        data_file = self.data_dir / f"us/{symbol}/{timeframe}.parquet"
        
        if not data_file.exists():
            print(f"警告: 找不到 {symbol} 的数据文件")
            return None
        
        try:
            df = pd.read_parquet(data_file)
            df['timestamp'] = pd.to_datetime(df['timestamp'])
            df = df.set_index('timestamp').sort_index()
            
            # 过滤日期范围
            if start_date:
                df = df[df.index >= start_date]
            if end_date:
                df = df[df.index <= end_date]
            
            return df
        except Exception as e:
            print(f"加载 {symbol} 数据失败: {e}")
            return None
    
    def load_multiple_symbols(self, symbols: List[str], timeframe: str = "1d", start_date: str = None, end_date: str = None) -> pd.DataFrame:
        """加载多个股票的收盘价数据"""
        close_data = {}
        
        for symbol in symbols:
            df = self.load_symbol_data(symbol, timeframe, start_date, end_date)
            if df is not None:
                close_data[symbol] = df['close']
        
        if not close_data:
            raise ValueError("无法加载任何股票数据")
        
        # 合并数据
        result = pd.DataFrame(close_data)
        result = result.dropna()  # 移除任何缺失数据
        
        return result

def load_hmm_data(symbols: List[str], start_date: str = "2000-01-01", end_date: str = None) -> Tuple[pd.DataFrame, pd.DataFrame]:
    """
    为HMM模型加载和预处理数据
    
    Args:
        symbols: 股票代码列表 ["SPY", "^VIX", "^TNX", "RSP", "HYG", "IEF"]
        start_date: 开始日期
        end_date: 结束日期
    
    Returns:
        raw_data: 原始价格数据
        processed_data: 处理后的特征数据
    """
    loader = LocalDataLoader()
    
    print(f"从本地加载数据: {symbols} ({start_date} to {end_date or 'latest'})")
    
    # 加载原始数据
    raw_data = loader.load_multiple_symbols(symbols, start_date, end_date)
    
    if raw_data.empty:
        raise ValueError("无法加载数据，请先运行 data.py 下载数据")
    
    # 重命名列以匹配原有逻辑
    column_map = {
        symbols[0]: 'price',  # SPY
        symbols[1]: 'vix',    # ^VIX  
        symbols[2]: 'tnx',    # ^TNX
        symbols[3]: 'rsp',    # RSP
        symbols[4]: 'hyg',    # HYG
        symbols[5]: 'ief'     # IEF
    }
    
    data = raw_data.rename(columns=column_map)
    
    print(f"成功加载 {len(data)} 条记录，日期范围: {data.index.min().date()} 到 {data.index.max().date()}")
    
    return raw_data, data

def load_macro_data(symbols: List[str], start_date: str, end_date: str) -> pd.DataFrame:
    """加载宏观数据"""
    loader = LocalDataLoader()
    
    print(f"加载宏观数据: {symbols}")
    
    # 获取月度数据（简化处理，使用日数据然后重采样）
    raw_data = loader.load_multiple_symbols(symbols, start_date, end_date)
    
    # 重采样为月度数据
    monthly_data = raw_data.resample('M').last()
    monthly_data.columns = [s.lower() for s in monthly_data.columns]
    
    return monthly_data

if __name__ == "__main__":
    # 测试代码
    symbols = ["SPY", "^VIX", "^TNX", "RSP", "HYG", "IEF"]
    try:
        raw_data, processed_data = load_hmm_data(symbols)
        print("\n数据加载测试成功!")
        print(f"数据形状: {processed_data.shape}")
        print(f"列名: {list(processed_data.columns)}")
        print(f"前5行:")
        print(processed_data.head())
    except Exception as e:
        print(f"测试失败: {e}")