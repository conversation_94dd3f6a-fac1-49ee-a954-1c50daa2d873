import pandas as pd
import numpy as np
import yfinance as yf
import matplotlib.pyplot as plt
from matplotlib.patches import Patch
from sklearn.preprocessing import StandardScaler
from hmmlearn.hmm import GaussianHMM
import warnings
import datetime

warnings.filterwarnings('ignore')

def download_and_prepare_features(symbols, start_date="2000-01-01", end_date=None, vol_window=21, ma_windows=[55, 233], mom_windows=[5, 20]):
    if end_date is None:
        end_date = datetime.datetime.today().strftime('%Y-%m-%d')
    
    print(f"1. Downloading daily data for {symbols} from {start_date} to {end_date}...")
    data = yf.download(symbols, start=start_date, end=end_date, progress=False)['Close']
    data.rename(columns={symbols[0]: 'price', symbols[1]: 'vix', symbols[2]: 'tnx', symbols[3]: 'rsp', symbols[4]: 'hyg', symbols[5]: 'ief'}, inplace=True)

    if data.empty:
        raise ValueError(f"Failed to download data. Please check symbols or date range.")

    print("2. Engineering features for HMM...")
    data['log_return'] = np.log(data['price'] / data['price'].shift(1))
    data['volatility'] = data['log_return'].rolling(window=vol_window).std() * np.sqrt(252)
    
    # New MA features based on your suggestion
    for window in ma_windows:
        data[f'dist_from_ma{window}'] = data['price'] / data['price'].rolling(window=window).mean() - 1
    data['dist_from_ma200'] = data['price'] / data['price'].rolling(window=200).mean() - 1

    data['tnx_change'] = data['tnx'].diff()

    for window in mom_windows:
        data[f'momentum_{window}d'] = data['price'].pct_change(window)

    data['market_breadth'] = data['rsp'] / data['price']
    data['credit_risk_ratio'] = data['hyg'] / data['ief']

    # Consolidate features, replacing the old MA feature with the new ones
    feature_cols = ['log_return', 'volatility', 'vix', 'tnx_change'] + [f'dist_from_ma{w}' for w in ma_windows] + ['dist_from_ma200'] + [f'momentum_{w}d' for w in mom_windows] + ['market_breadth', 'credit_risk_ratio']
    feature_df = data[feature_cols].dropna()
    
    return data[['price', 'log_return']].loc[feature_df.index], feature_df

def find_best_hmm_model(X_train: np.ndarray, n_states_range: range = range(2, 5)):
    best_model, best_bic = None, np.inf
    for n_components in n_states_range:
        try:
            model = GaussianHMM(n_components=n_components, covariance_type="full", n_iter=1000, random_state=42)
            model.fit(X_train)
            bic = model.bic(X_train)
            if bic < best_bic:
                best_bic, best_model = bic, model
        except Exception as e:
            print(f"   Error fitting model with {n_components} states: {e}")
    if best_model is None:
        raise RuntimeError("Failed to train any HMM model.")
    return best_model

def analyze_and_map_states(model: GaussianHMM, features: list, scaler: StandardScaler):
    state_means_original = scaler.inverse_transform(model.means_)
    state_profiles = pd.DataFrame(state_means_original, columns=features)
    
    print(f"   HMM Model chose {model.n_components} states.")
    print("   State Mean Profiles (unscaled):")
    with pd.option_context('display.max_columns', None):
        print(state_profiles)

    # Current simple mapping based on volatility
    bull_state = state_profiles['volatility'].idxmin()
    bear_state = state_profiles['volatility'].idxmax()
    
    state_map = {bull_state: "Bull Market", bear_state: "Bear Market"}
    for i in range(model.n_components):
        if i not in state_map:
            state_map[i] = "Transition" # Default for any other states
    
    print(f"   Initial State Mapping: {state_map}")
    return state_map

def run_backtest(full_data, feature_list, start_date, end_date, transaction_cost=0.0001):
    print("\n--- Starting Walk-Forward Backtest ---")
    
    backtest_start_date = pd.to_datetime(start_date)
    backtest_end_date = pd.to_datetime(end_date)
    
    all_signals = []
    
    # Walk-forward with annual retraining
    for year in range(backtest_start_date.year, backtest_end_date.year + 1):
        train_end_date = f"{year-1}-12-31"
        predict_start_date = f"{year}-01-01"
        predict_end_date = f"{year}-12-31"
        
        print(f"\nTraining for year {year} (data up to {train_end_date})...")
        
        train_df = full_data.loc[:train_end_date]
        predict_df = full_data.loc[predict_start_date:predict_end_date]
        
        if train_df.empty or predict_df.empty or len(train_df) < 252:
            print(f"Skipping year {year} due to insufficient training or prediction data.")
            continue
            
        scaler = StandardScaler()
        X_train = scaler.fit_transform(train_df[feature_list])
        
        hmm_model = find_best_hmm_model(X_train)
        state_map = analyze_and_map_states(hmm_model, feature_list, scaler)
        
        X_predict = scaler.transform(predict_df[feature_list])
        predicted_states = hmm_model.predict(X_predict)
        
        signals_df = pd.DataFrame(index=predict_df.index)
        signals_df['regime'] = pd.Series(predicted_states, index=predict_df.index).map(state_map)
        all_signals.append(signals_df)

    if not all_signals:
        print("Could not generate any signals. Backtest aborted.")
        return

    backtest_results = pd.concat(all_signals)
    
    print("\n--- Calculating Performance Metrics ---")
    
    backtest_results = full_data[['log_return']].join(backtest_results, how='right')
    backtest_results.dropna(inplace=True)

    # Define position based on regime using tiered allocation
    position_map = {'Bull Market': 1.0, 'Transition': 0.75, 'Bear Market': 0.0}
    backtest_results['position'] = backtest_results['regime'].map(position_map)
    
    # Calculate strategy returns including transaction costs
    # Note: Transaction cost is proportional to the change in position size
    backtest_results['trade'] = backtest_results['position'].diff().fillna(0).abs()
    backtest_results['strategy_return'] = backtest_results['position'].shift(1).fillna(0) * backtest_results['log_return']
    backtest_results['strategy_return'] -= backtest_results['trade'] * transaction_cost
    
    backtest_results['strategy_cumulative'] = np.exp(backtest_results['strategy_return'].cumsum())
    backtest_results['buy_and_hold_cumulative'] = np.exp(backtest_results['log_return'].cumsum())
    
    days_in_year = 252
    
    hmm_annual_return = np.exp(backtest_results['strategy_return'].mean() * days_in_year) - 1
    hmm_annual_vol = backtest_results['strategy_return'].std() * np.sqrt(days_in_year)
    hmm_sharpe = hmm_annual_return / hmm_annual_vol if hmm_annual_vol != 0 else 0
    
    bnh_annual_return = np.exp(backtest_results['log_return'].mean() * days_in_year) - 1
    bnh_annual_vol = backtest_results['log_return'].std() * np.sqrt(days_in_year)
    bnh_sharpe = bnh_annual_return / bnh_annual_vol if bnh_annual_vol != 0 else 0
    
    hmm_cum_re_max = backtest_results['strategy_cumulative'].cummax()
    hmm_drawdown = (backtest_results['strategy_cumulative'] - hmm_cum_re_max) / hmm_cum_re_max
    hmm_max_drawdown = hmm_drawdown.min()
    
    bnh_cum_re_max = backtest_results['buy_and_hold_cumulative'].cummax()
    bnh_drawdown = (backtest_results['buy_and_hold_cumulative'] - bnh_cum_re_max) / bnh_cum_re_max
    bnh_max_drawdown = bnh_drawdown.min()

    print("\n" + "="*50)
    print("Backtest Results")
    print(f"Period: {backtest_results.index[0].date()} to {backtest_results.index[-1].date()}")
    print("="*50)
    print(f"{'Metric':<25} | {'HMM Strategy':<15} | {'Buy and Hold':<15}")
    print("-"*55)
    print(f"{'Annualized Return':<25} | {hmm_annual_return:14.2%} | {bnh_annual_return:14.2%}")
    print(f"{'Annualized Volatility':<25} | {hmm_annual_vol:14.2%} | {bnh_annual_vol:14.2%}")
    print(f"{'Sharpe Ratio':<25} | {hmm_sharpe:14.2f} | {bnh_sharpe:14.2f}")
    print(f"{'Maximum Drawdown':<25} | {hmm_max_drawdown:14.2%} | {bnh_max_drawdown:14.2%}")
    print("="*55)

    plt.style.use('seaborn-v0_8-darkgrid')
    fig, ax = plt.subplots(figsize=(18, 9))
    ax.plot(backtest_results.index, backtest_results['strategy_cumulative'], label='HMM Strategy', color='royalblue', lw=2)
    ax.plot(backtest_results.index, backtest_results['buy_and_hold_cumulative'], label='Buy and Hold', color='black', lw=2, linestyle='--')
    ax.set_title('HMM Strategy vs. Buy and Hold - Cumulative Returns', fontsize=16, fontweight='bold')
    ax.set_ylabel('Cumulative Growth of $1')
    ax.set_xlabel('Date')
    ax.legend(loc='upper left')
    ax.grid(True)
    plt.tight_layout()
    filename = f"hmm_spy_backtest_plot_{datetime.datetime.now().strftime('%Y%m%d')}.png"
    plt.savefig(filename)
    print(f"\nBacktest chart saved to {filename}")
    plt.close()

if __name__ == '__main__':
    # --- Configuration for Experiment A: 55/233 MA ---
    SYMBOLS = ["SPY", "^VIX", "^TNX", "RSP", "HYG", "IEF"]
    DATA_START_DATE = "2000-01-01"
    BACKTEST_START_DATE = "2008-01-01"
    BACKTEST_END_DATE = datetime.datetime.today().strftime('%Y-%m-%d')
    
    # Feature list for Experiment B: All-in-One
    FEATURE_LIST = ['log_return', 'volatility', 'vix', 'tnx_change', 'dist_from_ma55', 'dist_from_ma233', 'dist_from_ma200', 'momentum_5d', 'momentum_20d', 'market_breadth', 'credit_risk_ratio']
    
    price_data, feature_data = download_and_prepare_features(
        symbols=SYMBOLS, 
        start_date=DATA_START_DATE, 
        end_date=BACKTEST_END_DATE
    )
    
    full_data_for_backtest = price_data.join(feature_data.drop(columns=['log_return']))

    run_backtest(full_data_for_backtest, FEATURE_LIST, BACKTEST_START_DATE, BACKTEST_END_DATE)
