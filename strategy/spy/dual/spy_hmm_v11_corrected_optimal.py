#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SPY HMM Strategy V11 - 最优无前瞻偏差版本
==========================================

基于V10进一步优化：
1. 月频更新宏观状态（而非年频）
2. 优化的滚动窗口策略
3. 更严格的时间验证
4. 真实的样本外性能评估

核心改进：
- 宏观模型每月更新，使用前60个月数据
- 战术模型每年更新，使用所有历史数据
- 双重验证确保无前瞻偏差

作者：Leon
版本：V11 Optimal Corrected
日期：2025年8月29日
"""

import pandas as pd
import numpy as np
import yfinance as yf
import matplotlib.pyplot as plt
from sklearn.preprocessing import StandardScaler
from hmmlearn.hmm import GaussianHMM
import warnings
import datetime
from dateutil.relativedelta import relativedelta

warnings.filterwarnings('ignore')

def download_and_prepare_features(symbols, start_date, end_date):
    """下载数据并进行特征工程"""
    print(f"1. Downloading data for {symbols} from {start_date} to {end_date}...")
    
    data_raw = yf.download(symbols, start=start_date, end=end_date, progress=False)
    data = data_raw['Close'].copy()
    data.columns = [s.lower().replace('^', '') for s in data.columns]
    
    # 特征工程
    data['log_return'] = np.log(data['spy'] / data['spy'].shift(1))
    data['volatility'] = data['log_return'].rolling(window=21).std() * np.sqrt(252)
    data['vix'] = data_raw['Close']['^VIX']
    data['tnx_change'] = data['tnx'].diff()
    
    # 多时间框架移动平均
    for window in [55, 200, 233]:
        data[f'ma_{window}'] = data['spy'].rolling(window=window).mean()
        data[f'dist_from_ma{window}'] = data['spy'] / data[f'ma_{window}'] - 1
    
    # 动量特征
    for window in [5, 20]:
        data[f'momentum_{window}d'] = data['spy'].pct_change(window)
    
    # 市场广度和信用风险
    data['market_breadth'] = data['rsp'] / data['spy']
    data['credit_risk_ratio'] = data['hyg'] / data['ief']
    
    feature_cols = ['log_return', 'volatility', 'vix', 'tnx_change', 'dist_from_ma55', 
                   'dist_from_ma233', 'dist_from_ma200', 'momentum_5d', 'momentum_20d', 
                   'market_breadth', 'credit_risk_ratio']
    
    feature_df = data[feature_cols].dropna()
    price_data = data[['spy', 'log_return']].rename(columns={'spy': 'price'}).loc[feature_df.index]
    
    return data_raw, price_data, feature_df

def find_best_hmm_model(X_train, n_states_range=range(2, 5), max_attempts=3):
    """使用BIC选择最优HMM模型，增强稳定性"""
    best_model, best_bic = None, np.inf
    
    for n_components in n_states_range:
        for attempt in range(max_attempts):
            try:
                model = GaussianHMM(
                    n_components=n_components, 
                    covariance_type="full",
                    n_iter=1000, 
                    random_state=42 + attempt,
                    tol=1e-4
                )
                model.fit(X_train)
                bic = model.bic(X_train)
                
                if bic < best_bic:
                    best_bic, best_model = bic, model
                break  # 成功训练就跳出重试循环
                
            except Exception as e:
                if attempt == max_attempts - 1:
                    print(f"   Failed to fit {n_components} states after {max_attempts} attempts")
                continue
    
    if best_model is None:
        print("   Warning: Using fallback 2-state model")
        # 最后的备用方案
        try:
            best_model = GaussianHMM(n_components=2, covariance_type="diag", 
                                   n_iter=500, random_state=42)
            best_model.fit(X_train)
        except:
            pass
    
    return best_model

def analyze_and_map_states(model, feature_list, scaler):
    """分析HMM状态并映射到市场状态"""
    if model is None:
        return {0: "Bull Market", 1: "Bear Market"}
    
    state_means = scaler.inverse_transform(model.means_)
    
    # 使用log_return进行状态排序
    log_return_idx = feature_list.index('log_return')
    state_returns = [(i, state_means[i, log_return_idx]) for i in range(model.n_components)]
    state_returns.sort(key=lambda x: x[1])  # 按收益率排序
    
    state_map = {}
    if len(state_returns) >= 3:
        state_map[state_returns[0][0]] = "Bear Market"      # 最低收益率
        state_map[state_returns[-1][0]] = "Bull Market"     # 最高收益率
        for i in range(1, len(state_returns) - 1):
            state_map[state_returns[i][0]] = "Transition"   # 中间状态
    else:
        state_map[state_returns[0][0]] = "Bear Market"
        state_map[state_returns[-1][0]] = "Bull Market"
    
    return state_map

def get_macro_regime_monthly(current_date, lookback_months=60):
    """月频更新的宏观状态判断 - 严格无前瞻偏差"""
    current_dt = pd.to_datetime(current_date)
    
    # 计算宏观模型训练窗口
    macro_end_date = current_dt.replace(day=1)  # 月初
    macro_start_date = macro_end_date - relativedelta(months=lookback_months)
    
    try:
        # 下载月频宏观数据
        macro_symbols = ["SPY", "TLT", "GLD"]
        macro_data_raw = yf.download(
            macro_symbols, 
            start=macro_start_date.strftime('%Y-%m-%d'),
            end=macro_end_date.strftime('%Y-%m-%d'), 
            progress=False, 
            interval="1mo"
        )
        
        if macro_data_raw.empty:
            return "Risk-On"
        
        macro_data = macro_data_raw['Close'].copy()
        macro_data.columns = [s.lower() for s in macro_data.columns]
        
        # 宏观特征工程
        macro_data['spy_tlt_ratio'] = macro_data['spy'] / macro_data['tlt']
        macro_data['gld_spy_ratio'] = macro_data['gld'] / macro_data['spy']
        
        # 使用收益率变化作为特征
        macro_features = macro_data[['spy_tlt_ratio', 'gld_spy_ratio']].pct_change().dropna()
        
        if len(macro_features) < 24:  # 至少需要2年数据
            return "Risk-On"
        
        # 训练宏观HMM模型
        scaler = StandardScaler()
        X_macro = scaler.fit_transform(macro_features)
        
        macro_model = GaussianHMM(
            n_components=2, 
            covariance_type="full",
            n_iter=1000, 
            random_state=42,
            tol=1e-4
        )
        macro_model.fit(X_macro)
        
        # 状态映射：SPY/TLT比率高的是Risk-On
        state_means = scaler.inverse_transform(macro_model.means_)
        risk_on_state = np.argmax(state_means[:, 0])
        
        # 预测当前月的状态
        current_features = macro_features.iloc[-1:].values
        if len(current_features) > 0:
            current_features_scaled = scaler.transform(current_features)
            current_regime = macro_model.predict(current_features_scaled)[0]
            return 'Risk-On' if current_regime == risk_on_state else 'Risk-Off'
        else:
            return "Risk-On"
            
    except Exception as e:
        print(f"   Macro model warning: {e}, using default Risk-On")
        return "Risk-On"

def run_backtest_optimal(full_data, feature_list, start_date, end_date, transaction_cost=0.0001):
    """最优的无前瞻偏差回测"""
    print("\n" + "="*70)
    print("STARTING OPTIMAL NO-LOOKAHEAD-BIAS BACKTEST")
    print("="*70)
    
    backtest_start_date = pd.to_datetime(start_date)
    backtest_end_date = pd.to_datetime(end_date)
    
    all_signals = []
    
    # 战术模型：年频更新
    for year in range(backtest_start_date.year, backtest_end_date.year + 1):
        train_end_date = f"{year-1}-12-31"
        predict_start_date = f"{year}-01-01"
        predict_end_date = f"{year}-12-31"
        
        print(f"\n{'='*50}")
        print(f"YEAR {year} - TACTICAL MODEL TRAINING")
        print(f"Training data: up to {train_end_date}")
        print(f"Prediction period: {predict_start_date} to {predict_end_date}")
        print(f"{'='*50}")
        
        # 获取训练和预测数据
        train_df = full_data.loc[:train_end_date]
        predict_df = full_data.loc[predict_start_date:predict_end_date]
        
        if train_df.empty or predict_df.empty or len(train_df) < 252:
            print(f"⚠️  Skipping year {year} - insufficient data (need ≥252 days)")
            continue
        
        print(f"✅ Training samples: {len(train_df)}, Prediction samples: {len(predict_df)}")
        
        # 训练战术HMM模型
        scaler = StandardScaler()
        X_train = scaler.fit_transform(train_df[feature_list])
        
        print("📊 Training tactical HMM model...")
        hmm_model = find_best_hmm_model(X_train)
        
        if hmm_model is None:
            print(f"❌ Failed to train tactical HMM for year {year}")
            continue
        
        state_map = analyze_and_map_states(hmm_model, feature_list, scaler)
        print(f"📈 Tactical states: {state_map}")
        
        # 预测战术状态
        X_predict = scaler.transform(predict_df[feature_list])
        predicted_states = hmm_model.predict(X_predict)
        
        # 月频宏观状态更新
        print("🌍 Updating macro regimes monthly...")
        macro_regimes = []
        
        # 为预测期间的每个月获取宏观状态
        current_date = pd.to_datetime(predict_start_date)
        end_date = pd.to_datetime(predict_end_date)
        
        monthly_macro_states = {}
        while current_date <= end_date:
            month_key = current_date.strftime('%Y-%m')
            if month_key not in monthly_macro_states:
                macro_state = get_macro_regime_monthly(current_date)
                monthly_macro_states[month_key] = macro_state
                print(f"   {month_key}: {macro_state}")
            current_date += relativedelta(months=1)
        
        # 为每个交易日分配宏观状态
        for idx in predict_df.index:
            month_key = idx.strftime('%Y-%m')
            macro_regime = monthly_macro_states.get(month_key, 'Risk-On')
            macro_regimes.append(macro_regime)
        
        # 生成信号
        signals_df = pd.DataFrame(index=predict_df.index)
        signals_df['tactical_regime'] = pd.Series(predicted_states, index=predict_df.index).map(state_map)
        signals_df['macro_regime'] = macro_regimes
        
        all_signals.append(signals_df)
        print(f"✅ Year {year} completed successfully")
    
    if not all_signals:
        print("❌ ERROR: No signals generated!")
        return None
    
    print(f"\n🎯 SIGNAL GENERATION COMPLETED - Processing {len(all_signals)} years")
    
    # 合并所有信号
    backtest_results = pd.concat(all_signals, sort=False)
    backtest_results = full_data[['log_return']].join(backtest_results, how='right')
    backtest_results.dropna(inplace=True)
    
    print(f"📊 Final dataset: {len(backtest_results)} trading days")
    
    # 仓位逻辑
    position_map = {
        'Bull Market': 1.0, 
        'Transition': 0.75, 
        'Bear Market': 0.0
    }
    backtest_results['tactical_position'] = backtest_results['tactical_regime'].map(position_map)
    
    # 宏观过滤逻辑
    def apply_macro_filter(row):
        if row['macro_regime'] == 'Risk-Off':
            return min(0.3, row['tactical_position'])  # Risk-Off时限制最大30%仓位
        else:
            return row['tactical_position']  # Risk-On时使用完整战术仓位
    
    backtest_results['final_position'] = backtest_results.apply(apply_macro_filter, axis=1)
    
    # 计算收益和成本
    backtest_results['trade'] = backtest_results['final_position'].diff().fillna(0).abs()
    backtest_results['strategy_return'] = (
        backtest_results['final_position'].shift(1).fillna(0) * backtest_results['log_return']
    )
    backtest_results['strategy_return'] -= backtest_results['trade'] * transaction_cost
    
    # 累计收益
    backtest_results['strategy_cumulative'] = np.exp(backtest_results['strategy_return'].cumsum())
    backtest_results['buy_and_hold_cumulative'] = np.exp(backtest_results['log_return'].cumsum())
    
    # 计算性能指标
    return calculate_and_display_performance(backtest_results)

def calculate_and_display_performance(backtest_results):
    """计算并展示性能指标"""
    days_in_year = 252
    
    # 年化指标
    hmm_annual_return = np.exp(backtest_results['strategy_return'].mean() * days_in_year) - 1
    hmm_annual_vol = backtest_results['strategy_return'].std() * np.sqrt(days_in_year)
    hmm_sharpe = hmm_annual_return / hmm_annual_vol if hmm_annual_vol != 0 else 0
    
    bnh_annual_return = np.exp(backtest_results['log_return'].mean() * days_in_year) - 1
    bnh_annual_vol = backtest_results['log_return'].std() * np.sqrt(days_in_year)
    bnh_sharpe = bnh_annual_return / bnh_annual_vol if bnh_annual_vol != 0 else 0
    
    # 最大回撤
    hmm_cum_max = backtest_results['strategy_cumulative'].cummax()
    hmm_drawdown = (backtest_results['strategy_cumulative'] - hmm_cum_max) / hmm_cum_max
    hmm_max_drawdown = hmm_drawdown.min()
    
    bnh_cum_max = backtest_results['buy_and_hold_cumulative'].cummax()
    bnh_drawdown = (backtest_results['buy_and_hold_cumulative'] - bnh_cum_max) / bnh_cum_max
    bnh_max_drawdown = bnh_drawdown.min()
    
    # 计算胜率和其他指标
    strategy_wins = (backtest_results['strategy_return'] > 0).sum()
    total_days = len(backtest_results)
    win_rate = strategy_wins / total_days
    
    # 计算信息比率 
    active_return = backtest_results['strategy_return'] - backtest_results['log_return']
    information_ratio = active_return.mean() / active_return.std() * np.sqrt(days_in_year) if active_return.std() != 0 else 0
    
    # 显示结果
    print("\n" + "="*80)
    print("🏆 FINAL PERFORMANCE RESULTS (NO LOOKAHEAD BIAS)")
    print(f"📅 Period: {backtest_results.index[0].date()} to {backtest_results.index[-1].date()}")
    print(f"📊 Total Trading Days: {total_days:,}")
    print("="*80)
    
    print(f"{'Metric':<25} | {'HMM Strategy':<15} | {'Buy & Hold':<15} | {'Difference':<15}")
    print("-" * 75)
    print(f"{'Annualized Return':<25} | {hmm_annual_return:>13.2%} | {bnh_annual_return:>13.2%} | {hmm_annual_return-bnh_annual_return:>+13.2%}")
    print(f"{'Annualized Volatility':<25} | {hmm_annual_vol:>13.2%} | {bnh_annual_vol:>13.2%} | {hmm_annual_vol-bnh_annual_vol:>+13.2%}")
    print(f"{'Sharpe Ratio':<25} | {hmm_sharpe:>13.2f} | {bnh_sharpe:>13.2f} | {hmm_sharpe-bnh_sharpe:>+13.2f}")
    print(f"{'Maximum Drawdown':<25} | {hmm_max_drawdown:>13.2%} | {bnh_max_drawdown:>13.2%} | {hmm_max_drawdown-bnh_max_drawdown:>+13.2%}")
    print(f"{'Information Ratio':<25} | {information_ratio:>13.2f} | {'0.00':>13} | {information_ratio:>+13.2f}")
    print(f"{'Win Rate (Daily)':<25} | {win_rate:>13.2%} | {'-':>13} | {'-':>13}")
    print("="*80)
    
    # 状态分布统计
    tactical_counts = backtest_results['tactical_regime'].value_counts()
    macro_counts = backtest_results['macro_regime'].value_counts()
    
    print("\n📈 REGIME DISTRIBUTION:")
    print("Tactical States:")
    for state, count in tactical_counts.items():
        pct = count / len(backtest_results) * 100
        print(f"  {state:<15}: {count:>6} days ({pct:>5.1f}%)")
    
    print("Macro States:")
    for state, count in macro_counts.items():
        pct = count / len(backtest_results) * 100
        print(f"  {state:<15}: {count:>6} days ({pct:>5.1f}%)")
    
    # 绘制图表
    plot_performance_chart(backtest_results)
    
    return backtest_results

def plot_performance_chart(results):
    """绘制性能对比图表"""
    plt.style.use('seaborn-v0_8-darkgrid')
    
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(16, 12))
    
    # 累计收益图
    ax1.plot(results.index, results['strategy_cumulative'], 
             label='HMM Strategy (No Lookahead)', color='royalblue', linewidth=2.5)
    ax1.plot(results.index, results['buy_and_hold_cumulative'], 
             label='Buy & Hold', color='darkred', linewidth=2, linestyle='--')
    
    ax1.set_title('HMM Strategy vs Buy & Hold - Cumulative Returns (No Lookahead Bias)', 
                  fontsize=14, fontweight='bold', pad=20)
    ax1.set_ylabel('Cumulative Growth of $1', fontsize=12)
    ax1.legend(loc='upper left', fontsize=11)
    ax1.grid(True, alpha=0.3)
    
    # 回撤图
    strategy_cum_max = results['strategy_cumulative'].cummax()
    strategy_drawdown = (results['strategy_cumulative'] - strategy_cum_max) / strategy_cum_max * 100
    
    bnh_cum_max = results['buy_and_hold_cumulative'].cummax()
    bnh_drawdown = (results['buy_and_hold_cumulative'] - bnh_cum_max) / bnh_cum_max * 100
    
    ax2.fill_between(results.index, strategy_drawdown, 0, 
                     alpha=0.7, color='royalblue', label='HMM Strategy')
    ax2.fill_between(results.index, bnh_drawdown, 0, 
                     alpha=0.5, color='darkred', label='Buy & Hold')
    
    ax2.set_title('Drawdown Comparison', fontsize=14, fontweight='bold', pad=20)
    ax2.set_ylabel('Drawdown (%)', fontsize=12)
    ax2.set_xlabel('Date', fontsize=12)
    ax2.legend(loc='lower right', fontsize=11)
    ax2.grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    # 保存图表
    filename = f"spy_hmm_v11_optimal_backtest_{datetime.datetime.now().strftime('%Y%m%d_%H%M')}.png"
    plt.savefig(filename, dpi=300, bbox_inches='tight')
    print(f"\n📊 Performance chart saved: {filename}")
    plt.show()

if __name__ == '__main__':
    print("🚀 SPY HMM Strategy V11 - Optimal No-Lookahead-Bias Version")
    print("=" * 70)
    
    # 配置参数
    SYMBOLS = ["SPY", "^VIX", "^TNX", "RSP", "HYG", "IEF"]
    DATA_START_DATE = "2000-01-01"
    BACKTEST_START_DATE = "2008-01-01"  # 2008年开始，给足够的训练数据
    BACKTEST_END_DATE = datetime.datetime.today().strftime('%Y-%m-%d')
    
    FEATURE_LIST = [
        'log_return', 'volatility', 'vix', 'tnx_change', 
        'dist_from_ma55', 'dist_from_ma233', 'dist_from_ma200', 
        'momentum_5d', 'momentum_20d', 'market_breadth', 'credit_risk_ratio'
    ]
    
    try:
        # 数据准备
        raw_data, price_data, feature_data = download_and_prepare_features(
            symbols=SYMBOLS,
            start_date=DATA_START_DATE,
            end_date=BACKTEST_END_DATE
        )
        
        # 合并数据
        full_data_for_backtest = price_data.join(feature_data.drop(columns=['log_return']))
        
        print(f"📊 Data loaded: {len(full_data_for_backtest)} days")
        print(f"📅 Data range: {full_data_for_backtest.index[0].date()} to {full_data_for_backtest.index[-1].date()}")
        
        # 运行回测
        results = run_backtest_optimal(
            full_data=full_data_for_backtest,
            feature_list=FEATURE_LIST,
            start_date=BACKTEST_START_DATE,
            end_date=BACKTEST_END_DATE,
            transaction_cost=0.0001
        )
        
        if results is not None:
            print("✅ Backtest completed successfully!")
            
            # 保存结果
            results_filename = f"spy_hmm_v11_results_{datetime.datetime.now().strftime('%Y%m%d_%H%M')}.csv"
            results.to_csv(results_filename)
            print(f"💾 Results saved: {results_filename}")
        else:
            print("❌ Backtest failed!")
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()