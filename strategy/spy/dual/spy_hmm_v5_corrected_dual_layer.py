#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SPY HMM Strategy V5 - 修正版双层架构
====================================

基于V5修正前瞻偏差问题：
1. 宏观模型采用严格的Walk-Forward训练
2. 避免使用未来信息进行状态识别
3. 保持V5的双层架构思路但确保无偏差

核心改进：
- 宏观模型每年重新训练，使用滚动60月窗口
- 战术模型每年重新训练，使用所有历史数据
- 严格的时间分离，无前瞻偏差

作者：基于V5修正
版本：V5 Corrected Dual Layer
日期：2025年8月29日
"""

import pandas as pd
import numpy as np
import yfinance as yf
import matplotlib.pyplot as plt
from sklearn.preprocessing import StandardScaler
from hmmlearn.hmm import GaussianHMM
import warnings
import datetime
from dateutil.relativedelta import relativedelta

warnings.filterwarnings('ignore')

def download_and_prepare_features(tactical_symbols, start_date, end_date):
    """下载数据并进行特征工程"""
    print("1. Downloading ALL necessary data...")
    
    # Download daily data for tactical model
    daily_data = yf.download(tactical_symbols, start=start_date, end=end_date, progress=False)['Close']
    daily_data.rename(columns={
        tactical_symbols[0]: 'price', 
        tactical_symbols[1]: 'vix', 
        tactical_symbols[2]: 'tnx', 
        tactical_symbols[3]: 'rsp', 
        tactical_symbols[4]: 'hyg', 
        tactical_symbols[5]: 'ief'
    }, inplace=True)

    if daily_data.empty:
        raise ValueError("Failed to download data.")

    # --- Tactical Feature Engineering (Daily) ---
    print("2. Engineering TACTICAL features (Daily)...")
    daily_data['log_return'] = np.log(daily_data['price'] / daily_data['price'].shift(1))
    daily_data['volatility'] = daily_data['log_return'].rolling(21).std() * np.sqrt(252)
    
    # 多时间框架移动平均
    for window in [55, 233, 200]:
        daily_data[f'dist_from_ma{window}'] = daily_data['price'] / daily_data['price'].rolling(window).mean() - 1
    
    daily_data['tnx_change'] = daily_data['tnx'].diff()
    
    for window in [5, 20]:
        daily_data[f'momentum_{window}d'] = daily_data['price'].pct_change(window)
    
    daily_data['market_breadth'] = daily_data['rsp'] / daily_data['price']
    daily_data['credit_risk_ratio'] = daily_data['hyg'] / daily_data['ief']
    
    tactical_features = [
        'log_return', 'volatility', 'vix', 'tnx_change', 
        'dist_from_ma55', 'dist_from_ma233', 'dist_from_ma200', 
        'momentum_5d', 'momentum_20d', 'market_breadth', 'credit_risk_ratio'
    ]
    
    feature_df = daily_data[tactical_features].dropna()
    price_data = daily_data[['price', 'log_return']].loc[feature_df.index]
    
    return price_data, feature_df

def find_best_hmm_model(X_train, n_states_range=range(2, 5)):
    """使用BIC选择最优HMM模型"""
    best_model, best_bic = None, np.inf
    for n_components in n_states_range:
        try:
            model = GaussianHMM(n_components=n_components, covariance_type="full", 
                              n_iter=1000, random_state=42)
            model.fit(X_train)
            bic = model.bic(X_train)
            if bic < best_bic:
                best_bic, best_model = bic, model
        except Exception as e:
            print(f"   Error fitting model with {n_components} states: {e}")
            continue
    
    if best_model is None:
        raise RuntimeError("Failed to train any HMM model.")
    return best_model

def analyze_and_map_states(model, features, scaler):
    """分析HMM状态并映射到市场状态"""
    state_means_original = scaler.inverse_transform(model.means_)
    state_profiles = pd.DataFrame(state_means_original, columns=features)
    
    print(f"   HMM Model chose {model.n_components} states.")
    print("   State Mean Profiles (unscaled):")
    with pd.option_context('display.max_columns', None):
        print(state_profiles)

    # 基于波动率的状态映射
    bull_state = state_profiles['volatility'].idxmin()
    bear_state = state_profiles['volatility'].idxmax()
    
    state_map = {bull_state: "Bull Market", bear_state: "Bear Market"}
    for i in range(model.n_components):
        if i not in state_map:
            state_map[i] = "Transition"
    
    print(f"   Initial State Mapping: {state_map}")
    return state_map

def get_macro_regime_corrected(current_date, lookback_months=60):
    """修正版宏观状态判断 - 严格无前瞻偏差"""
    current_dt = pd.to_datetime(current_date)
    
    # 计算宏观模型训练窗口
    macro_end_date = current_dt
    macro_start_date = macro_end_date - relativedelta(months=lookback_months)
    
    try:
        # 下载月频宏观数据
        macro_symbols = ["SPY", "TLT", "GLD", "DBC"]
        macro_data_raw = yf.download(
            macro_symbols, 
            start=macro_start_date.strftime('%Y-%m-%d'),
            end=macro_end_date.strftime('%Y-%m-%d'), 
            progress=False, 
            interval="1mo"
        )
        
        if macro_data_raw.empty:
            return "Risk-On"
        
        macro_data = macro_data_raw['Close'].copy()
        macro_data.columns = [s.lower() for s in macro_data.columns]
        
        # 宏观特征工程 - 保持V5的原始思路
        macro_features = pd.DataFrame(index=macro_data.index)
        macro_features['stock_bond_momentum'] = (macro_data['spy'] / macro_data['tlt']).pct_change(3)
        macro_features['gold_stock_momentum'] = (macro_data['gld'] / macro_data['spy']).pct_change(3)
        macro_features['commodity_momentum'] = macro_data['dbc'].pct_change(6)
        spy_returns = macro_data['spy'].pct_change()
        macro_features['market_volatility'] = spy_returns.rolling(6).std() * np.sqrt(12)
        
        macro_features = macro_features.dropna()
        
        if len(macro_features) < 24:  # 至少需要2年数据
            return "Risk-On"
        
        # 训练宏观HMM模型
        scaler = StandardScaler()
        X_macro = scaler.fit_transform(macro_features)
        
        macro_model = GaussianHMM(
            n_components=2, 
            covariance_type="full",
            n_iter=1000, 
            random_state=42
        )
        macro_model.fit(X_macro)
        
        # 状态映射：stock_bond_momentum高的是Risk-On
        state_means = scaler.inverse_transform(macro_model.means_)
        risk_on_state = np.argmax(state_means[:, 0])  # 第一个特征是股债动量
        
        # 预测当前状态
        if len(macro_features) > 0:
            current_features = macro_features.iloc[-1:].values
            current_features_scaled = scaler.transform(current_features)
            current_regime = macro_model.predict(current_features_scaled)[0]
            return 'Risk-On' if current_regime == risk_on_state else 'Risk-Off'
        else:
            return "Risk-On"
            
    except Exception as e:
        print(f"   Macro model warning: {e}, using default Risk-On")
        return "Risk-On"

def run_backtest_corrected_v5(full_data, feature_list, start_date, end_date, transaction_cost=0.0001):
    """修正版V5双层回测 - 无前瞻偏差"""
    print("\n" + "="*70)
    print("V5 CORRECTED: DUAL-LAYER HMM (NO LOOKAHEAD BIAS)")
    print("="*70)
    
    backtest_start_date = pd.to_datetime(start_date)
    backtest_end_date = pd.to_datetime(end_date)
    
    all_signals = []
    
    # 战术模型：年频更新
    for year in range(backtest_start_date.year, backtest_end_date.year + 1):
        train_end_date = f"{year-1}-12-31"
        predict_start_date = f"{year}-01-01"
        predict_end_date = f"{year}-12-31"
        
        print(f"\n{'='*50}")
        print(f"YEAR {year} - DUAL LAYER TRAINING")
        print(f"Training data: up to {train_end_date}")
        print(f"Prediction period: {predict_start_date} to {predict_end_date}")
        print(f"{'='*50}")
        
        # 获取训练和预测数据
        train_df = full_data.loc[:train_end_date]
        predict_df = full_data.loc[predict_start_date:predict_end_date]
        
        if train_df.empty or predict_df.empty or len(train_df) < 252:
            print(f"⚠️  Skipping year {year} - insufficient data (need ≥252 days)")
            continue
        
        print(f"✅ Training samples: {len(train_df)}, Prediction samples: {len(predict_df)}")
        
        # 1. 训练宏观模型（修正版 - 无前瞻偏差）
        print("🌍 Training macro model...")
        macro_regime = get_macro_regime_corrected(train_end_date)
        print(f"   Macro regime for {year}: {macro_regime}")
        
        # 2. 训练战术模型
        print("📊 Training tactical HMM model...")
        scaler = StandardScaler()
        X_train = scaler.fit_transform(train_df[feature_list])
        
        hmm_model = find_best_hmm_model(X_train)
        state_map = analyze_and_map_states(hmm_model, feature_list, scaler)
        
        # 3. 预测战术状态
        X_predict = scaler.transform(predict_df[feature_list])
        predicted_states = hmm_model.predict(X_predict)
        
        # 4. 生成信号
        signals_df = pd.DataFrame(index=predict_df.index)
        signals_df['tactical_regime'] = pd.Series(predicted_states, index=predict_df.index).map(state_map)
        signals_df['macro_regime'] = macro_regime  # 整年使用相同的宏观状态
        
        all_signals.append(signals_df)
        print(f"✅ Year {year} completed successfully")
    
    if not all_signals:
        print("❌ ERROR: No signals generated!")
        return None
    
    print(f"\n🎯 SIGNAL GENERATION COMPLETED - Processing {len(all_signals)} years")
    
    # 合并所有信号
    backtest_results = pd.concat(all_signals, sort=False)
    backtest_results = full_data[['log_return']].join(backtest_results, how='right')
    backtest_results.dropna(inplace=True)
    
    print(f"📊 Final dataset: {len(backtest_results)} trading days")
    
    # V5的仓位逻辑：双层融合
    tactical_position_map = {'Bull Market': 1.0, 'Transition': 0.75, 'Bear Market': 0.0}
    backtest_results['tactical_position'] = backtest_results['tactical_regime'].map(tactical_position_map)
    
    # 宏观过滤逻辑 - V5的原始思路
    def final_position(row):
        if row['macro_regime'] == "Risk-Off":
            return min(0.3, row['tactical_position'])  # Risk-Off时限制最大30%仓位
        return row['tactical_position']  # Risk-On时使用完整战术仓位
    
    backtest_results['position'] = backtest_results.apply(final_position, axis=1)
    
    # 计算收益和成本
    backtest_results['trade'] = backtest_results['position'].diff().fillna(0).abs()
    backtest_results['strategy_return'] = (
        backtest_results['position'].shift(1).fillna(0) * backtest_results['log_return']
    )
    backtest_results['strategy_return'] -= backtest_results['trade'] * transaction_cost
    
    # 累计收益
    backtest_results['strategy_cumulative'] = np.exp(backtest_results['strategy_return'].cumsum())
    backtest_results['buy_and_hold_cumulative'] = np.exp(backtest_results['log_return'].cumsum())
    
    # 计算性能指标
    return calculate_and_display_performance(backtest_results)

def calculate_and_display_performance(backtest_results):
    """计算并展示性能指标"""
    days_in_year = 252
    
    # 年化指标
    hmm_annual_return = np.exp(backtest_results['strategy_return'].mean() * days_in_year) - 1
    hmm_annual_vol = backtest_results['strategy_return'].std() * np.sqrt(days_in_year)
    hmm_sharpe = hmm_annual_return / hmm_annual_vol if hmm_annual_vol != 0 else 0
    
    bnh_annual_return = np.exp(backtest_results['log_return'].mean() * days_in_year) - 1
    bnh_annual_vol = backtest_results['log_return'].std() * np.sqrt(days_in_year)
    bnh_sharpe = bnh_annual_return / bnh_annual_vol if bnh_annual_vol != 0 else 0
    
    # 最大回撤
    hmm_cum_max = backtest_results['strategy_cumulative'].cummax()
    hmm_drawdown = (backtest_results['strategy_cumulative'] - hmm_cum_max) / hmm_cum_max
    hmm_max_drawdown = hmm_drawdown.min()
    
    bnh_cum_max = backtest_results['buy_and_hold_cumulative'].cummax()
    bnh_drawdown = (backtest_results['buy_and_hold_cumulative'] - bnh_cum_max) / bnh_cum_max
    bnh_max_drawdown = bnh_drawdown.min()
    
    # 计算胜率和其他指标
    strategy_wins = (backtest_results['strategy_return'] > 0).sum()
    total_days = len(backtest_results)
    win_rate = strategy_wins / total_days
    
    # 计算信息比率 
    active_return = backtest_results['strategy_return'] - backtest_results['log_return']
    information_ratio = active_return.mean() / active_return.std() * np.sqrt(days_in_year) if active_return.std() != 0 else 0
    
    # 显示结果
    print("\n" + "="*80)
    print("🏆 V5 CORRECTED PERFORMANCE RESULTS (NO LOOKAHEAD BIAS)")
    print(f"📅 Period: {backtest_results.index[0].date()} to {backtest_results.index[-1].date()}")
    print(f"📊 Total Trading Days: {total_days:,}")
    print("="*80)
    
    print(f"{'Metric':<25} | {'V5 Strategy':<15} | {'Buy & Hold':<15} | {'Difference':<15}")
    print("-" * 75)
    print(f"{'Annualized Return':<25} | {hmm_annual_return:>13.2%} | {bnh_annual_return:>13.2%} | {hmm_annual_return-bnh_annual_return:>+13.2%}")
    print(f"{'Annualized Volatility':<25} | {hmm_annual_vol:>13.2%} | {bnh_annual_vol:>13.2%} | {hmm_annual_vol-bnh_annual_vol:>+13.2%}")
    print(f"{'Sharpe Ratio':<25} | {hmm_sharpe:>13.2f} | {bnh_sharpe:>13.2f} | {hmm_sharpe-bnh_sharpe:>+13.2f}")
    print(f"{'Maximum Drawdown':<25} | {hmm_max_drawdown:>13.2%} | {bnh_max_drawdown:>13.2%} | {hmm_max_drawdown-bnh_max_drawdown:>+13.2%}")
    print(f"{'Information Ratio':<25} | {information_ratio:>13.2f} | {'0.00':>13} | {information_ratio:>+13.2f}")
    print(f"{'Win Rate (Daily)':<25} | {win_rate:>13.2%} | {'-':>13} | {'-':>13}")
    print("="*80)
    
    # 状态分布统计
    tactical_counts = backtest_results['tactical_regime'].value_counts()
    macro_counts = backtest_results['macro_regime'].value_counts()
    
    print("\n📈 REGIME DISTRIBUTION:")
    print("Tactical States:")
    for state, count in tactical_counts.items():
        pct = count / len(backtest_results) * 100
        print(f"  {state:<15}: {count:>6} days ({pct:>5.1f}%)")
    
    print("Macro States:")
    for state, count in macro_counts.items():
        pct = count / len(backtest_results) * 100
        print(f"  {state:<15}: {count:>6} days ({pct:>5.1f}%)")
    
    # 绘制图表
    plot_performance_chart(backtest_results)
    
    return backtest_results

def plot_performance_chart(results):
    """绘制性能对比图表"""
    plt.style.use('seaborn-v0_8-darkgrid')
    
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(16, 12))
    
    # 累计收益图
    ax1.plot(results.index, results['strategy_cumulative'], 
             label='V5 Corrected (No Lookahead)', color='green', linewidth=2.5)
    ax1.plot(results.index, results['buy_and_hold_cumulative'], 
             label='Buy & Hold', color='darkred', linewidth=2, linestyle='--')
    
    ax1.set_title('V5 Corrected Strategy vs Buy & Hold - Cumulative Returns (No Lookahead Bias)', 
                  fontsize=14, fontweight='bold', pad=20)
    ax1.set_ylabel('Cumulative Growth of $1', fontsize=12)
    ax1.legend(loc='upper left', fontsize=11)
    ax1.grid(True, alpha=0.3)
    
    # 回撤图
    strategy_cum_max = results['strategy_cumulative'].cummax()
    strategy_drawdown = (results['strategy_cumulative'] - strategy_cum_max) / strategy_cum_max * 100
    
    bnh_cum_max = results['buy_and_hold_cumulative'].cummax()
    bnh_drawdown = (results['buy_and_hold_cumulative'] - bnh_cum_max) / bnh_cum_max * 100
    
    ax2.fill_between(results.index, strategy_drawdown, 0, 
                     alpha=0.7, color='green', label='V5 Corrected')
    ax2.fill_between(results.index, bnh_drawdown, 0, 
                     alpha=0.5, color='darkred', label='Buy & Hold')
    
    ax2.set_title('Drawdown Comparison', fontsize=14, fontweight='bold', pad=20)
    ax2.set_ylabel('Drawdown (%)', fontsize=12)
    ax2.set_xlabel('Date', fontsize=12)
    ax2.legend(loc='lower right', fontsize=11)
    ax2.grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    # 保存图表
    filename = f"spy_hmm_v5_corrected_backtest_{datetime.datetime.now().strftime('%Y%m%d_%H%M')}.png"
    plt.savefig(filename, dpi=300, bbox_inches='tight')
    print(f"\n📊 Performance chart saved: {filename}")
    plt.show()

if __name__ == '__main__':
    print("🚀 SPY HMM Strategy V5 Corrected - Dual Layer No-Lookahead-Bias Version")
    print("=" * 80)
    
    # 配置参数
    TACTICAL_SYMBOLS = ["SPY", "^VIX", "^TNX", "RSP", "HYG", "IEF"]
    DATA_START_DATE = "2000-01-01"
    BACKTEST_START_DATE = "2008-01-01"
    BACKTEST_END_DATE = datetime.datetime.today().strftime('%Y-%m-%d')
    
    FEATURE_LIST = [
        'log_return', 'volatility', 'vix', 'tnx_change', 
        'dist_from_ma55', 'dist_from_ma233', 'dist_from_ma200', 
        'momentum_5d', 'momentum_20d', 'market_breadth', 'credit_risk_ratio'
    ]
    
    try:
        # 数据准备
        price_data, feature_data = download_and_prepare_features(
            tactical_symbols=TACTICAL_SYMBOLS,
            start_date=DATA_START_DATE,
            end_date=BACKTEST_END_DATE
        )
        
        # 合并数据
        full_data_for_backtest = price_data.join(feature_data.drop(columns=['log_return']))
        
        print(f"📊 Data loaded: {len(full_data_for_backtest)} days")
        print(f"📅 Data range: {full_data_for_backtest.index[0].date()} to {full_data_for_backtest.index[-1].date()}")
        
        # 运行回测
        results = run_backtest_corrected_v5(
            full_data=full_data_for_backtest,
            feature_list=FEATURE_LIST,
            start_date=BACKTEST_START_DATE,
            end_date=BACKTEST_END_DATE,
            transaction_cost=0.0001
        )
        
        if results is not None:
            print("✅ V5 Corrected backtest completed successfully!")
            
            # 保存结果
            results_filename = f"spy_hmm_v5_corrected_results_{datetime.datetime.now().strftime('%Y%m%d_%H%M')}.csv"
            results.to_csv(results_filename)
            print(f"💾 Results saved: {results_filename}")
        else:
            print("❌ Backtest failed!")
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()