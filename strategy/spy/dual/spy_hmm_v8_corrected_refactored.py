#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SPY HMM Strategy V8 - 修正版重构优化
====================================

基于V8修正前瞻偏差问题：
1. 宏观模型采用严格的Walk-Forward训练
2. 保持V8原有的代码重构和性能优化
3. 避免使用未来信息进行状态识别

核心修正：
- 宏观模型每年重新训练，只使用历史数据
- 保持V8的代码结构标准化和算法参数精细调优
- 严格的时间分离，无前瞻偏差
- 保持错误处理增强

作者：基于V8修正
版本：V8 Corrected Refactored
日期：2025年8月29日
"""

import pandas as pd
import numpy as np
import yfinance as yf
import matplotlib.pyplot as plt
from matplotlib.patches import Patch
from sklearn.preprocessing import StandardScaler
from hmmlearn.hmm import GaussianHMM
import warnings
import datetime

warnings.filterwarnings('ignore')

def download_and_prepare_features(symbols, start_date="2000-01-01", end_date=None, vol_window=21, ma_windows=[55, 233], mom_windows=[5, 20]):
    if end_date is None:
        end_date = datetime.datetime.today().strftime('%Y-%m-%d')
    
    print(f"1. Downloading daily data for {symbols} from {start_date} to {end_date}...")
    data_raw = yf.download(symbols, start=start_date, end=end_date, progress=False)
    data = data_raw['Close'].copy()
    data.rename(columns={symbols[0]: 'price', symbols[1]: 'vix', symbols[2]: 'tnx', symbols[3]: 'rsp', symbols[4]: 'hyg', symbols[5]: 'ief'}, inplace=True)

    if data.empty:
        raise ValueError(f"Failed to download data. Please check symbols or date range.")

    print("2. Engineering features for HMM...")
    data['log_return'] = np.log(data['price'] / data['price'].shift(1))
    data['volatility'] = data['log_return'].rolling(window=vol_window).std() * np.sqrt(252)
    
    for window in ma_windows:
        data[f'dist_from_ma{window}'] = data['price'] / data['price'].rolling(window=window).mean() - 1
    data['dist_from_ma200'] = data['price'] / data['price'].rolling(window=200).mean() - 1

    data['tnx_change'] = data['tnx'].diff()

    for window in mom_windows:
        data[f'momentum_{window}d'] = data['price'].pct_change(window)

    data['market_breadth'] = data['rsp'] / data['price']
    data['credit_risk_ratio'] = data['hyg'] / data['ief']

    feature_cols = ['log_return', 'volatility', 'vix', 'tnx_change'] + [f'dist_from_ma{w}' for w in ma_windows] + ['dist_from_ma200'] + [f'momentum_{w}d' for w in mom_windows] + ['market_breadth', 'credit_risk_ratio']
    feature_df = data[feature_cols].dropna()
    
    return data_raw, data[['price', 'log_return']].loc[feature_df.index], feature_df

def find_best_hmm_model_v8(X_train: np.ndarray, n_states_range: range = range(2, 5)):
    """V8重构优化的HMM训练 - 精细调优参数"""
    best_model, best_bic = None, np.inf
    
    # V8的参数精细调优
    covariance_types = ["full", "diag"]
    tolerances = [1e-4, 1e-3]
    n_iters = [1000, 1500]
    
    for n_components in n_states_range:
        for cov_type in covariance_types:
            for tol in tolerances:
                for n_iter in n_iters:
                    try:
                        model = GaussianHMM(
                            n_components=n_components, 
                            covariance_type=cov_type,
                            n_iter=n_iter, 
                            random_state=42,
                            tol=tol
                        )
                        model.fit(X_train)
                        bic = model.bic(X_train)
                        
                        if bic < best_bic:
                            best_bic, best_model = bic, model
                            print(f"   New best model: {n_components} states, {cov_type} cov, BIC={bic:.2f}")
                        
                    except Exception as e:
                        continue
    
    if best_model is None:
        print("   Warning: Using fallback model")
        try:
            best_model = GaussianHMM(n_components=2, covariance_type="diag", n_iter=500, random_state=42)
            best_model.fit(X_train)
        except:
            raise RuntimeError("Failed to train any HMM model.")
    
    return best_model

def analyze_and_map_states_v8(model: GaussianHMM, features: list, scaler: StandardScaler):
    """V8增强状态分析 - 代码结构标准化"""
    try:
        state_means_original = scaler.inverse_transform(model.means_)
        state_profiles = pd.DataFrame(state_means_original, columns=features)
        
        print(f"   HMM Model chose {model.n_components} states with enhanced analysis.")
        print("   State Mean Profiles (unscaled):")
        with pd.option_context('display.max_columns', None):
            print(state_profiles.round(4))

        # V8改进的状态识别逻辑 - 多特征综合考虑
        vol_ranks = state_profiles['volatility'].rank()
        return_ranks = state_profiles['log_return'].rank()
        vix_ranks = state_profiles['vix'].rank()
        
        # 综合排序
        composite_score = vol_ranks + vix_ranks - return_ranks
        sorted_states = composite_score.sort_values().index.tolist()
        
        state_map = {}
        if len(sorted_states) >= 3:
            state_map[sorted_states[0]] = "Bull Market"      # 低波动，低VIX，高收益
            state_map[sorted_states[-1]] = "Bear Market"     # 高波动，高VIX，低收益
            for i in range(1, len(sorted_states) - 1):
                state_map[sorted_states[i]] = "Transition"   # 中间状态
        else:
            state_map[sorted_states[0]] = "Bull Market"
            state_map[sorted_states[-1]] = "Bear Market"
        
        print(f"   V8 Enhanced State Mapping: {state_map}")
        return state_map
        
    except Exception as e:
        print(f"   Error in V8 state mapping: {e}, using fallback")
        return {0: "Bull Market", 1: "Bear Market"} if model.n_components == 2 else {0: "Bull Market", 1: "Transition", 2: "Bear Market"}

def get_macro_regime_corrected_v8(current_date):
    """修正版宏观状态判断 - V8重构逻辑但无前瞻偏差"""
    print("   Building V8 Refactored Macro-Economic Model (Corrected)...")
    current_dt = pd.to_datetime(current_date)
    
    # V8使用更长的历史数据训练
    macro_end_date = current_dt
    macro_start_date = current_dt - pd.DateOffset(years=7)  # V8扩展到7年
    
    try:
        macro_symbols = ["SPY", "TLT", "GLD"]
        print(f"   Downloading extended monthly macro data: {macro_symbols}...")
        macro_data_raw = yf.download(macro_symbols, 
                                   start=macro_start_date.strftime('%Y-%m-%d'),
                                   end=macro_end_date.strftime('%Y-%m-%d'), 
                                   progress=False, interval="1mo")
        
        if macro_data_raw.empty or len(macro_data_raw) < 36:  # V8要求3年最少数据
            print("   Insufficient macro data for V8, defaulting to Risk-On")
            return "Risk-On"
        
        macro_data = macro_data_raw['Close'].copy()
        macro_data.columns = [s.lower() for s in macro_data.columns]

        # V8的改进宏观特征
        macro_data['spy_tlt_ratio'] = macro_data['spy'] / macro_data['tlt']
        macro_data['gld_spy_ratio'] = macro_data['gld'] / macro_data['spy']
        
        # V8增加滚动统计特征
        macro_data['spy_tlt_ma6'] = macro_data['spy_tlt_ratio'].rolling(6).mean()
        macro_data['gld_spy_ma6'] = macro_data['gld_spy_ratio'].rolling(6).mean()
        
        macro_features = pd.DataFrame()
        macro_features['spy_tlt_change'] = macro_data['spy_tlt_ratio'].pct_change()
        macro_features['gld_spy_change'] = macro_data['gld_spy_ratio'].pct_change()
        macro_features['spy_tlt_trend'] = (macro_data['spy_tlt_ratio'] / macro_data['spy_tlt_ma6'] - 1)
        macro_features['gld_spy_trend'] = (macro_data['gld_spy_ratio'] / macro_data['gld_spy_ma6'] - 1)
        
        macro_features = macro_features.dropna()
        
        if len(macro_features) < 24:
            return "Risk-On"
        
        scaler = StandardScaler()
        X_macro = scaler.fit_transform(macro_features)
        
        # V8优化的宏观模型训练
        best_macro_model = None
        best_bic = np.inf
        
        for cov_type in ["full", "diag"]:
            for n_iter in [1000, 1500]:
                try:
                    macro_model = GaussianHMM(n_components=2, covariance_type=cov_type, 
                                            n_iter=n_iter, random_state=42, tol=1e-4)
                    macro_model.fit(X_macro)
                    bic = macro_model.bic(X_macro)
                    
                    if bic < best_bic:
                        best_bic = bic
                        best_macro_model = macro_model
                        
                except Exception:
                    continue
        
        if best_macro_model is None:
            print("   V8 macro model training failed, defaulting to Risk-On")
            return "Risk-On"
        
        state_means = scaler.inverse_transform(best_macro_model.means_)
        # V8改进：使用spy_tlt_change作为主要判断依据
        risk_on_state = np.argmax(state_means[:, 0])
        
        # 只预测最后一个时期的状态
        current_features = macro_features.iloc[-1:].values
        current_features_scaled = scaler.transform(current_features)
        current_regime = best_macro_model.predict(current_features_scaled)[0]
        
        regime_result = 'Risk-On' if current_regime == risk_on_state else 'Risk-Off'
        print(f"   V8 Macro regime: {regime_result}")
        
        return regime_result
        
    except Exception as e:
        print(f"   V8 macro model error: {e}, using default Risk-On")
        return "Risk-On"

def run_backtest(full_data, feature_list, start_date, end_date, transaction_cost=0.0001):
    print("\n--- Starting V8 Corrected Refactored Walk-Forward Backtest ---")
    
    backtest_start_date = pd.to_datetime(start_date)
    backtest_end_date = pd.to_datetime(end_date)
    
    all_signals = []
    
    for year in range(backtest_start_date.year, backtest_end_date.year + 1):
        train_end_date = f"{year-1}-12-31"
        predict_start_date = f"{year}-01-01"
        predict_end_date = f"{year}-12-31"
        
        print(f"\nV8 Training for year {year} (data up to {train_end_date})...")
        
        train_df = full_data.loc[:train_end_date]
        predict_df = full_data.loc[predict_start_date:predict_end_date]
        
        if train_df.empty or predict_df.empty or len(train_df) < 252:
            print(f"Skipping year {year} due to insufficient training or prediction data.")
            continue
        
        # 1. V8修正版宏观模型
        macro_regime = get_macro_regime_corrected_v8(train_end_date)
        
        # 2. V8重构优化的战术模型
        try:
            scaler = StandardScaler()
            X_train = scaler.fit_transform(train_df[feature_list])
            
            hmm_model = find_best_hmm_model_v8(X_train)
            state_map = analyze_and_map_states_v8(hmm_model, feature_list, scaler)
            
            X_predict = scaler.transform(predict_df[feature_list])
            predicted_states = hmm_model.predict(X_predict)
            
            signals_df = pd.DataFrame(index=predict_df.index)
            signals_df['tactical_regime'] = pd.Series(predicted_states, index=predict_df.index).map(state_map)
            signals_df['macro_regime'] = macro_regime
            all_signals.append(signals_df)
            
        except Exception as e:
            print(f"   V8 Error in year {year}: {e}, skipping...")
            continue

    if not all_signals:
        print("Could not generate any signals. Backtest aborted.")
        return None

    backtest_results = pd.concat(all_signals)
    
    print("\n--- V8 Fusing Macro and Tactical Models & Calculating Performance ---")
    
    backtest_results = full_data[['log_return']].join(backtest_results, how='right')
    backtest_results.dropna(inplace=True)

    # V8的精细调优仓位逻辑
    position_map = {'Bull Market': 1.0, 'Transition': 0.75, 'Bear Market': 0.0}
    backtest_results['tactical_position'] = backtest_results['tactical_regime'].map(position_map)

    # V8改进的宏观过滤逻辑
    backtest_results['final_position'] = np.where(
        backtest_results['macro_regime'] == 'Risk-Off',
        np.minimum(backtest_results['tactical_position'], 0.3),
        backtest_results['tactical_position']
    )
    
    backtest_results['trade'] = backtest_results['final_position'].diff().fillna(0).abs()
    backtest_results['strategy_return'] = backtest_results['final_position'].shift(1).fillna(0) * backtest_results['log_return']
    backtest_results['strategy_return'] -= backtest_results['trade'] * transaction_cost
    
    backtest_results['strategy_cumulative'] = np.exp(backtest_results['strategy_return'].cumsum())
    backtest_results['buy_and_hold_cumulative'] = np.exp(backtest_results['log_return'].cumsum())
    
    # V8增强的性能计算
    days_in_year = 252
    
    hmm_annual_return = np.exp(backtest_results['strategy_return'].mean() * days_in_year) - 1
    hmm_annual_vol = backtest_results['strategy_return'].std() * np.sqrt(days_in_year)
    hmm_sharpe = hmm_annual_return / hmm_annual_vol if hmm_annual_vol != 0 else 0
    
    bnh_annual_return = np.exp(backtest_results['log_return'].mean() * days_in_year) - 1
    bnh_annual_vol = backtest_results['log_return'].std() * np.sqrt(days_in_year)
    bnh_sharpe = bnh_annual_return / bnh_annual_vol if bnh_annual_vol != 0 else 0
    
    hmm_cum_re_max = backtest_results['strategy_cumulative'].cummax()
    hmm_drawdown = (backtest_results['strategy_cumulative'] - hmm_cum_re_max) / hmm_cum_re_max
    hmm_max_drawdown = hmm_drawdown.min()
    
    bnh_cum_re_max = backtest_results['buy_and_hold_cumulative'].cummax()
    bnh_drawdown = (backtest_results['buy_and_hold_cumulative'] - bnh_cum_re_max) / bnh_cum_re_max
    bnh_max_drawdown = bnh_drawdown.min()

    # V8增强的结果显示
    print("\n" + "="*60)
    print("V8 Corrected Refactored Backtest Results")
    print(f"Period: {backtest_results.index[0].date()} to {backtest_results.index[-1].date()}")
    print("="*60)
    print(f"{'Metric':<25} | {'V8 Strategy':<15} | {'Buy and Hold':<15}")
    print("-"*60)
    print(f"{'Annualized Return':<25} | {hmm_annual_return:14.2%} | {bnh_annual_return:14.2%}")
    print(f"{'Annualized Volatility':<25} | {hmm_annual_vol:14.2%} | {bnh_annual_vol:14.2%}")
    print(f"{'Sharpe Ratio':<25} | {hmm_sharpe:14.2f} | {bnh_sharpe:14.2f}")
    print(f"{'Maximum Drawdown':<25} | {hmm_max_drawdown:14.2%} | {bnh_max_drawdown:14.2%}")
    print("="*60)

    plt.style.use('seaborn-v0_8-darkgrid')
    fig, ax = plt.subplots(figsize=(18, 9))
    ax.plot(backtest_results.index, backtest_results['strategy_cumulative'], label='V8 Corrected Refactored', color='teal', lw=2)
    ax.plot(backtest_results.index, backtest_results['buy_and_hold_cumulative'], label='Buy and Hold', color='black', lw=2, linestyle='--')
    ax.set_title('V8 Corrected Refactored vs. Buy and Hold (No Lookahead)', fontsize=16, fontweight='bold')
    ax.set_ylabel('Cumulative Growth of $1')
    ax.set_xlabel('Date')
    ax.legend(loc='upper left')
    ax.grid(True)
    plt.tight_layout()
    filename = f"hmm_spy_v8_corrected_backtest_plot_{datetime.datetime.now().strftime('%Y%m%d')}.png"
    plt.savefig(filename)
    print(f"\nV8 Backtest chart saved to {filename}")
    plt.close()
    
    return backtest_results

if __name__ == '__main__':
    SYMBOLS = ["SPY", "^VIX", "^TNX", "RSP", "HYG", "IEF"]
    DATA_START_DATE = "2000-01-01"
    BACKTEST_START_DATE = "2008-01-01"
    BACKTEST_END_DATE = datetime.datetime.today().strftime('%Y-%m-%d')
    
    FEATURE_LIST = ['log_return', 'volatility', 'vix', 'tnx_change', 'dist_from_ma55', 'dist_from_ma233', 'dist_from_ma200', 'momentum_5d', 'momentum_20d', 'market_breadth', 'credit_risk_ratio']
    
    raw_data, price_data, feature_data = download_and_prepare_features(
        symbols=SYMBOLS, 
        start_date=DATA_START_DATE, 
        end_date=BACKTEST_END_DATE
    )
    
    full_data_for_backtest = price_data.join(feature_data.drop(columns=['log_return']))

    results = run_backtest(full_data_for_backtest, FEATURE_LIST, BACKTEST_START_DATE, BACKTEST_END_DATE)
    
    if results is not None:
        results_filename = f"spy_hmm_v8_corrected_results_{datetime.datetime.now().strftime('%Y%m%d_%H%M')}.csv"
        results.to_csv(results_filename)
        print(f"V8 Results saved: {results_filename}")