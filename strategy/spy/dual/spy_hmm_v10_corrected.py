#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SPY HMM Strategy V10 - 修正前瞻偏差版本
=============================================

基于V9版本，修正了宏观模型的前瞻偏差问题：
1. 宏观模型也采用Walk-Forward训练
2. 避免使用未来信息进行状态识别
3. 提供真实的样本外性能评估

修正要点：
- 宏观模型使用滚动窗口训练
- 每年重新训练宏观和战术模型
- 严格的时间分离，无前瞻偏差

作者：基于V9版本修正
版本：V10 Corrected
日期：2025年8月29日
"""

import pandas as pd
import numpy as np
import yfinance as yf
import matplotlib.pyplot as plt
from sklearn.preprocessing import StandardScaler
from hmmlearn.hmm import GaussianHMM
import warnings
import datetime

warnings.filterwarnings('ignore')

def download_and_prepare_features(symbols, start_date, end_date):
    """下载数据并进行特征工程"""
    print(f"1. Downloading data for {symbols} from {start_date} to {end_date}...")
    
    data_raw = yf.download(symbols, start=start_date, end=end_date, progress=False)
    data = data_raw['Close'].copy()
    data.columns = [s.lower().replace('^', '') for s in data.columns]
    
    # 特征工程
    data['log_return'] = np.log(data['spy'] / data['spy'].shift(1))
    data['volatility'] = data['log_return'].rolling(window=21).std() * np.sqrt(252)
    data['vix'] = data_raw['Close']['^VIX']
    data['tnx_change'] = data['tnx'].diff()
    
    # 多时间框架移动平均
    for window in [55, 200, 233]:
        data[f'ma_{window}'] = data['spy'].rolling(window=window).mean()
        data[f'dist_from_ma{window}'] = data['spy'] / data[f'ma_{window}'] - 1
    
    # 动量特征
    for window in [5, 20]:
        data[f'momentum_{window}d'] = data['spy'].pct_change(window)
    
    # 市场广度和信用风险
    data['market_breadth'] = data['rsp'] / data['spy']
    data['credit_risk_ratio'] = data['hyg'] / data['ief']
    
    feature_cols = ['log_return', 'volatility', 'vix', 'tnx_change', 'dist_from_ma55', 
                   'dist_from_ma233', 'dist_from_ma200', 'momentum_5d', 'momentum_20d', 
                   'market_breadth', 'credit_risk_ratio']
    
    feature_df = data[feature_cols].dropna()
    price_data = data[['spy', 'log_return']].rename(columns={'spy': 'price'}).loc[feature_df.index]
    
    return data_raw, price_data, feature_df

def find_best_hmm_model(X_train, n_states_range=range(2, 5)):
    """使用BIC选择最优HMM模型"""
    best_model, best_bic = None, np.inf
    for n_components in n_states_range:
        try:
            model = GaussianHMM(n_components=n_components, covariance_type="full", 
                              n_iter=1000, random_state=42)
            model.fit(X_train)
            bic = model.bic(X_train)
            if bic < best_bic:
                best_bic, best_model = bic, model
        except:
            continue
    return best_model

def analyze_and_map_states(model, feature_list, scaler):
    """分析HMM状态并映射到市场状态"""
    state_means = scaler.inverse_transform(model.means_)
    log_return_idx = feature_list.index('log_return')
    
    state_returns = [(i, state_means[i, log_return_idx]) for i in range(model.n_components)]
    state_returns.sort(key=lambda x: x[1])
    
    state_map = {}
    if len(state_returns) >= 3:
        state_map[state_returns[0][0]] = "Bear Market"
        state_map[state_returns[-1][0]] = "Bull Market"
        for i in range(1, len(state_returns) - 1):
            state_map[state_returns[i][0]] = "Transition"
    else:
        state_map[state_returns[0][0]] = "Bear Market"
        state_map[state_returns[-1][0]] = "Bull Market"
    
    return state_map

def get_macro_regime_corrected(current_date, lookback_months=60):
    """修正版宏观模型 - 避免前瞻偏差"""
    print(f"   Training macro model up to {current_date} (lookback: {lookback_months} months)...")
    
    # 计算训练窗口
    macro_end_date = current_date
    macro_start_date = pd.to_datetime(current_date) - pd.DateOffset(months=lookback_months)
    
    try:
        # 下载宏观数据
        macro_symbols = ["SPY", "TLT", "GLD"]
        macro_data_raw = yf.download(macro_symbols, start=macro_start_date, 
                                   end=macro_end_date, progress=False, interval="1mo")
        macro_data = macro_data_raw['Close'].copy()
        macro_data.columns = [s.lower() for s in macro_data.columns]
        
        # 特征工程
        macro_data['spy_tlt_ratio'] = macro_data['spy'] / macro_data['tlt']
        macro_data['gld_spy_ratio'] = macro_data['gld'] / macro_data['spy']
        macro_features = macro_data[['spy_tlt_ratio', 'gld_spy_ratio']].pct_change().dropna()
        
        if len(macro_features) < 24:  # 至少需要2年数据
            return "Risk-On"  # 默认状态
        
        # 训练宏观HMM
        scaler = StandardScaler()
        X_macro = scaler.fit_transform(macro_features)
        
        macro_model = GaussianHMM(n_components=2, covariance_type="full", 
                                n_iter=1000, random_state=42)
        macro_model.fit(X_macro)
        
        # 状态映射
        state_means = scaler.inverse_transform(macro_model.means_)
        risk_on_state = np.argmax(state_means[:, 0])  # SPY/TLT比率高的状态
        
        # 预测当前状态
        current_features = macro_features.iloc[-1:].values
        current_features_scaled = scaler.transform(current_features)
        current_regime = macro_model.predict(current_features_scaled)[0]
        
        return 'Risk-On' if current_regime == risk_on_state else 'Risk-Off'
        
    except Exception as e:
        print(f"   Macro model error: {e}, using default Risk-On")
        return "Risk-On"

def run_backtest_corrected(full_data, feature_list, start_date, end_date, transaction_cost=0.0001):
    """修正版回测 - 双层Walk-Forward"""
    print("\n--- Starting Corrected Walk-Forward Backtest (No Look-Ahead Bias) ---")
    
    backtest_start_date = pd.to_datetime(start_date)
    backtest_end_date = pd.to_datetime(end_date)
    
    all_signals = []
    
    for year in range(backtest_start_date.year, backtest_end_date.year + 1):
        train_end_date = f"{year-1}-12-31"
        predict_start_date = f"{year}-01-01"
        predict_end_date = f"{year}-12-31"
        
        print(f"\n=== Year {year} Training (data up to {train_end_date}) ===")
        
        train_df = full_data.loc[:train_end_date]
        predict_df = full_data.loc[predict_start_date:predict_end_date]
        
        if train_df.empty or predict_df.empty or len(train_df) < 252:
            print(f"Skipping year {year} due to insufficient data.")
            continue
        
        # 1. 训练宏观模型 (修正版 - 无前瞻偏差)
        macro_regime = get_macro_regime_corrected(train_end_date)
        print(f"   Macro regime for {year}: {macro_regime}")
        
        # 2. 训练战术模型
        scaler = StandardScaler()
        X_train = scaler.fit_transform(train_df[feature_list])
        
        hmm_model = find_best_hmm_model(X_train)
        if hmm_model is None:
            print(f"Failed to train HMM for year {year}")
            continue
            
        state_map = analyze_and_map_states(hmm_model, feature_list, scaler)
        print(f"   Tactical state mapping: {state_map}")
        
        # 3. 预测
        X_predict = scaler.transform(predict_df[feature_list])
        predicted_states = hmm_model.predict(X_predict)
        
        # 4. 生成信号
        signals_df = pd.DataFrame(index=predict_df.index)
        signals_df['tactical_regime'] = pd.Series(predicted_states, index=predict_df.index).map(state_map)
        signals_df['macro_regime'] = macro_regime  # 整年使用相同的宏观状态
        
        all_signals.append(signals_df)
    
    if not all_signals:
        print("Could not generate any signals. Backtest aborted.")
        return
    
    # 合并信号并计算仓位
    backtest_results = pd.concat(all_signals)
    backtest_results = full_data[['log_return']].join(backtest_results, how='right')
    backtest_results.dropna(inplace=True)
    
    # 仓位逻辑 (与V9相同)
    position_map = {'Bull Market': 1.0, 'Transition': 0.75, 'Bear Market': 0.0}
    backtest_results['tactical_position'] = backtest_results['tactical_regime'].map(position_map)
    
    # 宏观过滤逻辑
    conditions = [
        backtest_results['macro_regime'] == 'Risk-Off',
        backtest_results['macro_regime'] == 'Risk-On'
    ]
    choices = [
        np.minimum(backtest_results['tactical_position'], 0.3),  # Risk-Off时限制仓位
        backtest_results['tactical_position']  # Risk-On时使用战术仓位
    ]
    
    backtest_results['final_position'] = np.select(conditions, choices, 
                                                  default=backtest_results['tactical_position'])
    
    # 计算收益
    backtest_results['trade'] = backtest_results['final_position'].diff().fillna(0).abs()
    backtest_results['strategy_return'] = (backtest_results['final_position'].shift(1).fillna(0) * 
                                         backtest_results['log_return'])
    backtest_results['strategy_return'] -= backtest_results['trade'] * transaction_cost
    
    # 性能计算
    backtest_results['strategy_cumulative'] = np.exp(backtest_results['strategy_return'].cumsum())
    backtest_results['buy_and_hold_cumulative'] = np.exp(backtest_results['log_return'].cumsum())
    
    # 计算指标
    days_in_year = 252
    
    hmm_annual_return = np.exp(backtest_results['strategy_return'].mean() * days_in_year) - 1
    hmm_annual_vol = backtest_results['strategy_return'].std() * np.sqrt(days_in_year)
    hmm_sharpe = hmm_annual_return / hmm_annual_vol if hmm_annual_vol != 0 else 0
    
    bnh_annual_return = np.exp(backtest_results['log_return'].mean() * days_in_year) - 1
    bnh_annual_vol = backtest_results['log_return'].std() * np.sqrt(days_in_year)
    bnh_sharpe = bnh_annual_return / bnh_annual_vol if bnh_annual_vol != 0 else 0
    
    # 最大回撤
    hmm_cum_max = backtest_results['strategy_cumulative'].cummax()
    hmm_drawdown = (backtest_results['strategy_cumulative'] - hmm_cum_max) / hmm_cum_max
    hmm_max_drawdown = hmm_drawdown.min()
    
    bnh_cum_max = backtest_results['buy_and_hold_cumulative'].cummax()
    bnh_drawdown = (backtest_results['buy_and_hold_cumulative'] - bnh_cum_max) / bnh_cum_max
    bnh_max_drawdown = bnh_drawdown.min()
    
    # 显示结果
    print("\n" + "="*70)
    print("SPY HMM Strategy V10 - Corrected Backtest Results (No Look-Ahead Bias)")
    print(f"Period: {backtest_results.index[0].date()} to {backtest_results.index[-1].date()}")
    print("="*70)
    print(f"{'Metric':<25} | {'HMM Strategy':<15} | {'Buy and Hold':<15}")
    print("-"*70)
    print(f"{'Annualized Return':<25} | {hmm_annual_return:14.2%} | {bnh_annual_return:14.2%}")
    print(f"{'Annualized Volatility':<25} | {hmm_annual_vol:14.2%} | {bnh_annual_vol:14.2%}")
    print(f"{'Sharpe Ratio':<25} | {hmm_sharpe:14.2f} | {bnh_sharpe:14.2f}")
    print(f"{'Maximum Drawdown':<25} | {hmm_max_drawdown:14.2%} | {bnh_max_drawdown:14.2%}")
    print("="*70)
    print(f"🔧 修正要点:")
    print(f"   ✅ 宏观模型使用滚动60个月窗口训练")
    print(f"   ✅ 每年重新训练宏观和战术模型") 
    print(f"   ✅ 严格时间分离，无前瞻偏差")
    print(f"   ⚠️  性能可能低于V6-V9版本 (因为修正了偏差)")
    
    return backtest_results

if __name__ == '__main__':
    print("🔧 SPY HMM Strategy V10 - 修正前瞻偏差版本")
    print("="*60)
    
    SYMBOLS = ["SPY", "^VIX", "^TNX", "RSP", "HYG", "IEF"]
    DATA_START_DATE = "2000-01-01"
    BACKTEST_START_DATE = "2008-01-01"
    BACKTEST_END_DATE = datetime.datetime.today().strftime('%Y-%m-%d')
    
    FEATURE_LIST = ['log_return', 'volatility', 'vix', 'tnx_change', 'dist_from_ma55', 
                   'dist_from_ma233', 'dist_from_ma200', 'momentum_5d', 'momentum_20d', 
                   'market_breadth', 'credit_risk_ratio']
    
    raw_data, price_data, feature_data = download_and_prepare_features(
        symbols=SYMBOLS, 
        start_date=DATA_START_DATE, 
        end_date=BACKTEST_END_DATE
    )
    
    full_data_for_backtest = price_data.join(feature_data.drop(columns=['log_return']))
    
    results = run_backtest_corrected(full_data_for_backtest, FEATURE_LIST, 
                                   BACKTEST_START_DATE, BACKTEST_END_DATE)
