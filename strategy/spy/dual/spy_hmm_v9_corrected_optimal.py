#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SPY HMM Strategy V9 - 修正版终极优化
====================================

基于V9修正前瞻偏差问题：
1. 宏观模型采用严格的Walk-Forward训练
2. 保持V9原有的终极优化逻辑
3. 避免使用未来信息进行状态识别

核心修正：
- 宏观模型每年重新训练，只使用历史数据
- 保持V9的最优双层架构配置和精确参数调优
- 严格的时间分离，无前瞻偏差
- 保持完善的风险控制

作者：基于V9修正
版本：V9 Corrected Ultimate
日期：2025年8月29日
"""

import pandas as pd
import numpy as np
import yfinance as yf
import matplotlib.pyplot as plt
from matplotlib.patches import Patch
from sklearn.preprocessing import StandardScaler
from hmmlearn.hmm import GaussianHMM
import warnings
import datetime

warnings.filterwarnings('ignore')

def download_and_prepare_features(symbols, start_date="2000-01-01", end_date=None, vol_window=21, ma_windows=[55, 233], mom_windows=[5, 20]):
    if end_date is None:
        end_date = datetime.datetime.today().strftime('%Y-%m-%d')
    
    print(f"1. Downloading daily data for {symbols} from {start_date} to {end_date}...")
    data_raw = yf.download(symbols, start=start_date, end=end_date, progress=False)
    data = data_raw['Close'].copy()
    data.rename(columns={symbols[0]: 'price', symbols[1]: 'vix', symbols[2]: 'tnx', symbols[3]: 'rsp', symbols[4]: 'hyg', symbols[5]: 'ief'}, inplace=True)

    if data.empty:
        raise ValueError(f"Failed to download data. Please check symbols or date range.")

    print("2. Engineering features for HMM...")
    data['log_return'] = np.log(data['price'] / data['price'].shift(1))
    data['volatility'] = data['log_return'].rolling(window=vol_window).std() * np.sqrt(252)
    
    for window in ma_windows:
        data[f'dist_from_ma{window}'] = data['price'] / data['price'].rolling(window=window).mean() - 1
    data['dist_from_ma200'] = data['price'] / data['price'].rolling(window=200).mean() - 1

    data['tnx_change'] = data['tnx'].diff()

    for window in mom_windows:
        data[f'momentum_{window}d'] = data['price'].pct_change(window)

    data['market_breadth'] = data['rsp'] / data['price']
    data['credit_risk_ratio'] = data['hyg'] / data['ief']

    feature_cols = ['log_return', 'volatility', 'vix', 'tnx_change'] + [f'dist_from_ma{w}' for w in ma_windows] + ['dist_from_ma200'] + [f'momentum_{w}d' for w in mom_windows] + ['market_breadth', 'credit_risk_ratio']
    feature_df = data[feature_cols].dropna()
    
    return data_raw, data[['price', 'log_return']].loc[feature_df.index], feature_df

def find_best_hmm_model_ultimate(X_train: np.ndarray, n_states_range: range = range(2, 6)):
    """V9终极优化的HMM训练 - 最精确的参数调优"""
    best_model, best_bic = None, np.inf
    best_config = None
    
    # V9的终极参数空间搜索
    configurations = [
        {"covariance_type": "full", "n_iter": 1500, "tol": 1e-5, "random_state": 42},
        {"covariance_type": "full", "n_iter": 2000, "tol": 1e-4, "random_state": 42},
        {"covariance_type": "diag", "n_iter": 1000, "tol": 1e-4, "random_state": 42},
        {"covariance_type": "spherical", "n_iter": 1500, "tol": 1e-4, "random_state": 42},
    ]
    
    for n_components in n_states_range:
        for config in configurations:
            for attempt in range(3):  # 多次尝试确保稳定性
                try:
                    config_copy = config.copy()
                    config_copy["n_components"] = n_components
                    config_copy["random_state"] = 42 + attempt
                    
                    model = GaussianHMM(**config_copy)
                    model.fit(X_train)
                    bic = model.bic(X_train)
                    
                    if bic < best_bic:
                        best_bic, best_model = bic, model
                        best_config = config_copy
                        print(f"   V9 Ultimate: New best model - {n_components} states, BIC={bic:.2f}")
                        print(f"   Config: {config_copy}")
                        
                except Exception as e:
                    continue
                    
                if best_model is not None:
                    break  # 有好的模型就不需要更多尝试
    
    if best_model is None:
        print("   V9 Warning: Using fallback ultimate model")
        try:
            best_model = GaussianHMM(n_components=3, covariance_type="full", n_iter=1000, random_state=42)
            best_model.fit(X_train)
        except:
            raise RuntimeError("Failed to train any HMM model in V9 ultimate.")
    
    return best_model

def analyze_and_map_states_ultimate(model: GaussianHMM, features: list, scaler: StandardScaler):
    """V9终极优化状态分析 - 最完善的状态识别"""
    try:
        state_means_original = scaler.inverse_transform(model.means_)
        state_profiles = pd.DataFrame(state_means_original, columns=features)
        
        print(f"   V9 Ultimate HMM Model: {model.n_components} states with ultimate optimization.")
        print("   Ultimate State Mean Profiles (unscaled):")
        with pd.option_context('display.max_columns', None):
            print(state_profiles.round(4))

        # V9的终极多因子状态识别算法
        # 权重配置：收益率40%，波动率30%，VIX20%，动量10%
        weights = {
            'log_return': 0.4,
            'volatility': -0.3,  # 负权重，波动率高的是熊市
            'vix': -0.2,        # 负权重，VIX高的是熊市
            'momentum_5d': 0.05,
            'momentum_20d': 0.05
        }
        
        # 计算综合得分
        composite_scores = pd.Series(index=range(model.n_components), dtype=float)
        
        for state in range(model.n_components):
            score = 0
            for feature, weight in weights.items():
                if feature in state_profiles.columns:
                    score += state_profiles.loc[state, feature] * weight
            composite_scores[state] = score
        
        # 根据综合得分排序
        sorted_states = composite_scores.sort_values(ascending=False).index.tolist()
        
        state_map = {}
        if len(sorted_states) >= 3:
            state_map[sorted_states[0]] = "Bull Market"      # 最高得分
            state_map[sorted_states[-1]] = "Bear Market"     # 最低得分
            for i in range(1, len(sorted_states) - 1):
                state_map[sorted_states[i]] = "Transition"   # 中间得分
        else:
            state_map[sorted_states[0]] = "Bull Market"
            state_map[sorted_states[-1]] = "Bear Market"
        
        print(f"   V9 Ultimate State Mapping: {state_map}")
        print(f"   Composite Scores: {dict(composite_scores.round(4))}")
        return state_map
        
    except Exception as e:
        print(f"   Error in V9 ultimate state mapping: {e}, using fallback")
        return {0: "Bull Market", 1: "Bear Market"} if model.n_components == 2 else {0: "Bull Market", 1: "Transition", 2: "Bear Market"}

def get_macro_regime_corrected_ultimate(current_date):
    """修正版宏观状态判断 - V9终极逻辑但无前瞻偏差"""
    print("   Building V9 Ultimate Macro-Economic Model (Corrected)...")
    current_dt = pd.to_datetime(current_date)
    
    # V9使用最长的历史数据训练（10年）
    macro_end_date = current_dt
    macro_start_date = current_dt - pd.DateOffset(years=10)
    
    try:
        macro_symbols = ["SPY", "TLT", "GLD"]
        print(f"   Downloading ultimate monthly macro data: {macro_symbols}...")
        macro_data_raw = yf.download(macro_symbols, 
                                   start=macro_start_date.strftime('%Y-%m-%d'),
                                   end=macro_end_date.strftime('%Y-%m-%d'), 
                                   progress=False, interval="1mo")
        
        if macro_data_raw.empty or len(macro_data_raw) < 60:  # V9要求5年最少数据
            print("   Insufficient macro data for V9 ultimate, defaulting to Risk-On")
            return "Risk-On"
        
        macro_data = macro_data_raw['Close'].copy()
        macro_data.columns = [s.lower() for s in macro_data.columns]

        # V9的终极宏观特征工程
        macro_data['spy_tlt_ratio'] = macro_data['spy'] / macro_data['tlt']
        macro_data['gld_spy_ratio'] = macro_data['gld'] / macro_data['spy']
        
        # V9增加多种统计特征
        for window in [3, 6, 12]:
            macro_data[f'spy_tlt_ma{window}'] = macro_data['spy_tlt_ratio'].rolling(window).mean()
            macro_data[f'gld_spy_ma{window}'] = macro_data['gld_spy_ratio'].rolling(window).mean()
            macro_data[f'spy_tlt_std{window}'] = macro_data['spy_tlt_ratio'].rolling(window).std()
            macro_data[f'gld_spy_std{window}'] = macro_data['gld_spy_ratio'].rolling(window).std()
        
        # V9终极特征构造
        macro_features = pd.DataFrame()
        macro_features['spy_tlt_return'] = macro_data['spy_tlt_ratio'].pct_change()
        macro_features['gld_spy_return'] = macro_data['gld_spy_ratio'].pct_change()
        
        # 趋势特征
        macro_features['spy_tlt_trend_short'] = (macro_data['spy_tlt_ratio'] / macro_data['spy_tlt_ma3'] - 1)
        macro_features['spy_tlt_trend_long'] = (macro_data['spy_tlt_ratio'] / macro_data['spy_tlt_ma12'] - 1)
        macro_features['gld_spy_trend_short'] = (macro_data['gld_spy_ratio'] / macro_data['gld_spy_ma3'] - 1)
        macro_features['gld_spy_trend_long'] = (macro_data['gld_spy_ratio'] / macro_data['gld_spy_ma12'] - 1)
        
        # 波动率特征
        macro_features['spy_tlt_vol_short'] = macro_data['spy_tlt_std3']
        macro_features['spy_tlt_vol_long'] = macro_data['spy_tlt_std12']
        
        macro_features = macro_features.dropna()
        
        if len(macro_features) < 36:
            return "Risk-On"
        
        scaler = StandardScaler()
        X_macro = scaler.fit_transform(macro_features)
        
        # V9终极宏观模型训练
        best_macro_model = None
        best_bic = np.inf
        
        configs = [
            {"covariance_type": "full", "n_iter": 1500, "tol": 1e-5},
            {"covariance_type": "full", "n_iter": 2000, "tol": 1e-4},
            {"covariance_type": "diag", "n_iter": 1000, "tol": 1e-4},
        ]
        
        for config in configs:
            for attempt in range(3):
                try:
                    macro_model = GaussianHMM(n_components=2, random_state=42 + attempt, **config)
                    macro_model.fit(X_macro)
                    bic = macro_model.bic(X_macro)
                    
                    if bic < best_bic:
                        best_bic = bic
                        best_macro_model = macro_model
                        print(f"   V9 Ultimate Macro: New best BIC={bic:.2f}")
                        
                except Exception:
                    continue
        
        if best_macro_model is None:
            print("   V9 ultimate macro model training failed, defaulting to Risk-On")
            return "Risk-On"
        
        state_means = scaler.inverse_transform(best_macro_model.means_)
        # V9终极：使用spy_tlt_return作为主要判断依据
        risk_on_state = np.argmax(state_means[:, 0])
        
        # 只预测最后一个时期的状态
        current_features = macro_features.iloc[-1:].values
        current_features_scaled = scaler.transform(current_features)
        current_regime = best_macro_model.predict(current_features_scaled)[0]
        
        regime_result = 'Risk-On' if current_regime == risk_on_state else 'Risk-Off'
        print(f"   V9 Ultimate Macro regime: {regime_result}")
        
        return regime_result
        
    except Exception as e:
        print(f"   V9 ultimate macro model error: {e}, using default Risk-On")
        return "Risk-On"

def run_backtest(full_data, feature_list, start_date, end_date, transaction_cost=0.0001):
    print("\n--- Starting V9 Corrected Ultimate Walk-Forward Backtest ---")
    
    backtest_start_date = pd.to_datetime(start_date)
    backtest_end_date = pd.to_datetime(end_date)
    
    all_signals = []
    
    for year in range(backtest_start_date.year, backtest_end_date.year + 1):
        train_end_date = f"{year-1}-12-31"
        predict_start_date = f"{year}-01-01"
        predict_end_date = f"{year}-12-31"
        
        print(f"\nV9 Ultimate Training for year {year} (data up to {train_end_date})...")
        
        train_df = full_data.loc[:train_end_date]
        predict_df = full_data.loc[predict_start_date:predict_end_date]
        
        if train_df.empty or predict_df.empty or len(train_df) < 252:
            print(f"Skipping year {year} due to insufficient training or prediction data.")
            continue
        
        # 1. V9终极修正版宏观模型
        macro_regime = get_macro_regime_corrected_ultimate(train_end_date)
        
        # 2. V9终极优化的战术模型
        try:
            scaler = StandardScaler()
            X_train = scaler.fit_transform(train_df[feature_list])
            
            hmm_model = find_best_hmm_model_ultimate(X_train)
            state_map = analyze_and_map_states_ultimate(hmm_model, feature_list, scaler)
            
            X_predict = scaler.transform(predict_df[feature_list])
            predicted_states = hmm_model.predict(X_predict)
            
            signals_df = pd.DataFrame(index=predict_df.index)
            signals_df['tactical_regime'] = pd.Series(predicted_states, index=predict_df.index).map(state_map)
            signals_df['macro_regime'] = macro_regime
            all_signals.append(signals_df)
            
        except Exception as e:
            print(f"   V9 Ultimate Error in year {year}: {e}, skipping...")
            continue

    if not all_signals:
        print("Could not generate any signals. V9 Ultimate backtest aborted.")
        return None

    backtest_results = pd.concat(all_signals)
    
    print("\n--- V9 Ultimate: Fusing Macro and Tactical Models & Calculating Performance ---")
    
    backtest_results = full_data[['log_return']].join(backtest_results, how='right')
    backtest_results.dropna(inplace=True)

    # V9的终极仓位逻辑 - 基于收益率的自适应调整
    position_map = {'Bull Market': 1.0, 'Transition': 0.75, 'Bear Market': 0.0}
    backtest_results['base_position'] = backtest_results['tactical_regime'].map(position_map)

    # V9终极创新：自适应Risk-Off限制
    def calculate_adaptive_limit(vix_series):
        vix_percentile_75 = vix_series.rolling(252).quantile(0.75)
        return np.where(vix_series > vix_percentile_75, 0.2, 0.3)
    
    backtest_results = backtest_results.join(full_data[['vix']], how='left')
    adaptive_limits = calculate_adaptive_limit(backtest_results['vix'])
    
    # V9终极宏观过滤逻辑
    backtest_results['final_position'] = np.where(
        backtest_results['macro_regime'] == 'Risk-Off',
        np.minimum(backtest_results['base_position'], adaptive_limits),
        backtest_results['base_position']
    )
    
    backtest_results['trade'] = backtest_results['final_position'].diff().fillna(0).abs()
    backtest_results['strategy_return'] = backtest_results['final_position'].shift(1).fillna(0) * backtest_results['log_return']
    backtest_results['strategy_return'] -= backtest_results['trade'] * transaction_cost
    
    backtest_results['strategy_cumulative'] = np.exp(backtest_results['strategy_return'].cumsum())
    backtest_results['buy_and_hold_cumulative'] = np.exp(backtest_results['log_return'].cumsum())
    
    # V9终极性能计算
    days_in_year = 252
    
    hmm_annual_return = np.exp(backtest_results['strategy_return'].mean() * days_in_year) - 1
    hmm_annual_vol = backtest_results['strategy_return'].std() * np.sqrt(days_in_year)
    hmm_sharpe = hmm_annual_return / hmm_annual_vol if hmm_annual_vol != 0 else 0
    
    bnh_annual_return = np.exp(backtest_results['log_return'].mean() * days_in_year) - 1
    bnh_annual_vol = backtest_results['log_return'].std() * np.sqrt(days_in_year)
    bnh_sharpe = bnh_annual_return / bnh_annual_vol if bnh_annual_vol != 0 else 0
    
    hmm_cum_re_max = backtest_results['strategy_cumulative'].cummax()
    hmm_drawdown = (backtest_results['strategy_cumulative'] - hmm_cum_re_max) / hmm_cum_re_max
    hmm_max_drawdown = hmm_drawdown.min()
    
    bnh_cum_re_max = backtest_results['buy_and_hold_cumulative'].cummax()
    bnh_drawdown = (backtest_results['buy_and_hold_cumulative'] - bnh_cum_re_max) / bnh_cum_re_max
    bnh_max_drawdown = bnh_drawdown.min()

    # V9终极结果显示
    print("\n" + "="*70)
    print("🏆 V9 CORRECTED ULTIMATE BACKTEST RESULTS")
    print(f"Period: {backtest_results.index[0].date()} to {backtest_results.index[-1].date()}")
    print("="*70)
    print(f"{'Metric':<25} | {'V9 Ultimate':<15} | {'Buy and Hold':<15}")
    print("-"*70)
    print(f"{'Annualized Return':<25} | {hmm_annual_return:14.2%} | {bnh_annual_return:14.2%}")
    print(f"{'Annualized Volatility':<25} | {hmm_annual_vol:14.2%} | {bnh_annual_vol:14.2%}")
    print(f"{'Sharpe Ratio':<25} | {hmm_sharpe:14.2f} | {bnh_sharpe:14.2f}")
    print(f"{'Maximum Drawdown':<25} | {hmm_max_drawdown:14.2%} | {bnh_max_drawdown:14.2%}")
    print("="*70)

    plt.style.use('seaborn-v0_8-darkgrid')
    fig, ax = plt.subplots(figsize=(20, 10))
    ax.plot(backtest_results.index, backtest_results['strategy_cumulative'], label='V9 Corrected Ultimate', color='gold', lw=3)
    ax.plot(backtest_results.index, backtest_results['buy_and_hold_cumulative'], label='Buy and Hold', color='black', lw=2, linestyle='--')
    ax.set_title('🏆 V9 Corrected Ultimate vs. Buy and Hold (No Lookahead)', fontsize=18, fontweight='bold')
    ax.set_ylabel('Cumulative Growth of $1', fontsize=14)
    ax.set_xlabel('Date', fontsize=14)
    ax.legend(loc='upper left', fontsize=12)
    ax.grid(True, alpha=0.3)
    plt.tight_layout()
    filename = f"hmm_spy_v9_ultimate_corrected_backtest_plot_{datetime.datetime.now().strftime('%Y%m%d')}.png"
    plt.savefig(filename, dpi=300)
    print(f"\n🎨 V9 Ultimate Backtest chart saved to {filename}")
    plt.close()
    
    return backtest_results

if __name__ == '__main__':
    print("🚀 Starting V9 Corrected Ultimate SPY HMM Strategy")
    print("=" * 60)
    
    SYMBOLS = ["SPY", "^VIX", "^TNX", "RSP", "HYG", "IEF"]
    DATA_START_DATE = "2000-01-01"
    BACKTEST_START_DATE = "2008-01-01"
    BACKTEST_END_DATE = datetime.datetime.today().strftime('%Y-%m-%d')
    
    FEATURE_LIST = ['log_return', 'volatility', 'vix', 'tnx_change', 'dist_from_ma55', 'dist_from_ma233', 'dist_from_ma200', 'momentum_5d', 'momentum_20d', 'market_breadth', 'credit_risk_ratio']
    
    raw_data, price_data, feature_data = download_and_prepare_features(
        symbols=SYMBOLS, 
        start_date=DATA_START_DATE, 
        end_date=BACKTEST_END_DATE
    )
    
    full_data_for_backtest = price_data.join(feature_data.drop(columns=['log_return']))

    results = run_backtest(full_data_for_backtest, FEATURE_LIST, BACKTEST_START_DATE, BACKTEST_END_DATE)
    
    if results is not None:
        results_filename = f"spy_hmm_v9_ultimate_corrected_results_{datetime.datetime.now().strftime('%Y%m%d_%H%M')}.csv"
        results.to_csv(results_filename)
        print(f"🏆 V9 Ultimate Results saved: {results_filename}")
        print("✅ V9 Corrected Ultimate Analysis Complete!")
    else:
        print("❌ V9 Ultimate Analysis Failed!")