import pandas as pd
import numpy as np
import yfinance as yf
import matplotlib.pyplot as plt
from matplotlib.patches import Patch
from sklearn.preprocessing import StandardScaler
from hmmlearn.hmm import GaussianHMM
import warnings
import datetime

warnings.filterwarnings('ignore')

def download_and_prepare_features(symbols, start_date="2000-01-01", end_date=None, vol_window=21, ma_windows=[55, 233], mom_windows=[5, 20]):
    if end_date is None:
        end_date = datetime.datetime.today().strftime('%Y-%m-%d')
    
    print(f"1. Downloading daily data for {symbols} from {start_date} to {end_date}...")
    data = yf.download(symbols, start=start_date, end=end_date, progress=False)['Close']
    data.rename(columns={symbols[0]: 'price', symbols[1]: 'vix', symbols[2]: 'tnx', symbols[3]: 'rsp', symbols[4]: 'hyg', symbols[5]: 'ief'}, inplace=True)

    if data.empty:
        raise ValueError(f"Failed to download data. Please check symbols or date range.")

    print("2. Engineering features for HMM...")
    data['log_return'] = np.log(data['price'] / data['price'].shift(1))
    data['volatility'] = data['log_return'].rolling(window=vol_window).std() * np.sqrt(252)
    
    # New MA features based on your suggestion
    for window in ma_windows:
        data[f'dist_from_ma{window}'] = data['price'] / data['price'].rolling(window=window).mean() - 1
    data['dist_from_ma200'] = data['price'] / data['price'].rolling(window=200).mean() - 1

    data['tnx_change'] = data['tnx'].diff()

    for window in mom_windows:
        data[f'momentum_{window}d'] = data['price'].pct_change(window)

    data['market_breadth'] = data['rsp'] / data['price']
    data['credit_risk_ratio'] = data['hyg'] / data['ief']

    # Consolidate features, replacing the old MA feature with the new ones
    feature_cols = ['log_return', 'volatility', 'vix', 'tnx_change'] + [f'dist_from_ma{w}' for w in ma_windows] + ['dist_from_ma200'] + [f'momentum_{w}d' for w in mom_windows] + ['market_breadth', 'credit_risk_ratio']
    feature_df = data[feature_cols].dropna()
    
    return data[['price', 'log_return']].loc[feature_df.index], feature_df

def find_best_hmm_model(X_train: np.ndarray, n_states_range: range = range(2, 5)):
    best_model, best_bic = None, np.inf
    for n_components in n_states_range:
        try:
            model = GaussianHMM(n_components=n_components, covariance_type="full", n_iter=1000, random_state=42)
            model.fit(X_train)
            bic = model.bic(X_train)
            if bic < best_bic:
                best_bic, best_model = bic, model
        except Exception as e:
            print(f"   Error fitting model with {n_components} states: {e}")
    if best_model is None:
        raise RuntimeError("Failed to train any HMM model.")
    return best_model

def analyze_and_map_states(model: GaussianHMM, features: list, scaler: StandardScaler):
    state_means_original = scaler.inverse_transform(model.means_)
    state_profiles = pd.DataFrame(state_means_original, columns=features)
    
    print(f"   HMM Model chose {model.n_components} states.")
    print("   State Mean Profiles (unscaled):")
    with pd.option_context('display.max_columns', None):
        print(state_profiles)

    # Current simple mapping based on volatility
    bull_state = state_profiles['volatility'].idxmin()
    bear_state = state_profiles['volatility'].idxmax()
    
    state_map = {bull_state: "Bull Market", bear_state: "Bear Market"}
    for i in range(model.n_components):
        if i not in state_map:
            state_map[i] = "Transition" # Default for any other states
    
    print(f"   Initial State Mapping: {state_map}")
    return state_map

def download_and_prepare_features(tactical_symbols, macro_symbols, start_date, end_date):
    print("1. Downloading ALL necessary data...")
    
    # Download daily data for tactical model
    daily_data = yf.download(tactical_symbols, start=start_date, end=end_date, progress=False)['Close']
    daily_data.rename(columns={tactical_symbols[0]: 'price', tactical_symbols[1]: 'vix', tactical_symbols[2]: 'tnx', tactical_symbols[3]: 'rsp', tactical_symbols[4]: 'hyg', tactical_symbols[5]: 'ief'}, inplace=True)

    # Download monthly data for macro model
    monthly_data = yf.download(macro_symbols, start=start_date, end=end_date, progress=False, interval='1mo')['Close']

    if daily_data.empty or monthly_data.empty:
        raise ValueError("Failed to download data.")

    # --- Tactical Feature Engineering (Daily) ---
    print("2. Engineering TACTICAL features (Daily)...")
    daily_data['log_return'] = np.log(daily_data['price'] / daily_data['price'].shift(1))
    daily_data['volatility'] = daily_data['log_return'].rolling(21).std() * np.sqrt(252)
    for window in [55, 233, 200]:
        daily_data[f'dist_from_ma{window}'] = daily_data['price'] / daily_data['price'].rolling(window).mean() - 1
    daily_data['tnx_change'] = daily_data['tnx'].diff()
    for window in [5, 20]:
        daily_data[f'momentum_{window}d'] = daily_data['price'].pct_change(window)
    daily_data['market_breadth'] = daily_data['rsp'] / daily_data['price']
    daily_data['credit_risk_ratio'] = daily_data['hyg'] / daily_data['ief']
    tactical_features = ['log_return', 'volatility', 'vix', 'tnx_change', 'dist_from_ma55', 'dist_from_ma233', 'dist_from_ma200', 'momentum_5d', 'momentum_20d', 'market_breadth', 'credit_risk_ratio']
    
    # --- Macro Feature Engineering (Monthly) ---
    print("3. Engineering MACRO features (Monthly)...")
    macro_features = pd.DataFrame(index=monthly_data.index)
    macro_features['stock_bond_momentum'] = (monthly_data['SPY'] / monthly_data['TLT']).pct_change(3)
    macro_features['gold_stock_momentum'] = (monthly_data['GLD'] / monthly_data['SPY']).pct_change(3)
    macro_features['commodity_momentum'] = monthly_data['DBC'].pct_change(6)
    spy_returns = monthly_data['SPY'].pct_change()
    macro_features['market_volatility'] = spy_returns.rolling(6).std() * np.sqrt(12)

    return daily_data.dropna(), macro_features.dropna()

def run_backtest(full_data, feature_list, start_date, end_date, transaction_cost=0.0001):
    print("\n--- This function is now deprecated. Please use the new main execution block. ---")
    return None
    
    hmm_cum_re_max = backtest_results['strategy_cumulative'].cummax()
    hmm_drawdown = (backtest_results['strategy_cumulative'] - hmm_cum_re_max) / hmm_cum_re_max
    hmm_max_drawdown = hmm_drawdown.min()
    
    bnh_cum_re_max = backtest_results['buy_and_hold_cumulative'].cummax()
    bnh_drawdown = (backtest_results['buy_and_hold_cumulative'] - bnh_cum_re_max) / bnh_cum_re_max
    bnh_max_drawdown = bnh_drawdown.min()

    print("\n" + "="*50)
    print("Backtest Results")
    print(f"Period: {backtest_results.index[0].date()} to {backtest_results.index[-1].date()}")
    print("="*50)
    print(f"{'Metric':<25} | {'HMM Strategy':<15} | {'Buy and Hold':<15}")
    print("-"*55)
    print(f"{'Annualized Return':<25} | {hmm_annual_return:14.2%} | {bnh_annual_return:14.2%}")
    print(f"{'Annualized Volatility':<25} | {hmm_annual_vol:14.2%} | {bnh_annual_vol:14.2%}")
    print(f"{'Sharpe Ratio':<25} | {hmm_sharpe:14.2f} | {bnh_sharpe:14.2f}")
    print(f"{'Maximum Drawdown':<25} | {hmm_max_drawdown:14.2%} | {bnh_max_drawdown:14.2%}")
    print("="*55)

    plt.style.use('seaborn-v0_8-darkgrid')
    fig, ax = plt.subplots(figsize=(18, 9))
    ax.plot(backtest_results.index, backtest_results['strategy_cumulative'], label='HMM Strategy', color='royalblue', lw=2)
    ax.plot(backtest_results.index, backtest_results['buy_and_hold_cumulative'], label='Buy and Hold', color='black', lw=2, linestyle='--')
    ax.set_title('HMM Strategy vs. Buy and Hold - Cumulative Returns', fontsize=16, fontweight='bold')
    ax.set_ylabel('Cumulative Growth of $1')
    ax.set_xlabel('Date')
    ax.legend(loc='upper left')
    ax.grid(True)
    plt.tight_layout()
    filename = f"hmm_spy_backtest_plot_{datetime.datetime.now().strftime('%Y%m%d')}.png"
    plt.savefig(filename)
    print(f"\nBacktest chart saved to {filename}")
    plt.close()

if __name__ == '__main__':
    # --- Configuration ---
    TACTICAL_SYMBOLS = ["SPY", "^VIX", "^TNX", "RSP", "HYG", "IEF"]
    MACRO_SYMBOLS = ["SPY", "TLT", "GLD", "DBC"]
    DATA_START_DATE = "2007-01-01"
    BACKTEST_START_DATE = "2008-01-01"
    BACKTEST_END_DATE = datetime.datetime.today().strftime('%Y-%m-%d')
    TRANSACTION_COST = 0.0001

    # 1. Download and Prepare Data
    daily_data, monthly_macro_features = download_and_prepare_features(
        tactical_symbols=TACTICAL_SYMBOLS,
        macro_symbols=MACRO_SYMBOLS,
        start_date=DATA_START_DATE,
        end_date=BACKTEST_END_DATE
    )

    # 2. Pre-calculate Macro Regimes
    print("\n4. Pre-calculating all Macro Regimes...")
    X_macro = StandardScaler().fit_transform(monthly_macro_features)
    macro_model = GaussianHMM(n_components=2, covariance_type="full", random_state=42)
    macro_model.fit(X_macro)
    macro_regimes = macro_model.predict(X_macro)
    risk_on_state = np.argmax(macro_model.means_[:, 0]) # State with higher stock/bond momentum is Risk-On
    monthly_macro_features['macro_regime'] = ["Risk-On" if s == risk_on_state else "Risk-Off" for s in macro_regimes]
    print("Macro regime calculation complete.")

    # 3. Align Macro Regimes with Daily Data
    daily_data['macro_regime'] = monthly_macro_features['macro_regime'].reindex(daily_data.index, method='ffill')
    daily_data.dropna(inplace=True)

    # 4. Run Walk-Forward Backtest for Tactical Model
    print("\n5. Starting HYBRID Walk-Forward Backtest (Daily+Monthly)...")
    all_signals = []
    tactical_feature_list = ['log_return', 'volatility', 'vix', 'tnx_change', 'dist_from_ma55', 'dist_from_ma233', 'dist_from_ma200', 'momentum_5d', 'momentum_20d', 'market_breadth', 'credit_risk_ratio']

    for year in range(pd.to_datetime(BACKTEST_START_DATE).year, pd.to_datetime(BACKTEST_END_DATE).year + 1):
        train_end_date = f"{year-1}-12-31"
        predict_start_date = f"{year}-01-01"
        predict_end_date = f"{year}-12-31"
        
        print(f"\n--- Tactical Model Training for Year {year} ---")
        
        train_df = daily_data.loc[:train_end_date]
        predict_df = daily_data.loc[predict_start_date:predict_end_date]
        
        if train_df.empty or predict_df.empty or len(train_df) < 252:
            print(f"Skipping year {year} due to insufficient tactical data.")
            continue
            
        scaler = StandardScaler()
        X_train = scaler.fit_transform(train_df[tactical_feature_list])
        
        tactical_model = find_best_hmm_model(X_train)
        tactical_state_map = analyze_and_map_states(tactical_model, tactical_feature_list, scaler)
        
        X_predict = scaler.transform(predict_df[tactical_feature_list])
        predicted_states = tactical_model.predict(X_predict)
        
        signals_df = pd.DataFrame(index=predict_df.index)
        signals_df['tactical_regime'] = pd.Series(predicted_states, index=predict_df.index).map(tactical_state_map)
        all_signals.append(signals_df)

    if not all_signals:
        raise RuntimeError("Could not generate any tactical signals.")

    backtest_results = pd.concat(all_signals)
    
    # 5. Combine Signals and Calculate Final Performance
    print("\n6. Combining signals and calculating final performance...")
    final_results = daily_data.join(backtest_results, how='right')
    final_results.dropna(inplace=True)

    tactical_position_map = {'Bull Market': 1.0, 'Transition': 0.75, 'Bear Market': 0.0}
    final_results['tactical_position'] = final_results['tactical_regime'].map(tactical_position_map)

    def final_position(row):
        if row['macro_regime'] == "Risk-Off":
            return min(0.3, row['tactical_position'])
        return row['tactical_position']

    final_results['position'] = final_results.apply(final_position, axis=1)

    final_results['trade'] = final_results['position'].diff().fillna(0).abs()
    final_results['strategy_return'] = final_results['position'].shift(1).fillna(0) * final_results['log_return']
    final_results['strategy_return'] -= final_results['trade'] * TRANSACTION_COST
    
    final_results['strategy_cumulative'] = np.exp(final_results['strategy_return'].cumsum())
    final_results['buy_and_hold_cumulative'] = np.exp(final_results['log_return'].cumsum())
    
    # 6. Final Performance Metrics
    days_in_year = 252
    hmm_annual_return = np.exp(final_results['strategy_return'].mean() * days_in_year) - 1
    hmm_annual_vol = final_results['strategy_return'].std() * np.sqrt(days_in_year)
    hmm_sharpe = hmm_annual_return / hmm_annual_vol if hmm_annual_vol != 0 else 0
    
    bnh_annual_return = np.exp(final_results['log_return'].mean() * days_in_year) - 1
    bnh_annual_vol = final_results['log_return'].std() * np.sqrt(days_in_year)
    bnh_sharpe = bnh_annual_return / bnh_annual_vol if bnh_annual_vol != 0 else 0
    
    hmm_cum_re_max = final_results['strategy_cumulative'].cummax()
    hmm_drawdown = (final_results['strategy_cumulative'] - hmm_cum_re_max) / hmm_cum_re_max
    hmm_max_drawdown = hmm_drawdown.min()
    
    bnh_cum_re_max = final_results['buy_and_hold_cumulative'].cummax()
    bnh_drawdown = (final_results['buy_and_hold_cumulative'] - bnh_cum_re_max) / bnh_cum_re_max
    bnh_max_drawdown = bnh_drawdown.min()

    print("\n" + "="*50)
    print("Final Backtest Results (Hybrid Model)")
    print(f"Period: {final_results.index[0].date()} to {final_results.index[-1].date()}")
    print("="*50)
    print(f"{'Metric':<25} | {'HMM Strategy':<15} | {'Buy and Hold':<15}")
    print("-"*55)
    print(f"{'Annualized Return':<25} | {hmm_annual_return:14.2%} | {bnh_annual_return:14.2%}")
    print(f"{'Annualized Volatility':<25} | {hmm_annual_vol:14.2%} | {bnh_annual_vol:14.2%}")
    print(f"{'Sharpe Ratio':<25} | {hmm_sharpe:14.2f} | {bnh_sharpe:14.2f}")
    print(f"{'Maximum Drawdown':<25} | {hmm_max_drawdown:14.2%} | {bnh_max_drawdown:14.2%}")
    print("="*55)

    plt.style.use('seaborn-v0_8-darkgrid')
    fig, ax = plt.subplots(figsize=(18, 9))
    ax.plot(final_results.index, final_results['strategy_cumulative'], label='HMM Strategy', color='royalblue', lw=2)
    ax.plot(final_results.index, final_results['buy_and_hold_cumulative'], label='Buy and Hold', color='black', lw=2, linestyle='--')
    ax.set_title('HMM Strategy vs. Buy and Hold - Cumulative Returns', fontsize=16, fontweight='bold')
    ax.set_ylabel('Cumulative Growth of $1')
    ax.set_xlabel('Date')
    ax.legend(loc='upper left')
    ax.grid(True)
    plt.tight_layout()
    filename = f"hmm_spy_backtest_plot_{datetime.datetime.now().strftime('%Y%m%d')}.png"
    plt.savefig(filename)
    print(f"\nBacktest chart saved to {filename}")
    plt.close()