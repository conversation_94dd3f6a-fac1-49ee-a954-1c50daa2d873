#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SPY HMM V5.1 准确版本 - 重训频率对比分析
=====================================

完全基于V5最优版本的逻辑，准确实现不同重训频率的对比分析
确保所有参数、特征、回测期间都与V5最优版本一致
"""

import pandas as pd
import numpy as np
import yfinance as yf
import matplotlib.pyplot as plt
from sklearn.preprocessing import StandardScaler
from hmmlearn.hmm import GaussianHMM
import warnings
import datetime

warnings.filterwarnings('ignore')

def download_and_prepare_features_v5(symbols, start_date="2000-01-01", end_date=None):
    """V5特征工程 - 与最优版本完全一致"""
    if end_date is None:
        end_date = datetime.datetime.today().strftime('%Y-%m-%d')
    
    print(f"📊 下载数据: {symbols}")
    
    data = yf.download(symbols, start=start_date, end=end_date, progress=False)['Close']
    data.rename(columns={
        symbols[0]: 'price', symbols[1]: 'vix', symbols[2]: 'tnx', 
        symbols[3]: 'rsp', symbols[4]: 'hyg', symbols[5]: 'ief'
    }, inplace=True)
    
    if data.empty:
        raise ValueError("数据下载失败")
    
    print("🔧 计算特征 (与V5最优版本一致)...")
    
    # 完全一致的特征工程
    data['log_return'] = np.log(data['price'] / data['price'].shift(1))
    data['volatility'] = data['log_return'].rolling(window=21, min_periods=21).std() * np.sqrt(252)
    
    # MA距离特征
    data['dist_from_ma55'] = data['price'] / data['price'].rolling(window=55, min_periods=55).mean() - 1
    data['dist_from_ma233'] = data['price'] / data['price'].rolling(window=233, min_periods=233).mean() - 1
    data['dist_from_ma200'] = data['price'] / data['price'].rolling(window=200, min_periods=200).mean() - 1
    
    # 其他特征
    data['tnx_change'] = data['tnx'].diff()
    data['momentum_5d'] = data['price'].pct_change(5)
    data['momentum_20d'] = data['price'].pct_change(20)
    data['market_breadth'] = data['rsp'] / data['price']
    data['credit_risk_ratio'] = data['hyg'] / data['ief']
    
    feature_columns = [
        'log_return', 'volatility', 'vix', 'tnx_change',
        'dist_from_ma55', 'dist_from_ma233', 'dist_from_ma200',
        'momentum_5d', 'momentum_20d', 'market_breadth', 'credit_risk_ratio'
    ]
    
    feature_df = data[feature_columns].dropna()
    
    return data[['price', 'log_return']].loc[feature_df.index], feature_df

def train_hmm_model(features_df, n_states_range=[2, 3, 4]):
    """训练HMM模型 - 与V5最优版本完全一致"""
    scaler = StandardScaler()
    X = scaler.fit_transform(features_df)
    
    best_model = None
    best_bic = np.inf
    
    for n_states in n_states_range:
        try:
            model = GaussianHMM(n_components=n_states, covariance_type="full", 
                              n_iter=1000, random_state=42)
            model.fit(X)
            bic = model.bic(X)
            
            if bic < best_bic:
                best_bic = bic
                best_model = model
        except:
            continue
    
    return best_model, scaler

def calculate_historical_state_performance(historical_data, model, scaler, feature_cols):
    """计算历史状态表现 - 与V5最优版本完全一致"""
    try:
        features = historical_data[feature_cols].dropna()
        if len(features) < 252:
            return {}
        
        X = scaler.transform(features)
        states = model.predict(X)
        
        # 计算每个状态的历史表现
        state_performance = {}
        for state_id in range(model.n_components):
            state_mask = (states == state_id)
            if state_mask.sum() < 20:
                continue
            
            state_returns = features.loc[features.index[state_mask], 'log_return']
            state_vols = features.loc[features.index[state_mask], 'volatility']
            state_vix = features.loc[features.index[state_mask], 'vix']
            
            # 综合评分 - 与V5最优版本一致
            avg_return = float(state_returns.mean())
            avg_vol = float(state_vols.mean()) 
            avg_vix = float(state_vix.mean())
            
            # 风险调整收益评分
            return_score = avg_return * 1000
            risk_penalty = -avg_vol * 5
            vix_penalty = -max(0, avg_vix - 20) * 2
            
            total_score = return_score + risk_penalty + vix_penalty
            
            state_performance[state_id] = {
                'score': total_score,
                'return': avg_return,
                'vol': avg_vol,
                'vix': avg_vix,
                'frequency': float(state_mask.sum() / len(states))
            }
        
        return state_performance
    
    except Exception as e:
        return {}

def map_states_to_regimes(state_performance):
    """基于历史表现映射状态到交易状态 - 与V5最优版本完全一致"""
    if not state_performance:
        return {}, {}
    
    # 按得分排序
    sorted_states = sorted(state_performance.items(), key=lambda x: x[1]['score'])
    
    state_map = {}
    position_map = {}
    
    n_states = len(sorted_states)
    
    if n_states == 2:
        state_map[sorted_states[0][0]] = "Bear Market"
        state_map[sorted_states[1][0]] = "Bull Market"
        position_map = {"Bear Market": 0.0, "Bull Market": 1.0}
    elif n_states == 3:
        state_map[sorted_states[0][0]] = "Bear Market"
        state_map[sorted_states[1][0]] = "Transition"
        state_map[sorted_states[2][0]] = "Bull Market"
        position_map = {"Bear Market": 0.0, "Transition": 0.8, "Bull Market": 1.0}
    else:
        # 4个或更多状态
        for idx, (state_id, perf) in enumerate(sorted_states):
            if idx == 0:
                state_map[state_id] = "Bear Market"
                position_map["Bear Market"] = 0.0
            elif idx == n_states - 1:
                state_map[state_id] = "Bull Market"
                position_map["Bull Market"] = 1.0
            else:
                label = f"Transition_{idx}"
                state_map[state_id] = label
                position_map[label] = 0.5 + 0.4 * (idx / (n_states - 1))
    
    return state_map, position_map

def run_v5_backtest_with_frequency(price_data, feature_data, start_date, end_date, retraining_days, freq_name):
    """运行指定重训频率的回测 - 基于V5最优版本逻辑"""
    print(f"🚀 开始{freq_name}回测 (每{retraining_days}天重训)...")
    
    backtest_start = pd.to_datetime(start_date)
    backtest_end = pd.to_datetime(end_date)
    
    # 筛选回测数据
    full_data = price_data.join(feature_data.drop(columns=['log_return']))
    backtest_data = full_data.loc[backtest_start:backtest_end]
    
    results = []
    current_model = None
    current_scaler = None
    current_state_map = {}
    current_position_map = {}
    last_retrain_date = None
    retrain_count = 0
    
    print(f"   回测期间: {backtest_data.index[0].date()} 到 {backtest_data.index[-1].date()}")
    
    for i, current_date in enumerate(backtest_data.index):
        
        # 获取历史数据 (不包含当天)
        historical_data = full_data.loc[:current_date].iloc[:-1]
        
        if len(historical_data) < 756:  # 需要至少3年历史数据
            continue
        
        # 检查是否需要重新训练
        should_retrain = (
            current_model is None or
            last_retrain_date is None or
            (current_date - last_retrain_date).days >= retraining_days
        )
        
        if should_retrain:
            retrain_count += 1
            if retrain_count <= 5 or retrain_count % 10 == 0:  # 减少输出但保持信息
                print(f"   📚 重训 #{retrain_count}: {current_date.date()}")
            
            try:
                # 使用历史数据训练模型
                train_features = historical_data[feature_data.columns.tolist()]
                train_features = train_features.dropna()
                
                if len(train_features) < 756:
                    continue
                
                # 训练HMM模型
                model, scaler = train_hmm_model(train_features)
                if model is None:
                    continue
                
                # 计算历史状态表现
                state_perf = calculate_historical_state_performance(
                    historical_data, model, scaler, feature_data.columns.tolist()
                )
                
                if not state_perf:
                    continue
                
                # 映射状态
                state_map, position_map = map_states_to_regimes(state_perf)
                
                if not state_map:
                    continue
                
                # 更新当前模型和映射
                current_model = model
                current_scaler = scaler
                current_state_map = state_map
                current_position_map = position_map
                last_retrain_date = current_date
                
                # 只在前3次重训时输出详细信息
                if retrain_count <= 3:
                    print(f"     状态映射: {state_map}")
                    print(f"     仓位配置: {position_map}")
                
            except Exception as e:
                if retrain_count <= 3:
                    print(f"     模型训练失败: {e}")
                continue
        
        # 使用当前模型进行预测
        if current_model is not None and current_scaler is not None:
            try:
                # 获取当日特征
                current_features = backtest_data.loc[[current_date], feature_data.columns.tolist()]
                if current_features.isnull().any().any():
                    continue
                
                # 预测状态
                X_current = current_scaler.transform(current_features)
                predicted_state = current_model.predict(X_current)[0]
                
                # 获取交易信号
                regime = current_state_map.get(predicted_state, "Unknown")
                position = current_position_map.get(regime, 0.5)
                
                results.append({
                    'date': current_date,
                    'regime': regime,
                    'position': position,
                    'predicted_state': predicted_state,
                    'log_return': backtest_data.loc[current_date, 'log_return']
                })
                
            except Exception as e:
                continue
    
    if not results:
        print(f"   ❌ {freq_name}无法生成交易信号")
        return None, 0
    
    results_df = pd.DataFrame(results)
    results_df.set_index('date', inplace=True)
    
    print(f"   ✅ {freq_name}: 生成{len(results_df)}个信号, 重训{retrain_count}次")
    return results_df, retrain_count

def calculate_performance(results_df, freq_name):
    """计算策略表现 - 与V5最优版本完全一致"""
    # 策略收益
    results_df['trade'] = results_df['position'].diff().fillna(0).abs()
    results_df['strategy_return'] = (
        results_df['position'].shift(1).fillna(0) * results_df['log_return']
    )
    results_df['strategy_return'] -= results_df['trade'] * 0.0001  # 交易成本
    
    # 累计收益
    results_df['strategy_cumulative'] = np.exp(results_df['strategy_return'].cumsum())
    results_df['buy_and_hold_cumulative'] = np.exp(results_df['log_return'].cumsum())
    
    # 年化指标
    days_in_year = 252
    
    hmm_annual_return = np.exp(results_df['strategy_return'].mean() * days_in_year) - 1
    hmm_annual_vol = results_df['strategy_return'].std() * np.sqrt(days_in_year)
    hmm_sharpe = hmm_annual_return / hmm_annual_vol if hmm_annual_vol != 0 else 0
    
    bnh_annual_return = np.exp(results_df['log_return'].mean() * days_in_year) - 1
    bnh_annual_vol = results_df['log_return'].std() * np.sqrt(days_in_year)
    bnh_sharpe = bnh_annual_return / bnh_annual_vol if bnh_annual_vol != 0 else 0
    
    # 最大回撤
    hmm_cum_max = results_df['strategy_cumulative'].cummax()
    hmm_drawdown = (results_df['strategy_cumulative'] - hmm_cum_max) / hmm_cum_max
    hmm_max_drawdown = hmm_drawdown.min()
    
    bnh_cum_max = results_df['buy_and_hold_cumulative'].cummax()
    bnh_drawdown = (results_df['buy_and_hold_cumulative'] - bnh_cum_max) / bnh_cum_max
    bnh_max_drawdown = bnh_drawdown.min()
    
    return {
        'freq_name': freq_name,
        'hmm_annual_return': hmm_annual_return,
        'hmm_annual_vol': hmm_annual_vol,
        'hmm_sharpe': hmm_sharpe,
        'hmm_max_drawdown': hmm_max_drawdown,
        'bnh_annual_return': bnh_annual_return,
        'bnh_annual_vol': bnh_annual_vol,
        'bnh_sharpe': bnh_sharpe,
        'bnh_max_drawdown': bnh_max_drawdown,
        'results_df': results_df
    }

def main():
    """主函数"""
    try:
        print("🚀 SPY HMM V5.1 准确版本 - 重训频率对比分析")
        print("基于V5最优版本，确保所有参数和逻辑完全一致")
        print("="*60)
        
        # 配置 - 与V5最优版本完全一致
        SYMBOLS = ["SPY", "^VIX", "^TNX", "RSP", "HYG", "IEF"]
        DATA_START_DATE = "2000-01-01"
        BACKTEST_START_DATE = "2010-01-01"  # 与V5最优版本一致
        BACKTEST_END_DATE = datetime.datetime.today().strftime('%Y-%m-%d')
        
        # 数据准备
        price_data, feature_data = download_and_prepare_features_v5(
            SYMBOLS, DATA_START_DATE, BACKTEST_END_DATE
        )
        
        # 重训频率配置
        frequencies = [
            (252, "年度重训"),
            (126, "半年重训"),  # 新增半年重训
            (63, "季度重训"),   # V5最优版本使用的频率
            (21, "月度重训")
        ]
        
        # 运行所有频率的回测
        all_results = {}
        all_performances = {}
        
        for retraining_days, freq_name in frequencies:
            try:
                result = run_v5_backtest_with_frequency(
                    price_data, feature_data, BACKTEST_START_DATE, BACKTEST_END_DATE, 
                    retraining_days, freq_name
                )
                
                if result[0] is not None:
                    results_df, retrain_count = result
                    performance = calculate_performance(results_df, freq_name)
                    performance['retrain_count'] = retrain_count
                    performance['avg_position'] = results_df['position'].mean()
                    performance['total_trades'] = results_df['trade'].sum()
                    
                    all_results[freq_name] = results_df
                    all_performances[freq_name] = performance
                
            except Exception as e:
                print(f"❌ {freq_name} 失败: {e}")
                continue
        
        if not all_performances:
            print("❌ 没有成功的回测结果")
            return
        
        # 结果展示
        print(f"\n📊 重训频率对比分析结果 (2010-2025)")
        print("="*85)
        print(f"{'策略':<12} | {'年化收益':<8} | {'夏普比率':<8} | {'最大回撤':<8} | {'重训次数':<8} | {'平均仓位':<8}")
        print("-"*85)
        
        # 特别标注V5最优版本的结果
        for name, perf in all_performances.items():
            marker = " ★" if name == "季度重训" else "  "
            print(f"{name:<12}{marker} | {perf['hmm_annual_return']:>7.2%} | {perf['hmm_sharpe']:>7.2f} | "
                  f"{perf['hmm_max_drawdown']:>7.2%} | {perf['retrain_count']:>7d} | "
                  f"{perf['avg_position']:>7.1%}")
        
        print("="*85)
        print("★ 季度重训 = V5最优版本基准")
        
        # 验证季度重训结果
        if "季度重训" in all_performances:
            quarterly_perf = all_performances["季度重训"]
            print(f"\n🔍 V5最优版本验证:")
            print(f"   期望年化收益: 12.29%")
            print(f"   实际年化收益: {quarterly_perf['hmm_annual_return']:.2%}")
            
            diff = abs(quarterly_perf['hmm_annual_return'] - 0.1229)
            if diff < 0.005:  # 允许0.5%的误差
                print(f"   ✅ 验证通过! 差异: {diff:.3%}")
            else:
                print(f"   ⚠️ 存在差异: {diff:.3%}")
        
        # 绘制对比图
        if len(all_results) > 1:
            plt.figure(figsize=(16, 10))
            
            # 累计收益对比
            plt.subplot(2, 2, 1)
            colors = ['blue', 'purple', 'green', 'orange']
            for i, (name, results_df) in enumerate(all_results.items()):
                line_style = '-' if name == "季度重训" else '-'
                line_width = 3 if name == "季度重训" else 2
                plt.plot(results_df.index, results_df['strategy_cumulative'], 
                        label=name, color=colors[i], linewidth=line_width, linestyle=line_style)
            
            # 买入持有基准
            sample_df = list(all_results.values())[0]
            plt.plot(sample_df.index, sample_df['buy_and_hold_cumulative'], 
                    label='买入持有', color='black', linestyle='--', linewidth=2)
            
            plt.title('累计收益对比', fontsize=12, fontweight='bold')
            plt.ylabel('累计收益倍数')
            plt.legend()
            plt.grid(True, alpha=0.3)
            
            # 年化收益对比
            plt.subplot(2, 2, 2)
            names = list(all_performances.keys())
            returns = [perf['hmm_annual_return'] for perf in all_performances.values()]
            colors_bar = ['green' if name == "季度重训" else 'lightblue' for name in names]
            
            bars = plt.bar(names, returns, color=colors_bar, alpha=0.7)
            plt.title('年化收益对比', fontsize=12, fontweight='bold')
            plt.ylabel('年化收益率')
            plt.xticks(rotation=45)
            
            # 添加数值标签
            for bar, ret in zip(bars, returns):
                plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.002, 
                        f'{ret:.2%}', ha='center', va='bottom', fontsize=10)
            
            # 夏普比率对比
            plt.subplot(2, 2, 3)
            sharpe_ratios = [perf['hmm_sharpe'] for perf in all_performances.values()]
            colors_sharpe = ['green' if name == "季度重训" else 'lightcoral' for name in names]
            
            bars_sharpe = plt.bar(names, sharpe_ratios, color=colors_sharpe, alpha=0.7)
            plt.title('夏普比率对比', fontsize=12, fontweight='bold')
            plt.ylabel('夏普比率')
            plt.xticks(rotation=45)
            
            # 添加数值标签
            for bar, sharpe in zip(bars_sharpe, sharpe_ratios):
                plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01, 
                        f'{sharpe:.2f}', ha='center', va='bottom', fontsize=10)
            
            # 重训次数对比
            plt.subplot(2, 2, 4)
            retrain_counts = [perf['retrain_count'] for perf in all_performances.values()]
            colors_retrain = ['green' if name == "季度重训" else 'lightyellow' for name in names]
            
            bars_retrain = plt.bar(names, retrain_counts, color=colors_retrain, alpha=0.7)
            plt.title('重训次数对比', fontsize=12, fontweight='bold')
            plt.ylabel('重训次数')
            plt.xticks(rotation=45)
            
            # 添加数值标签
            for bar, count in zip(bars_retrain, retrain_counts):
                plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 1, 
                        str(count), ha='center', va='bottom', fontsize=10)
            
            plt.tight_layout()
            filename = f"spy_hmm_v5.1_accurate_{datetime.datetime.now().strftime('%Y%m%d_%H%M')}.png"
            plt.savefig(filename, dpi=300, bbox_inches='tight')
            print(f"\n📊 对比图表已保存: {filename}")
            plt.close()
        
        # 底层逻辑分析
        print(f"\n🔍 重训频率底层逻辑分析:")
        
        performances_list = list(all_performances.values())
        performances_list.sort(key=lambda x: x['retrain_count'])
        
        print(f"\n1️⃣ 重训频率与表现关系:")
        for perf in performances_list:
            marker = " ★" if perf['freq_name'] == "季度重训" else ""
            print(f"   {perf['freq_name']}{marker}: 重训{perf['retrain_count']}次 -> "
                  f"收益{perf['hmm_annual_return']:.2%}, 夏普{perf['hmm_sharpe']:.2f}")
        
        best_sharpe = max(performances_list, key=lambda x: x['hmm_sharpe'])
        best_return = max(performances_list, key=lambda x: x['hmm_annual_return'])
        
        print(f"\n2️⃣ 最优表现:")
        print(f"   📈 最高年化收益: {best_return['freq_name']} ({best_return['hmm_annual_return']:.2%})")
        print(f"   📊 最高夏普比率: {best_sharpe['freq_name']} ({best_sharpe['hmm_sharpe']:.2f})")
        
        print(f"\n3️⃣ 核心发现:")
        if best_sharpe['freq_name'] in ["年度重训", "半年重训"]:
            print(f"   💡 低频重训表现更好 - 避免过拟合，捕获长期趋势")
        elif best_sharpe['freq_name'] == "季度重训":
            print(f"   💡 季度重训平衡了适应性与稳定性")
        else:
            print(f"   💡 高频重训在此期间表现最佳 - 可能因市场波动性较高")
        
        print(f"\n✅ SPY HMM V5.1 准确版本分析完成!")
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()