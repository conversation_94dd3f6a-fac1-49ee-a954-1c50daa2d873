import pandas as pd
import numpy as np
from lightweight_charts import Chart
from hmmlearn.hmm import GaussianHMM
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import accuracy_score
import os
from datetime import datetime, timezone, timedelta
import time
import threading
from typing import Optional, Dict, List, Callable, Tuple
import warnings
warnings.filterwarnings('ignore')

# 内嵌MT5数据获取功能
try:
    import MetaTrader5 as mt5
    MT5_AVAILABLE = True
except ImportError:
    MT5_AVAILABLE = False
    print("⚠️ MetaTrader5 模块未安装，请运行: pip install MetaTrader5")

class MT5DataFetcher:
    """MT5数据获取器 - 内嵌版本"""
    
    def __init__(self, symbol="XAUUSD"):
        self.symbol = symbol
        self.connected = False
        self._gmt_offset = None
    
    def connect(self) -> bool:
        """连接MT5"""
        if not MT5_AVAILABLE:
            print("❌ MT5模块不可用")
            return False
        
        try:
            if not mt5.initialize():
                print(f"❌ MT5初始化失败: {mt5.last_error()}")
                return False
            
            symbol_info = mt5.symbol_info(self.symbol)
            if symbol_info is None:
                print(f"❌ 品种 {self.symbol} 不可用")
                return False
            
            if not symbol_info.visible:
                if not mt5.symbol_select(self.symbol, True):
                    print(f"❌ 无法选择品种 {self.symbol}")
                    return False
            
            self.connected = True
            print(f"✅ MT5连接成功 ({self.symbol})")
            return True
            
        except Exception as e:
            print(f"❌ MT5连接异常: {e}")
            return False
    
    def disconnect(self):
        """断开MT5连接"""
        if MT5_AVAILABLE and self.connected:
            mt5.shutdown()
            self.connected = False
            print("✅ MT5连接已断开")
    
    def get_historical_data(self, start_date, end_date, timeframe="M1"):
        """获取历史数据 - 修复时区问题"""
        if not self.connected:
            print("❌ MT5未连接")
            return None
        
        try:
            tf_map = {"M1": mt5.TIMEFRAME_M1, "M5": mt5.TIMEFRAME_M5, "M15": mt5.TIMEFRAME_M15, "H1": mt5.TIMEFRAME_H1}
            mt5_timeframe = tf_map.get(timeframe, mt5.TIMEFRAME_M1)
            
            if self._gmt_offset is None:
                self.get_broker_gmt_offset()
            
            rates = mt5.copy_rates_range(self.symbol, mt5_timeframe, start_date, end_date)
            
            if rates is None or len(rates) == 0:
                print(f"❌ 未获取到历史数据: {mt5.last_error()}")
                return None
            
            df = pd.DataFrame(rates)
            df['time'] = pd.to_datetime(df['time'], unit='s')
            df['time'] = df['time'] - pd.Timedelta(hours=self._gmt_offset)
            df['time'] = df['time'].dt.tz_localize('UTC')
            df.set_index('time', inplace=True)
            
            df = df.rename(columns={'open': 'Open', 'high': 'High', 'low': 'Low', 'close': 'Close', 'tick_volume': 'Volume'})
            
            print(f"🕒 历史数据时区已修正 (GMT{self._gmt_offset:+d} → UTC)")
            return df
            
        except Exception as e:
            print(f"❌ 获取历史数据失败: {e}")
            return None
    
    def get_broker_gmt_offset(self):
        """获取经纪商GMT偏移量"""
        try:
            server_time_utc = pd.Timestamp(mt5.symbol_info_tick(self.symbol).time, unit='s', tz='UTC')
            # Fallback to server time if info is not available
            if pd.isna(server_time_utc):
                 server_time_utc = pd.Timestamp(mt5.copy_rates_from_pos(self.symbol, mt5.TIMEFRAME_M1, 0, 1)[0]['time'], unit='s', tz='UTC')

            local_time_utc = datetime.now(timezone.utc)
            self._gmt_offset = round((server_time_utc - local_time_utc).total_seconds() / 3600)
            print(f"🕒 检测到经纪商GMT偏移: {self._gmt_offset:+d}小时")

        except Exception as e:
            print(f"⚠️ 无法检测GMT偏移，使用默认值+2: {e}")
            self._gmt_offset = 2

    def get_latest_complete_candle(self, timeframe="M1"):
        """获取最新完整K线 - 修复时区问题"""
        if not self.connected: return None
        try:
            tf_map = {"M1": mt5.TIMEFRAME_M1, "M5": mt5.TIMEFRAME_M5, "M15": mt5.TIMEFRAME_M15, "H1": mt5.TIMEFRAME_H1}
            mt5_timeframe = tf_map.get(timeframe, mt5.TIMEFRAME_M1)
            
            rates = mt5.copy_rates_from_pos(self.symbol, mt5_timeframe, 0, 2)
            if rates is None or len(rates) < 2: return None
            
            candle = rates[-2]
            
            if self._gmt_offset is None: self.get_broker_gmt_offset()
            
            utc_timestamp = pd.Timestamp(candle['time'], unit='s') - pd.Timedelta(hours=self._gmt_offset)
            utc_timestamp = utc_timestamp.tz_localize('UTC')
            
            return {'datetime': utc_timestamp, 'open': float(candle['open']), 'high': float(candle['high']), 'low': float(candle['low']), 'close': float(candle['close']), 'volume': float(candle['tick_volume'])}
        except: return None

class HMMTradingSystemMT5:
    def __init__(self, symbol: str = "XAUUSD", n_states: int = 3, ma_fast: int = 20, ma_slow: int = 35, width: int = 1600, height: int = 900):
        self.symbol = symbol
        self.n_states = n_states
        self.ma_fast = ma_fast
        self.ma_slow = ma_slow
        self.fetcher = MT5DataFetcher(symbol)
        self.chart = Chart(width=width, height=height, toolbox=True)
        self._setup_chart_style()
        self.model: Optional[GaussianHMM] = None
        self.scaler: Optional[StandardScaler] = None
        self.state_map: Dict[int, str] = {}
        self.features = ['log_return', 'momentum_5m', 'momentum_20m', 'price_position', 'ma_diff']
        self.df: Optional[pd.DataFrame] = None
        self.state_colors = {'上涨': '#00C851', '下跌': '#ff4444', '盘整': '#ffbb33'}
        self.best_params = {}
        self.retrain_frequency_hours = 24
        self.on_signal_change: Optional[Callable] = None
        print(f"🚀 HMM状态识别系统初始化完成 (MT5-{symbol})")
        print(f"🎯 HMM状态数: {n_states}个状态")
        print(f"📊 技术指标: MA{ma_fast}/{ma_slow}周期")

    def _setup_chart_style(self):
        self.chart.layout(background_color='#ffffff', text_color='#333333', font_size=13, font_family='Segoe UI, Arial, sans-serif')
        self.chart.candle_style(up_color='#26a69a', down_color='#ef5350', border_up_color='#26a69a', border_down_color='#ef5350', wick_up_color='#26a69a', wick_down_color='#ef5350')
        self.chart.volume_config(up_color='rgba(38, 166, 154, 0.2)', down_color='rgba(239, 83, 80, 0.2)')
        self.chart.crosshair(mode='normal', vert_color='#e0e0e0', vert_style='dotted', horz_color='#e0e0e0', horz_style='dotted')
        self.chart.watermark(f'HMM状态识别系统 ({self.symbol})', color='rgba(200, 200, 200, 0.3)')
        self.chart.legend(visible=False, font_size=13)
        self.chart.grid(vert_enabled=True, horz_enabled=True, color='#f5f5f5')

    def _calculate_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """为给定的DataFrame计算技术指标"""
        df['log_return'] = np.log(df['Close'] / df['Close'].shift(1))
        df['momentum_5m'] = np.log(df['Close'] / df['Close'].shift(5))
        df['momentum_20m'] = np.log(df['Close'] / df['Close'].shift(20))
        df['ma_fast'] = df['Close'].rolling(window=self.ma_fast).mean()
        df['ma_slow'] = df['Close'].rolling(window=self.ma_slow).mean()
        df['price_position'] = (df['Close'] - df['ma_slow']) / df['ma_slow']
        df['ma_diff'] = (df['ma_fast'] - df['ma_slow']) / df['ma_slow']
        return df

    def _load_and_prepare_initial_data(self, days: int = 20) -> bool:
        print(f"📥 开始下载并准备过去 {days} 天的 {self.symbol} 数据...")
        try:
            if not self.fetcher.connect(): return False
            end_date = datetime.now(timezone.utc)
            start_date = end_date - timedelta(days=days)
            
            df = self.fetcher.get_historical_data(start_date, end_date)
            if df is None or df.empty:
                print("❌ MT5历史数据获取失败")
                return False
            
            self.df = self._calculate_features(df)
            self.df.dropna(inplace=True)
            
            print(f"✅ 初始数据加载和准备完成: {self.df.shape[0]} 条记录")
            return True
        except Exception as e:
            print(f"❌ 数据加载和准备失败: {e}")
            return False
        finally:
            self.fetcher.disconnect()

    def _backfill_data_with_live_feed(self) -> bool:
        print("\n🔄 使用实时接口回补数据断层...")
        if self.df is None or self.df.empty: return False
        try:
            if not self.fetcher.connect(): return False
            last_history_time = self.df.index[-1]
            now_utc = datetime.now(timezone.utc)
            gap_minutes = int((now_utc - last_history_time).total_seconds() / 60)

            if gap_minutes > 1:
                print(f"  ⏳ 检测到 {gap_minutes} 分钟的数据断层，正在回补...")
                rates = mt5.copy_rates_from_pos(self.symbol, mt5.TIMEFRAME_M1, 1, gap_minutes)
                if rates is None or len(rates) == 0:
                    print("🟡 无法通过实时接口获取断层数据，将使用现有数据。")
                    return True
                
                gap_df = pd.DataFrame(rates)
                gap_df['time'] = pd.to_datetime(gap_df['time'], unit='s')
                gap_df['time'] = gap_df['time'] - pd.Timedelta(hours=self.fetcher._gmt_offset)
                gap_df['time'] = gap_df['time'].dt.tz_localize('UTC')
                gap_df.set_index('time', inplace=True)
                gap_df = gap_df.rename(columns={'open': 'Open', 'high': 'High', 'low': 'Low', 'close': 'Close', 'tick_volume': 'Volume'})

                # 🔧 智能处理重叠数据：优先使用实时数据，处理部分重叠情况
                overlap_start = max(self.df.index[0], gap_df.index[0])
                overlap_end = min(self.df.index[-1], gap_df.index[-1])
                
                if overlap_start <= overlap_end:
                    overlap_minutes = int((overlap_end - overlap_start).total_seconds() / 60) + 1
                    print(f"  🔄 检测到 {overlap_minutes} 分钟重叠数据，优先使用实时数据...")
                    
                    # 保留历史数据中非重叠部分
                    non_overlap_historical = self.df[self.df.index < overlap_start]
                    # 使用实时数据的重叠部分和新增部分
                    realtime_data = gap_df[gap_df.index >= overlap_start]
                    
                    if len(non_overlap_historical) > 0:
                        self.df = pd.concat([non_overlap_historical, realtime_data])
                    else:
                        self.df = realtime_data
                else:
                    # 无重叠情况，直接拼接
                    self.df = pd.concat([self.df, gap_df])
                
                # 最终去重和排序（防止边界情况）
                self.df = self.df[~self.df.index.duplicated(keep='last')].sort_index()
                
                print("  🔧 正在为完整数据集重新计算所有技术指标...")
                self.df = self._calculate_features(self.df)
                self.df.dropna(inplace=True)
                print(f"  ✅ 数据回补和特征重计算完成。最终记录: {len(self.df)}")
            else:
                print("✅ 数据断层小于1分钟，无需回补。")
            return True
        except Exception as e:
            print(f"❌ 回补数据时发生严重错误: {e}")
            return False
        finally:
            self.fetcher.disconnect()

    def walk_forward_analysis(self, min_train_ratio: float = 0.6, max_train_ratio: float = 0.8, step: float = 0.05) -> dict:
        print(f"\n🔍 开始前向展开分析...")
        if self.df is None: return {}
        # ... (rest of the function is correct)
        results = []
        train_ratios = np.arange(min_train_ratio, max_train_ratio + step, step)
        
        for train_ratio in train_ratios:
            print(f"  测试训练比例: {train_ratio:.2f}")
            
            try:
                split_idx = int(len(self.df) * train_ratio)
                train_data = self.df.iloc[:split_idx]
                test_data = self.df.iloc[split_idx:]
                
                if len(test_data) < 100: continue
                
                X_train = train_data[self.features].values
                scaler = StandardScaler().fit(X_train)
                X_train_scaled = scaler.transform(X_train)
                
                model = GaussianHMM(n_components=self.n_states, covariance_type="diag", n_iter=300, random_state=42, tol=1e-4).fit(X_train_scaled)
                
                state_means = pd.DataFrame(model.means_, columns=self.features)
                sorted_by_return = state_means.sort_values('log_return')
                state_map = {sorted_by_return.index[0]: "下跌", sorted_by_return.index[-1]: "上涨"}
                if self.n_states == 3: state_map[sorted_by_return.index[1]] = "盘整"
                
                # 🔧 修复：使用与实时预测完全一致的逻辑
                X_test_scaled = scaler.transform(test_data[self.features].values)
                test_states = model.predict(X_test_scaled)
                
                # 创建临时DataFrame以使用统一的predict_states逻辑
                temp_test_df = test_data.copy()
                temp_test_df['state'] = test_states
                temp_test_df['regime'] = temp_test_df['state'].map(state_map)
                # 使用shift(1)确保避免未来信息泄露
                temp_test_df['signal'] = temp_test_df['regime'].map({"上涨": 1, "下跌": -1, "盘整": 0}).shift(1).fillna(0)
                
                # 使用修正后的信号进行回测
                signals = temp_test_df['signal'].values
                test_returns = test_data['log_return'].values
                strategy_returns = test_returns * signals
                
                sharpe_ratio = np.mean(strategy_returns) / np.std(strategy_returns) * np.sqrt(252*24*60) if np.std(strategy_returns) > 0 else 0
                max_drawdown = self._calculate_max_drawdown(strategy_returns)
                total_return = np.exp(np.sum(strategy_returns)) - 1
                
                results.append({'train_ratio': train_ratio, 'sharpe_ratio': sharpe_ratio, 'max_drawdown': max_drawdown, 'total_return': total_return, 'test_days': len(test_data) / (24 * 60)})
                print(f"    夏普比率: {sharpe_ratio:.2f}, 最大回撤: {max_drawdown:.2%}, 收益: {total_return:.2%}")
            except Exception as e:
                print(f"    ❌ 训练比例 {train_ratio:.2f} 失败: {e}")
        
        if not results: return {}
        
        results_df = pd.DataFrame(results)
        results_df['score'] = results_df['sharpe_ratio'] * 0.7 - results_df['max_drawdown'] * 0.3
        best_result = results_df.loc[results_df['score'].idxmax()]
        self.best_params = best_result.to_dict()
        
        print(f"\n🎯 最优训练窗口分析结果:")
        print(f"  最佳训练比例: {self.best_params['train_ratio']:.2f}")
        # ... (rest of printing is fine)
        test_period_hours = self.best_params['test_days'] * 24
        self.retrain_frequency_hours = max(12, min(72, test_period_hours / 3))
        print(f"💡 建议重训练频率: 每 {self.retrain_frequency_hours:.0f} 小时")
        return self.best_params

    def _calculate_max_drawdown(self, returns: np.array) -> float:
        cumulative = np.exp(np.cumsum(returns))
        running_max = np.maximum.accumulate(cumulative)
        drawdown = (cumulative - running_max) / running_max
        return abs(drawdown.min())

    def train_optimal_model(self) -> bool:
        if self.df is None: return False
        try:
            train_ratio = self.best_params.get('train_ratio', 0.75)
            split_idx = int(len(self.df) * train_ratio)
            train_data = self.df.iloc[:split_idx]
            
            self.scaler = StandardScaler().fit(train_data[self.features].values)
            X_train_scaled = self.scaler.transform(train_data[self.features].values)
            
            self.model = GaussianHMM(n_components=self.n_states, covariance_type="diag", n_iter=500, random_state=42, tol=1e-4).fit(X_train_scaled)
            self._identify_state_mapping()
            print(f"✅ 最优模型训练完成 (BIC: {self.model.bic(X_train_scaled):.2f})")
            return True
        except Exception as e:
            print(f"❌ 模型训练失败: {e}")
            return False

    def _identify_state_mapping(self):
        state_means = pd.DataFrame(self.model.means_, columns=self.features)
        sorted_by_return = state_means.sort_values('log_return')
        self.state_map = {sorted_by_return.index[0]: "下跌", sorted_by_return.index[-1]: "上涨"}
        if self.n_states == 3: self.state_map[sorted_by_return.index[1]] = "盘整"
        print("🎯 状态映射:")
        for state, regime in self.state_map.items():
            print(f"  状态{state} -> {regime} (收益:{state_means.loc[state, 'log_return']:.4f}, 均线:{state_means.loc[state, 'ma_diff']:.4f})")

    def predict_states(self, df_to_predict: pd.DataFrame) -> pd.DataFrame:
        if self.model is None or self.scaler is None: raise ValueError("模型未训练")
        
        X = df_to_predict[self.features].values
        X_scaled = self.scaler.transform(X)
        states = self.model.predict(X_scaled)
        
        result_df = df_to_predict.copy()
        result_df['state'] = states
        result_df['regime'] = result_df['state'].map(self.state_map)
        
        # 🔧 统一逻辑：所有预测都使用shift(1)避免未来信息泄露，确保历史分析与实时预测完全一致
        result_df['signal'] = result_df['regime'].map({"上涨": 1, "下跌": -1, "盘整": 0}).shift(1).fillna(0)
        
        return result_df

    def display_recent_analysis(self, hours_back: int = 48):
        if self.df is None: return
        
        recent_data = self.df.tail(hours_back * 60)
        pred_df = self.predict_states(recent_data)  # 历史数据使用shift(1)逻辑
        
        chart_data = pred_df[['Open', 'High', 'Low', 'Close', 'Volume']].copy()
        chart_data.columns = ['open', 'high', 'low', 'close', 'volume']
        chart_data.index = (chart_data.index + pd.Timedelta(hours=8)).tz_localize(None)
        
        self.chart.set(chart_data)
        self._add_regime_markers(pred_df)
        
        # 🔧 关键修复：正确初始化_last_signal - 使用计算出的signal而非regime
        if not pred_df.empty:
            # 使用pred_df中计算出的signal，而不是regime的直接映射
            latest_signal = int(pred_df['signal'].iloc[-1])
            latest_regime = pred_df['regime'].iloc[-1]
            self._last_signal = latest_signal
            print(f"🎯 初始化实时监控基准信号: {latest_regime} (signal={latest_signal})")
        
        print(f"📊 显示最近{hours_back}小时分析，包含 {len(chart_data)} 根K线")

    def _add_regime_markers(self, df: pd.DataFrame):
        signal_changes = df['signal'] != df['signal'].shift(1)
        for idx, row in df[signal_changes].iterrows():
            try:
                marker_time = (idx + pd.Timedelta(hours=8)).tz_localize(None)
                self.chart.marker(time=marker_time, position='below' if row['signal'] != -1 else 'above', shape='arrow_up' if row['signal'] == 1 else 'arrow_down' if row['signal'] == -1 else 'circle', color=self.state_colors[row['regime']], text=f'{row["regime"]}')
            except Exception as e:
                print(f"⚠️ 状态标记添加失败: {e}")

    def start_realtime_monitoring(self, max_updates: int = 1440):
        print(f"\n🔄 启动实时监控...")
        
        def monitoring_loop():
            if self.df is None: return
            last_candle_time = self.df.index[-1]
            
            print("⏰ 实时监控已启动。按 Ctrl+C 停止。")
            if not self.fetcher.connect(): return

            update_count = 0
            while update_count < max_updates:
                try:
                    latest_candle = self.fetcher.get_latest_complete_candle()
                    if latest_candle and latest_candle['datetime'] > last_candle_time:
                        last_candle_time = latest_candle['datetime']
                        self._update_with_new_candle(latest_candle)
                        update_count += 1
                    time.sleep(10)
                except KeyboardInterrupt:
                    print("\n🛑 用户停止监控")
                    break
                except Exception as e:
                    print(f"❌ 监控循环发生错误: {e}")
                    time.sleep(30)
            
            self.fetcher.disconnect()
            print(f"✅ 实时监控结束，共处理 {update_count} 根新K线")

        threading.Thread(target=monitoring_loop, daemon=True).start()

    def _update_with_new_candle(self, candle_data: dict):
        try:
            new_row_df = pd.DataFrame([candle_data]).rename(columns={'datetime': 'time', 'open': 'Open', 'high': 'High', 'low': 'Low', 'close': 'Close', 'volume': 'Volume'}).set_index('time')
            
            # Append new candle and recalculate features
            self.df = pd.concat([self.df, new_row_df])
            self.df = self.df[~self.df.index.duplicated(keep='last')]
            
            # 🔧 关键修复：确保特征计算一致性 - 基于完整历史数据重新计算所有特征
            self.df = self._calculate_features(self.df)
            
            # 🔧 核心逻辑修复：确保实时预测与历史分析完全一致
            # 新K线到来后，重新预测最后两根K线，使用统一的shift(1)逻辑
            if len(self.df) >= 2:
                last_two_rows = self.df.tail(2)
                pred_df = self.predict_states(last_two_rows)  # 使用统一的shift(1)逻辑
                
                # 获取基于shift(1)逻辑计算的当前信号
                # 当前信号：基于倒数第二根K线的状态，用于最新K线时刻的交易决策（避免未来信息泄露）
                current_signal = int(pred_df['signal'].iloc[-1])  
                current_regime = pred_df['regime'].iloc[-1]  # 最新K线的状态（仅用于显示）
                signal_time = last_two_rows.index[-1]  # 最新K线的时间
            else:
                # 如果数据不足，跳过
                print("⚠️ 数据不足，跳过状态预测")
                return

            # Update chart - 使用最新K线数据
            chart_time = (signal_time + pd.Timedelta(hours=8)).tz_localize(None)
            last_candle = self.df.iloc[-1]
            update_series = pd.Series({'time': chart_time, 'open': last_candle['Open'], 'high': last_candle['High'], 'low': last_candle['Low'], 'close': last_candle['Close'], 'volume': last_candle['Volume']})
            self.chart.update(update_series)
            print(f"📊 图表已更新: ${update_series['close']:.2f} [{chart_time.strftime('%H:%M:%S')}] - 当前状态: {current_regime}")

            # 🔧 信号变化检测：与历史分析逻辑完全一致
            if hasattr(self, '_last_signal'):
                last_signal = self._last_signal
            else:
                last_signal = None
                self._last_signal = None
            
            if current_signal != last_signal:
                print(f"🔔 {chart_time.strftime('%H:%M')}: {current_regime} 信号变化 (从 {last_signal} 到 {current_signal}) | ${update_series['close']:.2f}")
                self.chart.marker(time=chart_time, position='below' if current_signal != -1 else 'above', shape='arrow_up' if current_signal == 1 else 'arrow_down' if current_signal == -1 else 'circle', color=self.state_colors[current_regime], text=f'{current_regime}')
                if self.on_signal_change: self.on_signal_change(current_signal, current_regime, last_candle.to_dict())
            else:
                print(f"  ↳ 信号保持不变: {current_regime} ({current_signal})")
            
            self._last_signal = current_signal
        except Exception as e:
            print(f"❌ 更新图表失败: {e}")
            import traceback
            traceback.print_exc()

    def show_dashboard(self, block: bool = True):
        print(f"\n📊 启动HMM状态识别仪表板 ({self.symbol})")
        self.chart.show(block=block)

    def set_signal_callback(self, callback: Callable):
        self.on_signal_change = callback

def main():
    print("=" * 80)
    print(" HMM黄金状态识别系统 (MT5版本)")
    print(" 基于MT5 XAUUSD数据的实时状态监控")
    print("=" * 80)
    
    system = HMMTradingSystemMT5(symbol="XAUUSD", n_states=3, ma_fast=20, ma_slow=35)
    
    def on_signal_change(signal, regime, data_dict):
        action = "买入" if signal == 1 else "卖出" if signal == -1 else "观望"
        print(f"🎯 交易提示: {action} | {regime} | ${data_dict['Close']:.2f}")
    system.set_signal_callback(on_signal_change)
    
    try:
        if not system._load_and_prepare_initial_data(days=20):
            return
        if not system._backfill_data_with_live_feed():
            print("⚠️ 未能成功回补数据，初始预测可能不准。")
        if not system.walk_forward_analysis():
             print("❌ 参数优化失败，使用默认参数")
        if not system.train_optimal_model():
            return
        
        system.display_recent_analysis(hours_back=48)
        system.start_realtime_monitoring()
        system.show_dashboard(block=True)
        
    except KeyboardInterrupt:
        print("\n\n用户中断程序")
    except Exception as e:
        print(f"\n\n程序执行异常: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n✅ HMM黄金状态识别系统结束")

if __name__ == "__main__":
    main()
