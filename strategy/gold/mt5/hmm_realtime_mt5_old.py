import pandas as pd
import numpy as np
from lightweight_charts import Chart
from hmmlearn.hmm import GaussianHMM
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import accuracy_score
import os
from datetime import datetime, timezone, timedelta
import time
import threading
from typing import Optional, Dict, List, Callable, Tuple
import warnings
warnings.filterwarnings('ignore')

# 内嵌MT5数据获取功能
try:
    import MetaTrader5 as mt5
    MT5_AVAILABLE = True
except ImportError:
    MT5_AVAILABLE = False
    print("⚠️ MetaTrader5 模块未安装，请运行: pip install MetaTrader5")

class MT5DataFetcher:
    """MT5数据获取器 - 内嵌版本"""
    
    def __init__(self, symbol="XAUUSD"):
        self.symbol = symbol
        self.connected = False
    
    def connect(self) -> bool:
        """连接MT5"""
        if not MT5_AVAILABLE:
            print("❌ MT5模块不可用")
            return False
        
        try:
            if not mt5.initialize():
                print(f"❌ MT5初始化失败: {mt5.last_error()}")
                return False
            
            # 检查品种可用性
            symbol_info = mt5.symbol_info(self.symbol)
            if symbol_info is None:
                print(f"❌ 品种 {self.symbol} 不可用")
                return False
            
            if not symbol_info.visible:
                if not mt5.symbol_select(self.symbol, True):
                    print(f"❌ 无法选择品种 {self.symbol}")
                    return False
            
            self.connected = True
            print(f"✅ MT5连接成功 ({self.symbol})")
            return True
            
        except Exception as e:
            print(f"❌ MT5连接异常: {e}")
            return False
    
    def disconnect(self):
        """断开MT5连接"""
        if MT5_AVAILABLE and self.connected:
            mt5.shutdown()
            self.connected = False
            print("✅ MT5连接已断开")
    
    def get_historical_data(self, start_date, end_date, timeframe="M1"):
        """获取历史数据 - 修复时区问题"""
        if not self.connected:
            print("❌ MT5未连接")
            return None
        
        try:
            # 转换时间框架
            tf_map = {
                "M1": mt5.TIMEFRAME_M1,
                "M5": mt5.TIMEFRAME_M5,
                "M15": mt5.TIMEFRAME_M15,
                "H1": mt5.TIMEFRAME_H1
            }
            
            mt5_timeframe = tf_map.get(timeframe, mt5.TIMEFRAME_M1)
            
            # 获取经纪商GMT偏移量
            if not hasattr(self, '_gmt_offset'):
                self._gmt_offset = self.get_broker_gmt_offset()
            
            # 获取数据
            rates = mt5.copy_rates_range(
                self.symbol, 
                mt5_timeframe, 
                start_date, 
                end_date
            )
            
            if rates is None or len(rates) == 0:
                print(f"❌ 未获取到历史数据: {mt5.last_error()}")
                return None
            
            # 转换为DataFrame
            df = pd.DataFrame(rates)
            
            # 关键修复：正确处理MT5服务器时间转UTC
            df['time'] = pd.to_datetime(df['time'], unit='s')
            # 减去GMT偏移量得到UTC时间
            df['time'] = df['time'] - pd.Timedelta(hours=self._gmt_offset)
            # 设置为UTC时区
            df['time'] = df['time'].dt.tz_localize('UTC')
            df.set_index('time', inplace=True)
            
            # 重命名列
            df = df.rename(columns={
                'open': 'Open',
                'high': 'High',
                'low': 'Low',
                'close': 'Close',
                'tick_volume': 'Volume'
            })
            
            print(f"🕒 历史数据时区已修正 (GMT{self._gmt_offset:+d} → UTC)")
            
            return df
            
        except Exception as e:
            print(f"❌ 获取历史数据失败: {e}")
            return None
    
    def get_broker_gmt_offset(self):
        """获取经纪商GMT偏移量"""
        try:
            # 获取服务器时间信息
            account_info = mt5.account_info()
            if account_info is None:
                return 0  # 默认偏移量
            
            # 尝试获取当前服务器时间
            current_rates = mt5.copy_rates_from_pos(self.symbol, mt5.TIMEFRAME_M1, 0, 1)
            if current_rates is None or len(current_rates) == 0:
                return 0
            
            # 服务器时间
            server_time = pd.Timestamp(current_rates[0]['time'], unit='s')
            # 当前UTC时间
            utc_time = pd.Timestamp.now(tz='UTC').replace(tzinfo=None)
            
            # 计算偏移量（小时）
            time_diff = (server_time - utc_time).total_seconds() / 3600
            # 四舍五入到最近的小时
            gmt_offset = round(time_diff)
            
            print(f"🕒 检测到经纪商GMT偏移: {gmt_offset:+d}小时")
            print(f"   服务器时间: {server_time.strftime('%H:%M:%S')}")
            print(f"   UTC时间: {utc_time.strftime('%H:%M:%S')}")
            
            return gmt_offset
            
        except Exception as e:
            print(f"⚠️ 无法检测GMT偏移，使用默认值: {e}")
            return 2  # 默认使用GMT+2（常见的欧洲经纪商时间）
    
    def get_latest_complete_candle(self, timeframe="M1"):
        """获取最新完整K线 - 修复时区问题"""
        if not self.connected:
            return None
        
        try:
            # 转换时间框架
            tf_map = {
                "M1": mt5.TIMEFRAME_M1,
                "M5": mt5.TIMEFRAME_M5,
                "M15": mt5.TIMEFRAME_M15,
                "H1": mt5.TIMEFRAME_H1
            }
            
            mt5_timeframe = tf_map.get(timeframe, mt5.TIMEFRAME_M1)
            
            # 获取最近2根K线，确保获取到完整的上一根
            rates = mt5.copy_rates_from_pos(self.symbol, mt5_timeframe, 0, 2)
            
            if rates is None or len(rates) == 0:
                return None
            
            # 取倒数第二根K线（完整的）
            if len(rates) >= 2:
                candle = rates[-2]  # 倒数第二根，确保完整
            else:
                candle = rates[-1]  # 如果只有一根，也取这根
            
            # 关键修复：正确处理MT5服务器时间
            # 获取经纪商GMT偏移量
            if not hasattr(self, '_gmt_offset'):
                self._gmt_offset = self.get_broker_gmt_offset()
            
            # MT5服务器时间转换为UTC时间
            server_timestamp = pd.Timestamp(candle['time'], unit='s')
            # 减去GMT偏移量得到UTC时间
            utc_timestamp = server_timestamp - pd.Timedelta(hours=self._gmt_offset)
            # 设置为UTC时区
            utc_timestamp = utc_timestamp.tz_localize('UTC')
            
            # 转换格式，与原monitor保持一致
            result = {
                'datetime': utc_timestamp,
                'open': float(candle['open']),
                'high': float(candle['high']),
                'low': float(candle['low']),
                'close': float(candle['close']),
                'volume': float(candle['tick_volume'])
            }
            
            return result
            
        except Exception as e:
            # 静默处理错误，避免刷屏
            return None

class HMMTradingSystemMT5:
    """HMM黄金状态识别系统 - 基于MT5 XAUUSD数据的实时状态监控"""
    
    def __init__(self, 
                 symbol: str = "XAUUSD",
                 n_states: int = 3,
                 ma_fast: int = 20,
                 ma_slow: int = 35,
                 width: int = 1600,
                 height: int = 900):
        """
        初始化HMM交易系统
        
        Args:
            symbol: 交易品种
            n_states: HMM状态数量
            ma_fast: 快速移动平均线周期
            ma_slow: 慢速移动平均线周期
            width: 图表宽度
            height: 图表高度
        """
        self.symbol = symbol
        self.n_states = n_states
        self.ma_fast = ma_fast
        self.ma_slow = ma_slow
        
        # 初始化MT5数据获取器
        self.fetcher = MT5DataFetcher(symbol)
        
        # 初始化图表
        self.chart = Chart(
            width=width,
            height=height,
            toolbox=True
        )
        
        # 配置图表样式
        self._setup_chart_style()
        
        # HMM相关
        self.model: Optional[GaussianHMM] = None
        self.scaler: Optional[StandardScaler] = None
        self.state_map: Dict[int, str] = {}
        self.features = ['log_return', 'momentum_5m', 'momentum_20m', 'price_position', 'ma_diff']
        
        # 数据存储
        self.df: Optional[pd.DataFrame] = None
        self.last_processed_index = 0
        self.current_data_buffer = pd.DataFrame()  # 实时数据缓冲
        
        # 状态颜色映射
        self.state_colors = {
            '上涨': '#00C851',    # 明亮绿色
            '下跌': '#ff4444',    # 明亮红色  
            '盘整': '#ffbb33'     # 橙黄色
        }
        
        # 性能指标
        self.best_params = {}
        self.retrain_frequency_hours = 24  # 默认24小时重训练一次
        
        # 回调函数
        self.on_signal_change: Optional[Callable] = None
        
        print(f"🚀 HMM状态识别系统初始化完成 (MT5-{symbol})")
        print(f"🎯 HMM状态数: {n_states}个状态")
        print(f"📊 技术指标: MA{ma_fast}/{ma_slow}周期")
        
    def _setup_chart_style(self):
        """配置图表样式 - 简洁专业主题，专注状态识别"""
        # 基础布局
        self.chart.layout(
            background_color='#ffffff',
            text_color='#333333',
            font_size=13,
            font_family='Segoe UI, Arial, sans-serif'
        )
        
        # K线样式
        self.chart.candle_style(
            up_color='#26a69a',
            down_color='#ef5350',
            border_up_color='#26a69a',
            border_down_color='#ef5350',
            wick_up_color='#26a69a',
            wick_down_color='#ef5350'
        )
        
        # 成交量样式
        self.chart.volume_config(
            up_color='rgba(38, 166, 154, 0.2)',
            down_color='rgba(239, 83, 80, 0.2)'
        )
        
        # 十字线
        self.chart.crosshair(
            mode='normal',
            vert_color='#e0e0e0',
            vert_style='dotted',
            horz_color='#e0e0e0',
            horz_style='dotted'
        )
        
        # 水印
        self.chart.watermark(f'HMM状态识别系统 ({self.symbol})', color='rgba(200, 200, 200, 0.3)')
        
        # 图例
        self.chart.legend(visible=False, font_size=13)
        
        # 网格线
        self.chart.grid(vert_enabled=True, horz_enabled=True, color='#f5f5f5')
    
    def download_gold_data(self, days: int = 20) -> bool:
        """下载黄金数据 (XAUUSD) - 从MT5获取历史数据"""
        print(f"📥 开始下载过去{days}天的{self.symbol}数据...")
        
        try:
            # 连接MT5
            if not self.fetcher.connect():
                print("❌ MT5连接失败")
                return False
            
            # 计算时间范围
            end_date = datetime.now(timezone.utc)
            start_date = end_date - timedelta(days=days)
            
            print(f"📊 数据源: MT5 {self.symbol}")
            print(f"📅 时间范围: {start_date.strftime('%Y-%m-%d')} ~ {end_date.strftime('%Y-%m-%d')}")
            
            # 获取历史数据
            data = self._download_mt5_historical_data(start_date, end_date)
            
            if data is None or data.empty:
                print("❌ MT5历史数据获取失败")
                self.fetcher.disconnect()
                return False
            
            # 保存数据
            os.makedirs("data", exist_ok=True)
            timestamp = datetime.now().strftime("%Y%m%d")
            filename = f"data/{self.symbol}_1m_last_{days}days_{timestamp}.parquet"
            data.to_parquet(filename)
            
            self.data_file = filename
            print(f"✅ 数据下载完成: {len(data)} 条记录")
            print(f"📅 时间范围: {data.index[0].strftime('%m-%d %H:%M')} ~ {data.index[-1].strftime('%m-%d %H:%M')}")
            print(f"💾 已保存至: {filename}")
            
            return True
            
        except Exception as e:
            print(f"❌ 数据下载失败: {e}")
            return False
        finally:
            self.fetcher.disconnect()
    
    def _download_mt5_historical_data(self, start_date, end_date):
        """从MT5下载历史数据"""
        try:
            # 使用MT5获取历史数据
            data = self.fetcher.get_historical_data(
                start_date=start_date,
                end_date=end_date,
                timeframe="M1"
            )
            
            if data is None or data.empty:
                return None
            
            # 确保数据格式正确
            data = data.dropna().sort_index()
            
            # 重命名列以匹配原系统期望的格式
            if 'open' in data.columns:
                data = data.rename(columns={
                    'open': 'Open',
                    'high': 'High', 
                    'low': 'Low',
                    'close': 'Close',
                    'volume': 'Volume'
                })
            
            # 确保Volume列存在
            if 'Volume' not in data.columns:
                data['Volume'] = 1000  # 默认成交量
            
            print(f"  📊 获取历史数据: {len(data)} 条记录")
            
            return data
            
        except Exception as e:
            print(f"  ❌ MT5历史数据获取失败: {e}")
            return None
    
    def load_and_prepare_data(self, data_file: str = None) -> bool:
        """加载并准备数据"""
        try:
            if data_file:
                self.data_file = data_file
            
            if not hasattr(self, 'data_file') or not os.path.exists(self.data_file):
                raise FileNotFoundError(f"数据文件未找到: {getattr(self, 'data_file', 'None')}")
            
            # 加载数据
            df = pd.read_parquet(self.data_file)
            if isinstance(df.columns, pd.MultiIndex):
                df.columns = df.columns.get_level_values(0)
            
            # 特征工程
            df['log_return'] = np.log(df['Close'] / df['Close'].shift(1))
            df['momentum_5m'] = np.log(df['Close'] / df['Close'].shift(5))
            df['momentum_20m'] = np.log(df['Close'] / df['Close'].shift(20))
            
            # 移动平均线
            df['ma_fast'] = df['Close'].rolling(window=self.ma_fast).mean()
            df['ma_slow'] = df['Close'].rolling(window=self.ma_slow).mean()
            df['price_position'] = (df['Close'] - df['ma_slow']) / df['ma_slow']
            df['ma_diff'] = (df['ma_fast'] - df['ma_slow']) / df['ma_slow']
            
            # 清理数据
            df.dropna(inplace=True)
            
            # 时间格式转换
            if df.index.tz is None:
                df.index = df.index.tz_localize('UTC')
            
            self.df = df
            print(f"✅ 数据加载成功: {df.shape[0]} 条记录")
            
            return True
            
        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            return False
    
    def walk_forward_analysis(self, min_train_ratio: float = 0.6, max_train_ratio: float = 0.8, step: float = 0.05) -> dict:
        """前向展开分析优化训练窗口"""
        print(f"\n🔍 开始前向展开分析...")
        
        if self.df is None:
            print("❌ 请先加载数据")
            return {}
        
        results = []
        train_ratios = np.arange(min_train_ratio, max_train_ratio + step, step)
        
        for train_ratio in train_ratios:
            print(f"  测试训练比例: {train_ratio:.2f}")
            
            try:
                # 分割数据
                split_idx = int(len(self.df) * train_ratio)
                train_data = self.df.iloc[:split_idx]
                test_data = self.df.iloc[split_idx:]
                
                if len(test_data) < 100:  # 确保测试集足够大
                    continue
                
                # 训练模型
                X_train = train_data[self.features].values
                scaler = StandardScaler()
                X_train_scaled = scaler.fit_transform(X_train)
                
                model = GaussianHMM(
                    n_components=self.n_states,
                    covariance_type="diag",
                    n_iter=300,
                    random_state=42,
                    tol=1e-4
                )
                model.fit(X_train_scaled)
                
                # 状态映射
                state_means = pd.DataFrame(model.means_, columns=self.features)
                sorted_by_return = state_means.sort_values('log_return')
                
                state_map = {
                    sorted_by_return.index[0]: "下跌",
                    sorted_by_return.index[-1]: "上涨"
                }
                if self.n_states == 3:
                    state_map[sorted_by_return.index[1]] = "盘整"
                
                # 测试集预测
                X_test = test_data[self.features].values
                X_test_scaled = scaler.transform(X_test)
                
                test_states = model.predict(X_test_scaled)
                test_regimes = [state_map[s] for s in test_states]
                
                # 计算收益
                signal_map = {"上涨": 1, "下跌": -1, "盘整": 0}
                signals = [signal_map[r] for r in test_regimes]
                
                test_returns = test_data['log_return'].values[1:]  # 滞后一期
                strategy_returns = test_returns * np.array(signals[:-1])
                
                # 性能指标
                sharpe_ratio = np.mean(strategy_returns) / np.std(strategy_returns) * np.sqrt(252*24*60) if np.std(strategy_returns) > 0 else 0
                max_drawdown = self._calculate_max_drawdown(strategy_returns)
                total_return = np.exp(np.sum(strategy_returns)) - 1
                
                results.append({
                    'train_ratio': train_ratio,
                    'sharpe_ratio': sharpe_ratio,
                    'max_drawdown': max_drawdown,
                    'total_return': total_return,
                    'test_days': len(test_data) / (24 * 60)  # 测试天数
                })
                
                print(f"    夏普比率: {sharpe_ratio:.2f}, 最大回撤: {max_drawdown:.2%}, 收益: {total_return:.2%}")
                
            except Exception as e:
                print(f"    ❌ 训练比例 {train_ratio:.2f} 失败: {e}")
                continue
        
        if not results:
            print("❌ 前向展开分析失败")
            return {}
        
        # 选择最优参数
        results_df = pd.DataFrame(results)
        
        # 综合评分：夏普比率权重70%，回撤权重30%
        results_df['score'] = results_df['sharpe_ratio'] * 0.7 - results_df['max_drawdown'] * 0.3
        best_result = results_df.loc[results_df['score'].idxmax()]
        
        self.best_params = best_result.to_dict()
        
        print(f"\n🎯 最优训练窗口分析结果:")
        print(f"  最佳训练比例: {best_result['train_ratio']:.2f}")
        print(f"  样本外夏普比率: {best_result['sharpe_ratio']:.2f}")
        print(f"  样本外最大回撤: {best_result['max_drawdown']:.2%}")
        print(f"  样本外总收益: {best_result['total_return']:.2%}")
        print(f"  测试天数: {best_result['test_days']:.1f}天")
        
        # 计算建议的重训练频率
        test_period_hours = best_result['test_days'] * 24
        self.retrain_frequency_hours = max(12, min(72, test_period_hours / 3))  # 12-72小时之间
        
        print(f"💡 建议重训练频率: 每 {self.retrain_frequency_hours:.0f} 小时")
        
        return self.best_params
    
    def _calculate_max_drawdown(self, returns: np.array) -> float:
        """计算最大回撤"""
        cumulative = np.exp(np.cumsum(returns))
        running_max = np.maximum.accumulate(cumulative)
        drawdown = (cumulative - running_max) / running_max
        return abs(drawdown.min())

    def _fill_data_gap(self):
        """检查并填补历史数据与当前时间之间的数据断层"""
        print("\n🔄 开始检查并填补数据断层...")
        if self.df is None or self.df.empty:
            print("⚠️ 数据集为空，无法填补断层。")
            return

        last_data_time = self.df.index[-1]
        current_time = datetime.now(timezone.utc)
        
        time_diff_minutes = (current_time - last_data_time).total_seconds() / 60

        print(f"  🕒 最后历史数据时间 (UTC): {last_data_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"  🕒 当前时间 (UTC):          {current_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"  ⏳ 时间差异: {time_diff_minutes:.2f} 分钟")

        # 如果数据差异大于5分钟，则尝试填补
        if time_diff_minutes > 5:
            print(f"  Gap detected. Attempting to fill {time_diff_minutes:.0f} minutes of missing data.")
            
            try:
                if not self.fetcher.connect():
                    print("❌ MT5连接失败，无法填补数据。")
                    return

                # 从历史数据最后时间的下一分钟开始获取
                start_fill_time = last_data_time + timedelta(minutes=1)
                
                gap_data = self.fetcher.get_historical_data(
                    start_date=start_fill_time,
                    end_date=current_time,
                    timeframe="M1"
                )

                if gap_data is not None and not gap_data.empty:
                    # 特征工程
                    gap_data['log_return'] = np.log(gap_data['Close'] / gap_data['Close'].shift(1))
                    gap_data['momentum_5m'] = np.log(gap_data['Close'] / gap_data['Close'].shift(5))
                    gap_data['momentum_20m'] = np.log(gap_data['Close'] / gap_data['Close'].shift(20))
                    gap_data['ma_fast'] = gap_data['Close'].rolling(window=self.ma_fast).mean()
                    gap_data['ma_slow'] = gap_data['Close'].rolling(window=self.ma_slow).mean()
                    gap_data['price_position'] = (gap_data['Close'] - gap_data['ma_slow']) / gap_data['ma_slow']
                    gap_data['ma_diff'] = (gap_data['ma_fast'] - gap_data['ma_slow']) / gap_data['ma_slow']
                    
                    # 拼接数据
                    self.df = pd.concat([self.df, gap_data])
                    self.df.dropna(inplace=True)
                    self.df = self.df[~self.df.index.duplicated(keep='first')] # 去重
                    
                    print(f"✅ 成功填补并合并了 {len(gap_data)} 条新数据。")
                    print(f"  📈 新的数据范围 (UTC): {self.df.index[0].strftime('%H:%M')} - {self.df.index[-1].strftime('%H:%M')}")
                else:
                    print("🟡 未能获取到用于填补的断层数据。")

            except Exception as e:
                print(f"❌ 填补数据过程中发生错误: {e}")
            finally:
                self.fetcher.disconnect()
        else:
            print("✅ 数据是连续的，无需填补。")
    
    def train_optimal_model(self) -> bool:
        """使用最优参数训练模型"""
        if self.df is None:
            print("❌ 请先加载数据")
            return False
        
        try:
            # 使用最优训练比例
            train_ratio = self.best_params.get('train_ratio', 0.75)
            split_idx = int(len(self.df) * train_ratio)
            train_data = self.df.iloc[:split_idx]
            
            # 准备训练特征
            X_train = train_data[self.features].values
            
            # 标准化
            self.scaler = StandardScaler()
            X_train_scaled = self.scaler.fit_transform(X_train)
            
            # 训练HMM
            self.model = GaussianHMM(
                n_components=self.n_states,
                covariance_type="diag",
                n_iter=500,
                random_state=42,
                tol=1e-4
            )
            self.model.fit(X_train_scaled)
            
            # 识别状态映射
            self._identify_state_mapping()
            
            print(f"✅ 最优模型训练完成")
            print(f"📊 使用训练比例: {train_ratio:.2f}")
            print(f"📊 BIC分数: {self.model.bic(X_train_scaled):.2f}")
            
            return True
            
        except Exception as e:
            print(f"❌ 模型训练失败: {e}")
            return False
    
    def _identify_state_mapping(self):
        """识别状态映射"""
        state_means = pd.DataFrame(self.model.means_, columns=self.features)
        
        # 按收益率排序识别状态
        sorted_by_return = state_means.sort_values('log_return')
        
        bear_state = sorted_by_return.index[0]
        bull_state = sorted_by_return.index[-1] 
        neutral_state = sorted_by_return.index[1] if self.n_states == 3 else None
        
        self.state_map = {
            bear_state: "下跌",
            bull_state: "上涨"
        }
        
        if neutral_state is not None:
            self.state_map[neutral_state] = "盘整"
        
        print("🎯 状态映射:")
        for state, regime in self.state_map.items():
            ret = state_means.loc[state, 'log_return']
            ma = state_means.loc[state, 'ma_diff']
            print(f"  状态{state} -> {regime} (收益:{ret:.4f}, 均线:{ma:.4f})")
    
    def predict_states(self, start_idx: int = 0) -> pd.DataFrame:
        """预测状态序列"""
        if self.model is None or self.scaler is None:
            raise ValueError("请先训练模型")
        
        # 预测状态
        data_subset = self.df.iloc[start_idx:]
        X = data_subset[self.features].values
        X_scaled = self.scaler.transform(X)
        
        states = self.model.predict(X_scaled)
        
        # 生成结果DataFrame
        result_df = data_subset.copy()
        result_df['state'] = states
        result_df['regime'] = result_df['state'].map(self.state_map)
        
        # 交易信号
        signal_map = {"上涨": 1, "下跌": -1, "盘整": 0}
        result_df['signal'] = result_df['regime'].map(signal_map).shift(1).fillna(0)
        
        return result_df
    
    def display_recent_analysis(self, hours_back: int = 48):
        """显示最近的交易分析"""
        if self.df is None:
            print("❌ 请先加载数据")
            return
        
        # 获取最近48小时数据
        recent_data = self.df.tail(hours_back * 60)  # 1分钟数据
        
        # 预测状态
        pred_df = self.predict_states(len(self.df) - len(recent_data))
        pred_recent = pred_df.tail(len(recent_data))
        
        # 准备K线数据 - 修复时区显示问题
        chart_data = pred_recent[['Open', 'High', 'Low', 'Close']].copy()
        chart_data.columns = ['open', 'high', 'low', 'close']  # 标准化列名
        if 'Volume' in pred_recent.columns:
            chart_data['volume'] = pred_recent['Volume']
        else:
            chart_data['volume'] = 1000  # 默认成交量
        
        # 关键修复：调整历史数据的时间戳显示
        # lightweight-charts将时间戳当作UTC处理，我们需要将UTC时间调整+8小时
        # 这样图表显示的就是北京时间
        # 关键修复：在增加8小时后，移除时区信息，确保图表库不会误解
        chart_data.index = (chart_data.index + pd.Timedelta(hours=8)).tz_localize(None)
        
        # 设置数据
        self.chart.set(chart_data)
        
        # 添加状态变化标记
        self._add_regime_markers(pred_recent)
        
        self.last_processed_index = len(self.df)
        
        print(f"📊 显示最近{hours_back}小时分析，包含 {len(chart_data)} 根K线")
    
    def _add_regime_markers(self, df: pd.DataFrame):
        """添加状态变化标记 - 修复时区显示"""
        signal_changes = df['signal'] != df['signal'].shift(1)
        
        # 在信号变化点添加明显标记
        for idx, row in df[signal_changes].iterrows():
            signal = row['signal']
            regime = row['regime']
            
            try:
                # 关键修复：标记时间也需要调整时区，并移除时区信息
                marker_time = (idx + pd.Timedelta(hours=8)).tz_localize(None)
                
                if signal == 1:  # 买入信号
                    self.chart.marker(
                        time=marker_time,  # 使用调整后的时间
                        position='below',
                        shape='arrow_up',
                        color=self.state_colors['上涨'],
                        text=f'{regime}'
                    )
                elif signal == -1:  # 卖出信号
                    self.chart.marker(
                        time=marker_time,
                        position='above', 
                        shape='arrow_down',
                        color=self.state_colors['下跌'],
                        text=f'{regime}'
                    )
                elif signal == 0 and regime == '盘整':  # 盘整信号
                    self.chart.marker(
                        time=marker_time,
                        position='below',
                        shape='circle',
                        color=self.state_colors['盘整'],
                        text=f'{regime}'
                    )
            except Exception as e:
                # 静默处理标记错误，不影响主要功能
                print(f"⚠️ 状态标记添加失败 ({regime}): {e}")
                continue
    
    def start_realtime_monitoring(self, update_interval_minutes: int = 1, max_updates: int = 1440):
        """启动实时监控 - 基于MT5的K线数据获取策略"""
        print(f"\n🔄 启动实时监控 (每{update_interval_minutes}分钟更新)")
        print(f"💡 MT5实时策略：获取完整K线数据，确保时间连续性")
        print(f"💡 将连续监控{max_updates//60}小时")
        
        def fetch_latest_data():
            """获取最新K线数据 - 使用MT5"""
            return self._fetch_complete_mt5_data()
        
        def update_chart_with_new_data(new_data_row, current_time):
            """更新图表with新数据 - MT5数据格式"""
            if self.model is None or self.scaler is None:
                print("⚠️ 模型或标准化器未初始化，跳过状态预测")
                return
            
            try:
                # 确保数据为标量值 - MT5数据格式
                close_price = float(new_data_row['close'])
                open_price = float(new_data_row['open'])
                high_price = float(new_data_row['high'])
                low_price = float(new_data_row['low'])
                volume = float(new_data_row.get('volume', 1.0))
                
                # 计算技术指标（基于历史数据）
                historical_prices = [float(x) for x in self.df['Close'].tail(self.ma_slow).values]
                recent_prices = historical_prices + [close_price]
                
                # 计算特征 - 确保所有值都是标量
                if len(recent_prices) >= 2:
                    log_return = float(np.log(recent_prices[-1] / recent_prices[-2]))
                else:
                    log_return = 0.0
                
                if len(recent_prices) >= 6:
                    momentum_5m = float(np.log(recent_prices[-1] / recent_prices[-6]))
                else:
                    momentum_5m = 0.0
                    
                if len(recent_prices) >= 21:
                    momentum_20m = float(np.log(recent_prices[-1] / recent_prices[-21]))
                    ma_fast = float(np.mean(recent_prices[-self.ma_fast:]))
                    ma_slow = float(np.mean(recent_prices[-self.ma_slow:]))
                    price_position = float((recent_prices[-1] - ma_slow) / ma_slow)
                    ma_diff = float((ma_fast - ma_slow) / ma_slow)
                else:
                    momentum_20m = 0.0
                    price_position = 0.0
                    ma_diff = 0.0
                
                # 构建特征向量
                features = np.array([[log_return, momentum_5m, momentum_20m, price_position, ma_diff]], dtype=np.float64)
                
                # 标准化并预测
                features_scaled = self.scaler.transform(features)
                state = self.model.predict(features_scaled)[0]
                regime = self.state_map[state]
                
                # 生成交易信号
                signal_map = {"上涨": 1, "下跌": -1, "盘整": 0}
                signal = signal_map[regime]
                
                # 检查时间连续性
                try:
                    # 确保时间戳格式正确
                    if hasattr(current_time, 'timestamp'):
                        chart_time = current_time
                    else:
                        chart_time = pd.Timestamp(current_time)
                    
                    # 检查是否比上次更新的时间更新
                    if hasattr(self, '_last_chart_time'):
                        if chart_time <= self._last_chart_time:
                            print(f"⏭️ 跳过过时数据: {chart_time.strftime('%H:%M:%S')} <= {self._last_chart_time.strftime('%H:%M:%S')}")
                            return
                    
                    # 关键修复：将时间戳转换为所需的显示时区
                    # lightweight-charts处理所有时间为UTC，所以我们需要调整时间戳
                    # 将UTC时间调整为北京时间(GMT+8)，这样图表显示就是北京时间
                    # 关键修复：在增加8小时后，移除时区信息，确保图表库不会误解
                    chart_display_time = (chart_time + pd.Timedelta(hours=8)).tz_localize(None)
                    
                    # 更新图表 - 传入调整后的时间戳
                    update_data = pd.Series({
                        'time': chart_display_time,
                        'open': open_price,
                        'high': high_price,
                        'low': low_price,
                        'close': close_price,
                        'volume': volume
                    })
                    
                    self.chart.update(update_data)
                    self._last_chart_time = chart_time  # 记录最后更新时间(仍用UTC)
                    
                    # 显示时间转换为北京时间
                    display_time_str = chart_display_time.strftime('%H:%M:%S')
                    print(f"📊 图表已更新: ${close_price:.2f} (Vol: {volume:.0f}) [{display_time_str}]")
                    
                except Exception as e:
                    print(f"⚠️ 图表更新失败: {e}")
                    # 继续执行，不影响状态识别
                
                # 检查信号变化
                last_signal = getattr(self, '_last_signal', None)
                if signal != last_signal:
                    # 状态变化提示 - 使用北京时间
                    status_emoji = "🟢" if signal == 1 else "🔴" if signal == -1 else "🟡"
                    time_local = current_time.astimezone(timezone(timedelta(hours=8)))
                    time_str = time_local.strftime('%H:%M')
                    print(f"{status_emoji} {time_str}: {regime} 信号 | ${close_price:.2f}")
                    
                    # 添加实时标记 - 修复时区
                    try:
                        # 标记时间也需要调整时区
                        marker_time = chart_time + pd.Timedelta(hours=8)
                        
                        if signal == 1:
                            self.chart.marker(
                                time=marker_time,
                                position='below',
                                shape='arrow_up', 
                                color=self.state_colors['上涨'],
                                text=f'{regime}'
                            )
                        elif signal == -1:
                            self.chart.marker(
                                time=marker_time,
                                position='above',
                                shape='arrow_down',
                                color=self.state_colors['下跌'],
                                text=f'{regime}'
                            )
                        elif signal == 0:
                            self.chart.marker(
                                time=marker_time,
                                position='below',
                                shape='circle',
                                color=self.state_colors['盘整'],
                                text=f'{regime}'
                            )
                    except Exception as e:
                        print(f"⚠️ 标记添加失败: {e}")
                    
                    # 触发回调
                    if self.on_signal_change:
                        data_dict = {
                            'Close': close_price,
                            'Open': open_price,
                            'High': high_price,
                            'Low': low_price,
                            'Volume': volume
                        }
                        self.on_signal_change(signal, regime, data_dict)
                
                self._last_signal = signal
                
            except Exception as e:
                print(f"❌ 更新图表失败: {e}")
        
        def monitoring_loop():
            """
            实时监控循环 - 完全复制mt5_simple_kline_monitor.py的逻辑:
            - 在新分钟开始后的前20秒内检查新K线
            - 通过比较candle_time > last_candle_time来避免重复处理
            - 获取到新K线后立即更新图表和状态
            """
            update_count = 0
            last_candle_time = None
            
            print("⏰ 实时监控已启动 (MT5策略，与simple_kline_monitor一致)...")

            # 连接MT5
            if not self.fetcher.connect():
                print("❌ MT5连接失败，无法启动实时监控")
                return

            # 获取初始K线
            try:
                initial_candle = fetch_latest_data()
                if initial_candle:
                    last_candle_time = initial_candle['datetime']
                    update_chart_with_new_data(initial_candle, last_candle_time)
                    close_price = float(initial_candle['close'])
                    volume = float(initial_candle.get('volume', 0))
                    
                    # 转换为北京时间显示
                    time_local = last_candle_time.astimezone(timezone(timedelta(hours=8)))
                    time_str = time_local.strftime('%H:%M:%S')
                    print(f"📊 初始K线 | {time_str} 北京时间 | ${close_price:.2f} (Vol: {volume:.0f})")
            except Exception as e:
                print(f"⚠️ 获取初始K线失败: {e}")

            try:
                start_time = datetime.now(timezone.utc)
                end_time = start_time + timedelta(minutes=max_updates)
                
                while update_count < max_updates and datetime.now(timezone.utc) < end_time:
                    try:
                        current_time = datetime.now(timezone.utc)

                        # 检查是否在交易时间
                        if current_time.weekday() >= 5:
                            print("📅 周末市场休市，等待下一个交易日...")
                            # 计算到下周一的睡眠时间
                            days_to_monday = (7 - current_time.weekday()) % 7
                            next_monday = (current_time + timedelta(days=days_to_monday)).replace(hour=0, minute=0, second=0, microsecond=0)
                            sleep_duration = (next_monday - current_time).total_seconds()
                            time.sleep(min(sleep_duration, 3600))  # 最多睡眠1小时后重新检查
                            continue

                        # 核心逻辑：在新分钟开始后的前20秒内检查新K线
                        if current_time.second <= 20:
                            # 获取最新K线
                            latest_candle = fetch_latest_data()
                            if latest_candle is None:
                                time.sleep(5)  # 短暂等待后重试
                                continue
                            
                            candle_time = latest_candle['datetime']
                            
                            # 检查是否是新K线 - 与mt5_simple_kline_monitor.py完全一致
                            if (last_candle_time is None or candle_time > last_candle_time):
                                # 发现新K线，立即处理
                                last_candle_time = candle_time
                                
                                # 更新图表和状态 - 关键：立即更新
                                update_chart_with_new_data(latest_candle, candle_time)
                                
                                # 显示K线信息 - 修复时区显示
                                close_price = float(latest_candle['close'])
                                volume = float(latest_candle.get('volume', 0))
                                
                                # 当前时间显示为本地时间(东八区)
                                current_local = current_time.astimezone(timezone(timedelta(hours=8)))
                                current_time_str = current_local.strftime('%H:%M:%S')
                                
                                # K线时间也转换为本地时间显示
                                k_time_local = candle_time.astimezone(timezone(timedelta(hours=8)))
                                k_time_str = k_time_local.strftime('%H:%M:%S')
                                
                                change = latest_candle['close'] - latest_candle['open']
                                change_pct = (change / latest_candle['open']) * 100
                                direction = "↗️" if change > 0 else "↘️" if change < 0 else "→"
                                
                                print(f"更新 | {k_time_str} 北京时间 | O:{latest_candle['open']:.2f} H:{latest_candle['high']:.2f} "
                                      f"L:{latest_candle['low']:.2f} C:{latest_candle['close']:.2f} | "
                                      f"{change:+.2f}({change_pct:+.2f}%) {direction} | "
                                      f"Vol:{volume:.0f} | 当前:{current_time_str} 北京时间")
                                
                                update_count += 1
                                
                                # 新K线已处理，等到下一分钟再检查
                                sleep_until_next_minute = 60 - current_time.second + 5  # 多等5秒确保进入下一分钟
                                time.sleep(max(sleep_until_next_minute, 40))
                            else:
                                time.sleep(5)  # 短暂等待后重试
                        else:
                            # 在分钟的后半段，不需要频繁检查
                            time.sleep(10)

                    except KeyboardInterrupt:
                        print("\n🛑 用户停止监控")
                        break
                    except Exception as e:
                        print(f"❌ 监控循环发生错误: {e}")
                        time.sleep(5)  # 短暂休眠后继续
                
                print(f"✅ 实时监控结束，共处理 {update_count} 根新K线")
            
            finally:
                self.fetcher.disconnect()
        
        # 在后台线程运行监控
        monitor_thread = threading.Thread(target=monitoring_loop)
        monitor_thread.daemon = True
        monitor_thread.start()
        
        return monitor_thread
    
    def show_dashboard(self, block: bool = True):
        """显示交易仪表板"""
        print(f"\n📊 启动HMM状态识别仪表板 ({self.symbol})")
        print(f"💡 专注状态识别: 🟢上涨 🔴下跌 🟡盘整")
        print(f"💡 提示: 关闭图表窗口结束程序")
        
        self.chart.show(block=block)
    
    def _fetch_complete_mt5_data(self):
        """获取MT5最新完整K线数据 - 与mt5_simple_kline_monitor.py完全一致"""
        try:
            # 使用内嵌的MT5数据获取器获取最新完整K线
            candle = self.fetcher.get_latest_complete_candle("M1")
            
            if candle is None:
                return None
            
            # 返回的格式已经与mt5_simple_kline_monitor.py一致
            # {'datetime': pd.Timestamp, 'open': float, 'high': float, 'low': float, 'close': float, 'volume': float}
            return candle
            
        except Exception as e:
            # 静默处理错误，避免刷屏
            return None
    
    def set_signal_callback(self, callback: Callable):
        """设置信号变化回调函数"""
        self.on_signal_change = callback

def main():
    """主程序 - HMM黄金状态识别系统 (MT5版本)"""
    print("=" * 80)
    print("🚀 HMM黄金状态识别系统 (MT5版本)")
    print("📈 基于MT5 XAUUSD数据的实时状态监控")
    print("=" * 80)
    
    # 初始化系统
    system = HMMTradingSystemMT5(
        symbol="XAUUSD",
        n_states=3,
        ma_fast=20,
        ma_slow=35
    )
    
    # 设置信号回调
    def on_signal_change(signal, regime, data_dict):
        action = "买入" if signal == 1 else "卖出" if signal == -1 else "观望"
        close_price = data_dict['Close']
        print(f"🎯 交易提示: {action} | {regime} | ${close_price:.2f}")
    
    system.set_signal_callback(on_signal_change)
    
    try:
        # 步骤1: 下载数据
        if not system.download_gold_data(days=20):
            print("❌ 数据下载失败，退出程序")
            return
        
        # 步骤2: 加载和准备数据
        if not system.load_and_prepare_data():
            print("❌ 数据准备失败，退出程序")
            return
        
        # 步骤3: 前向展开分析优化参数
        best_params = system.walk_forward_analysis()
        if not best_params:
            print("❌ 参数优化失败，使用默认参数")
        
        # 步骤4: 训练最优模型
        if not system.train_optimal_model():
            print("❌ 模型训练失败，退出程序")
            return

        # 步骤4.5: 填补数据断层，确保数据连续性
        system._fill_data_gap()
        
        # 步骤5: 显示最近48小时分析
        system.display_recent_analysis(hours_back=48)
        
        # 步骤6: 启动实时监控
        monitor_thread = system.start_realtime_monitoring(
            update_interval_minutes=1, 
            max_updates=1440  # 24小时监控
        )
        
        # 步骤7: 显示交易仪表板
        print(f"\n💡 建议每 {system.retrain_frequency_hours:.0f} 小时重新运行系统进行模型更新")
        system.show_dashboard(block=True)
        
    except KeyboardInterrupt:
        print("\n\n用户中断程序")
    except Exception as e:
        print(f"\n\n程序执行异常: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n✅ HMM黄金状态识别系统结束")

if __name__ == "__main__":
    main()