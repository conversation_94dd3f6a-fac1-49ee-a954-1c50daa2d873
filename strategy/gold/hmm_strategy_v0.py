

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from hmmlearn.hmm import GaussianHMM
import os
from matplotlib.patches import Patch

# --- Core Functions ---

def load_and_prepare_data(file_path: str) -> pd.DataFrame:
    """Loads, flattens columns, and engineers features for the HMM model."""
    if not os.path.exists(file_path):
        raise FileNotFoundError(f"Data file not found at {file_path}")
    
    df = pd.read_parquet(file_path)
    if isinstance(df.columns, pd.MultiIndex):
        df.columns = df.columns.get_level_values(0)

    print("Data loaded successfully.")
    
    # Feature Engineering
    df['log_return'] = np.log(df['Close'] / df['Close'].shift(1))
    df['volatility'] = df['log_return'].rolling(window=60).std()
    delta = df['Close'].diff()
    gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
    rs = gain / loss
    df['rsi'] = 100 - (100 / (1 + rs))
    
    df.dropna(inplace=True)
    print("Features calculated: log_return, volatility, rsi")
    return df

def train_hmm_model(X_train: np.ndarray, n_states_range: range = range(2, 5)):
    """Finds the best HMM model using BIC."""
    print("\n--- 1. Quantitative Evaluation: Finding Optimal Number of States (BIC) ---")
    best_model, best_bic = None, np.inf

    for n_components in n_states_range:
        try:
            model = GaussianHMM(n_components=n_components, covariance_type="full", n_iter=1000, random_state=42)
            model.fit(X_train)
            bic = model.bic(X_train)
            print(f"N_components={n_components}, BIC={bic:.2f}")
            if bic < best_bic:
                best_bic, best_model = bic, model
        except Exception as e:
            print(f"Error fitting model with {n_components} components: {e}")

    if best_model is None: raise RuntimeError("Failed to train any HMM model.")
    print(f"\nBest model found with {best_model.n_components} components.")
    return best_model

# --- Evaluation Framework Functions ---

def perform_full_evaluation(model: GaussianHMM, train_df: pd.DataFrame, features: list):
    """Performs a comprehensive qualitative and quantitative evaluation of the HMM model."""
    
    print("\n--- 2. Qualitative & Quantitative Evaluation ---")
    
    state_means = pd.DataFrame(model.means_, columns=features).sort_values(by='log_return')
    print("\n2.A. State Statistical Profiles:")
    print(state_means)

    bear_market_state, bull_market_state = state_means.index[0], state_means.index[-1]
    if bear_market_state == bull_market_state: raise ValueError("Bull and Bear states are the same.")
    state_map = {bull_market_state: "Bull", bear_market_state: "Bear"}
    print(f"\nRegime Interpretation -> Bull: State {bull_market_state}, Bear: State {bear_market_state}")

    print("\n2.B. State Persistence & Transition Analysis:")
    print(pd.DataFrame(model.transmat_, columns=[f'To {i}' for i in range(model.n_components)], index=[f'From {i}' for i in range(model.n_components)]).round(3))
    
    expected_durations = 1 / (1 - np.diag(model.transmat_))
    print("\nExpected State Durations (in minutes):")
    for i, duration in enumerate(expected_durations):
        print(f"  - State {i} ({state_map.get(i, 'Neutral')}): {duration:.1f} minutes")

    train_df['hidden_state'] = model.predict(train_df[features].values)
    train_df['next_period_vol'] = train_df['log_return'].shift(-1).abs()
    vol_prediction = train_df.groupby('hidden_state')['next_period_vol'].mean().sort_values(ascending=False)
    print("\n2.C. Predictive Power for Next-Period Volatility:")
    print(vol_prediction)
    if vol_prediction.index[0] == bear_market_state:
        print("Conclusion: Correct. The 'Bear' state correctly predicts higher future volatility.")
    else:
        print("Conclusion: Warning. The 'Bear' state does not predict higher future volatility.")
        
    return state_map

def run_backtest_and_visualize(df: pd.DataFrame, model: GaussianHMM, state_map: dict, features: list, start_date: pd.Timestamp):
    """Runs the backtest and creates a consolidated visualization with clear background shading."""
    print("\n--- 3. Final Backtest & Visualization --- ")
    
    df['hidden_state'] = model.predict(df[features].values)
    df['regime'] = df['hidden_state'].map(state_map).fillna("Neutral")
    df['signal'] = df['regime'].map({"Bull": 1, "Bear": -1, "Neutral": 0}).shift(1).fillna(0)
    df['strategy_return'] = df['log_return'] * df['signal']
    df['bnh_return'] = df['log_return']

    # --- Visualization --- 
    fig, axes = plt.subplots(2, 2, figsize=(18, 12), gridspec_kw={'height_ratios': [3, 2]})
    fig.suptitle('HMM Gold Strategy: Comprehensive Analysis (Gapless Timeline)', fontsize=16, fontweight='bold')
    plt.style.use('seaborn-v0_8-darkgrid')

    x_axis = np.arange(len(df.index))
    num_ticks = 6
    tick_indices = np.linspace(0, len(x_axis) - 1, num_ticks, dtype=int)
    tick_labels = [df.index[i].strftime('%Y-%m-%d') for i in tick_indices]

    # 1. Cumulative Returns
    ax1 = axes[0, 0]
    ax1.plot(x_axis, np.exp(df['strategy_return'].cumsum()), label='HMM Strategy', linewidth=2)
    ax1.plot(x_axis, np.exp(df['bnh_return'].cumsum()), label='Buy & Hold', linestyle='--', alpha=0.7)
    ax1.set_title('1. Strategy Performance', fontsize=12, fontweight='bold')
    ax1.set_ylabel('Cumulative Return'); ax1.legend(); ax1.grid(True)
    ax1.set_xticks(tick_indices); ax1.set_xticklabels(tick_labels, rotation=15, ha='right')

    # 2. Price with Regimes (Corrected Visualization)
    ax2 = axes[0, 1]
    ax2.plot(x_axis, df['Close'], color='black', label='Gold Price', lw=1.0)
    
    state_map_inv = {v: k for k, v in state_map.items()}
    bull_state, bear_state = state_map_inv.get("Bull"), state_map_inv.get("Bear")
    other_states = [s for s in range(model.n_components) if s not in [bull_state, bear_state]]
    state_colors = {bull_state: 'lightgreen', bear_state: 'lightcoral'}
    color_palette = ['lightyellow', 'lightblue', 'lightgrey']
    for i, state in enumerate(other_states):
        state_colors[state] = color_palette[i % len(color_palette)]

    # Use fill_between to avoid overlapping color blocks
    for i in range(model.n_components):
        state_mask = (df['hidden_state'] == i)
        ax2.fill_between(x_axis, 0, df['Close'].max()*1.1, where=state_mask,
                        facecolor=state_colors[i], alpha=0.4)

    # Set y-axis to auto-adapt to price range
    min_val = float(df['Close'].min())
    max_val = float(df['Close'].max())
    ax2.set_ylim(min_val * 0.95, max_val * 1.05)
    
    ax2.set_title('2. Visual Inspection: Price & Regimes', fontsize=12, fontweight='bold')
    legend_elements = [
        Patch(color='black', label='Gold Price'),
        Patch(facecolor=state_colors[bull_state], alpha=0.4, label=f'Bull (State {bull_state})'),
        Patch(facecolor=state_colors[bear_state], alpha=0.4, label=f'Bear (State {bear_state})'),
    ]
    for state in other_states:
        legend_elements.append(Patch(facecolor=state_colors[state], alpha=0.4, label=f'Neutral (State {state})'))
    ax2.legend(handles=legend_elements); ax2.grid(True)
    ax2.set_xticks(tick_indices); ax2.set_xticklabels(tick_labels, rotation=15, ha='right')

    # 3. Regime Timeline
    ax3 = axes[1, 0]
    ax3.plot(x_axis, df['hidden_state'], drawstyle='steps-post', linewidth=1.5)
    ax3.set_title('3. Regime Timeline', fontsize=12, fontweight='bold')
    ax3.set_yticks(range(model.n_components))
    ax3.set_yticklabels([f"State {i} ({state_map.get(i, 'Neutral')})" for i in range(model.n_components)])
    ax3.grid(True)
    ax3.set_xticks(tick_indices); ax3.set_xticklabels(tick_labels, rotation=15, ha='right')

    # 4. Performance Metrics
    ax4 = axes[1, 1]
    ax4.axis('off')
    def calculate_metrics(returns, name):
        if returns.empty: return {k: "0.00%" for k in ["Total Return", "Annualized Return"]} | {"Sharpe Ratio": "0.00"}
        median_td = returns.index.to_series().diff().median()
        ppy = (pd.Timedelta(days=365) / median_td) if pd.notna(median_td) else 252*24*60
        sr = np.exp(returns) - 1
        tr = (1 + sr).prod() - 1
        ar = (1 + sr.mean())**ppy - 1
        sh = ar / (sr.std() * np.sqrt(ppy)) if sr.std() != 0 else 0
        return {"Name": name, "Total Return": f"{tr:.2%}", "Annualized Return": f"{ar:.2%}", "Sharpe Ratio": f"{sh:.2f}"}
    
    metrics = [calculate_metrics(df['strategy_return'], "HMM Strategy"), calculate_metrics(df['bnh_return'], "Buy & Hold")]
    table = ax4.table(cellText=[list(m.values()) for m in metrics], colLabels=list(metrics[0].keys()), loc='center', cellLoc='center')
    table.auto_set_font_size(False); table.set_fontsize(10); table.scale(1.1, 1.5)
    ax4.set_title('4. Performance Metrics', fontsize=12, fontweight='bold', y=0.8)

    plt.tight_layout(rect=[0, 0, 1, 0.96])
    plt.show()

def main():
    """Main function to orchestrate the HMM strategy evaluation."""
    try:
        file_path = os.path.join("data/gold", "1m.parquet")
        TRAIN_TEST_SPLIT_DATE = pd.to_datetime("2025-08-16", utc=True)
        features = ['log_return', 'volatility', 'rsi']
        
        featured_df = load_and_prepare_data(file_path)
        
        train_data = featured_df[featured_df.index < TRAIN_TEST_SPLIT_DATE]
        test_data = featured_df[featured_df.index >= TRAIN_TEST_SPLIT_DATE]
        
        if train_data.empty or test_data.empty: raise ValueError("Not enough data for train/test split.")
            
        model = train_hmm_model(train_data[features].values)
        state_map = perform_full_evaluation(model, train_data.copy(), features)
        run_backtest_and_visualize(test_data.copy(), model, state_map, features, TRAIN_TEST_SPLIT_DATE)

    except (FileNotFoundError, ValueError, RuntimeError) as e:
        print(f"\nExecution failed: {e}")
    except Exception as e:
        print(f"\nAn unexpected critical error occurred: {e}")

if __name__ == "__main__":
    main()
