import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from hmmlearn.hmm import GaussianHMM
from sklearn.preprocessing import StandardScaler
import os
from matplotlib.patches import Patch

# 设置matplotlib支持中文显示
plt.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

# --- 简化版核心函数 ---

def load_and_prepare_data_simple(file_path: str) -> tuple[pd.DataFrame, list]:
    """加载数据并进行简化特征工程，专注于核心趋势识别特征"""
    if not os.path.exists(file_path):
        raise FileNotFoundError(f"数据文件未找到: {file_path}")
    
    df = pd.read_parquet(file_path)
    if isinstance(df.columns, pd.MultiIndex):
        df.columns = df.columns.get_level_values(0)

    print("数据加载成功")
    
    # === 简化特征工程：保留最核心的趋势识别特征 ===
    
    # 1. 基础收益特征
    df['log_return'] = np.log(df['Close'] / df['Close'].shift(1))
    df['log_hl_range'] = np.log(df['High'] / df['Low'])
    
    # 2. 动量特征（保留最有效的）
    df['momentum_5m'] = np.log(df['Close'] / df['Close'].shift(5))
    df['momentum_20m'] = np.log(df['Close'] / df['Close'].shift(20))
    
    # 3. 简单双均线特征
    df['sma_fast'] = df['Close'].rolling(window=10).mean()
    df['sma_slow'] = df['Close'].rolling(window=30).mean()
    df['price_position'] = (df['Close'] - df['sma_slow']) / df['sma_slow']  # 价格相对位置
    df['ma_diff'] = (df['sma_fast'] - df['sma_slow']) / df['sma_slow']     # 均线差值
    
    # 简化特征列表 - 只用5个最核心的特征
    features = ['log_return', 'momentum_5m', 'momentum_20m', 'price_position', 'ma_diff']
    
    df.dropna(inplace=True)
    print(f"简化特征计算完成: {', '.join(features)}")
    print(f"特征工程后数据形状: {df.shape}")
    
    return df, features

def train_hmm_model_simple(X_train: np.ndarray, n_states: int = 3):
    """训练简化的HMM模型"""
    print(f"\n--- 训练简化3状态HMM模型 ---")
    
    scaler = StandardScaler()
    X_train_scaled = scaler.fit_transform(X_train)
    
    try:
        model = GaussianHMM(
            n_components=n_states,
            covariance_type="diag",
            n_iter=500,           # 减少迭代次数
            random_state=42,
            tol=1e-4             # 放宽收敛标准
        )
        model.fit(X_train_scaled)
        bic = model.bic(X_train_scaled)
        print(f"模型训练完成，BIC={bic:.2f}")
        
    except Exception as e:
        raise RuntimeError(f"HMM模型训练失败: {e}")

    return model, scaler

def identify_regime_states_simple(model: GaussianHMM, features: list) -> dict:
    """简化的状态识别：基于收益率和趋势特征"""
    
    print(f"\n--- 简化状态识别 ---")
    
    state_means = pd.DataFrame(model.means_, columns=features)
    print("\n各状态统计特征：")
    print(state_means.round(4))
    
    # 简化识别逻辑：主要看log_return和ma_diff
    state_interpretations = {}
    
    # 按平均收益率排序
    sorted_by_return = state_means.sort_values('log_return')
    
    bear_state = sorted_by_return.index[0]    # 收益率最低
    bull_state = sorted_by_return.index[-1]   # 收益率最高
    neutral_state = sorted_by_return.index[1] # 中间收益率
    
    # 验证逻辑：检查ma_diff是否与收益率一致
    bear_ma_diff = state_means.loc[bear_state, 'ma_diff']
    bull_ma_diff = state_means.loc[bull_state, 'ma_diff']
    
    if bear_ma_diff < 0 and bull_ma_diff > 0:
        print("✓ 状态识别逻辑一致：熊市状态均线向下，牛市状态均线向上")
    else:
        print("⚠️ 警告：状态识别逻辑可能有问题")
    
    state_interpretations = {
        bear_state: "下跌",
        bull_state: "上涨", 
        neutral_state: "盘整"
    }
    
    print(f"\n状态映射结果：")
    for state, regime in state_interpretations.items():
        mean_return = state_means.loc[state, 'log_return']
        mean_ma_diff = state_means.loc[state, 'ma_diff']
        print(f"  状态 {state} -> {regime} (收益:{mean_return:.6f}, 均线差:{mean_ma_diff:.6f})")
    
    return state_interpretations

def run_simple_backtest(df: pd.DataFrame, model: GaussianHMM, state_map: dict, 
                       features: list, scaler, start_date: pd.Timestamp):
    """运行简化回测"""
    print(f"\n--- 简化回测分析 ---")
    
    df = df.copy()
    X_test_scaled = scaler.transform(df[features].values)
    
    df['hidden_state'] = model.predict(X_test_scaled)
    df['regime'] = df['hidden_state'].map(state_map).fillna("未知")
    
    # 简单信号生成
    regime_signal_map = {"上涨": 1, "下跌": -1, "盘整": 0}
    df['signal'] = df['regime'].map(regime_signal_map).shift(1).fillna(0)
    
    df['strategy_return'] = df['log_return'] * df['signal']
    df['bnh_return'] = df['log_return']
    
    # 计算基准策略
    df['sma_fast'] = df['Close'].rolling(window=10).mean()
    df['sma_slow'] = df['Close'].rolling(window=30).mean()
    ma_signal_raw = np.where(df['sma_fast'] > df['sma_slow'], 1, -1)
    df['ma_signal'] = pd.Series(ma_signal_raw, index=df.index).shift(1).fillna(0)
    df['ma_strategy_return'] = df['log_return'] * df['ma_signal']
    
    # 性能指标
    def calc_metrics(returns, name):
        total_return = np.exp(returns.sum()) - 1
        sharpe = returns.mean() / returns.std() * np.sqrt(252*24*60) if returns.std() > 0 else 0
        cumulative = np.exp(returns.cumsum())
        running_max = cumulative.expanding().max()
        drawdown = (cumulative - running_max) / running_max
        max_dd = drawdown.min()
        win_rate = (returns > 0).sum() / len(returns)
        
        return {
            'name': name,
            'total_return': total_return,
            'sharpe': sharpe,
            'max_drawdown': max_dd,
            'win_rate': win_rate
        }
    
    results = {
        'strategy': calc_metrics(df['strategy_return'], 'HMM策略'),
        'bnh': calc_metrics(df['bnh_return'], '买入持有'),
        'ma': calc_metrics(df['ma_strategy_return'], '双均线策略')
    }
    
    # 状态统计
    state_counts = df['regime'].value_counts()
    state_transitions = (df['regime'] != df['regime'].shift(1)).sum()
    
    print(f"\n📊 简化策略性能：")
    for key, metrics in results.items():
        print(f"{metrics['name']}: 收益{metrics['total_return']:6.2%}, 夏普{metrics['sharpe']:7.3f}, 最大回撤{metrics['max_drawdown']:6.2%}")
    
    print(f"\n📈 状态统计：")
    for regime, count in state_counts.items():
        print(f"{regime}状态: {count}次 ({count/len(df)*100:.1f}%)")
    print(f"状态切换次数: {state_transitions}")
    
    # 简化可视化
    visualize_simple_strategy(df, results, state_counts, state_transitions)
    
    return df, results

def visualize_simple_strategy(df: pd.DataFrame, results: dict, state_counts: pd.Series, transitions: int):
    """简化的可视化"""
    
    fig, axes = plt.subplots(2, 2, figsize=(18, 12))
    fig.suptitle('HMM简化策略：回归基础的趋势盘整识别', fontsize=16, fontweight='bold')
    plt.style.use('seaborn-v0_8-darkgrid')
    # 重新设置中文字体（防止被样式覆盖）
    plt.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'SimHei', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False
    
    x_axis = np.arange(len(df.index))
    num_ticks = 6
    tick_indices = np.linspace(0, len(x_axis) - 1, num_ticks, dtype=int)
    tick_labels = [df.index[i].strftime('%m-%d %H:%M') for i in tick_indices]
    
    # 1. 策略收益对比
    ax1 = axes[0, 0]
    ax1.plot(x_axis, np.exp(df['strategy_return'].cumsum()), 
             label='HMM简化策略', linewidth=2.5, color='darkblue')
    ax1.plot(x_axis, np.exp(df['bnh_return'].cumsum()), 
             label='买入持有', linestyle='--', alpha=0.7, color='gray')
    ax1.plot(x_axis, np.exp(df['ma_strategy_return'].cumsum()), 
             label='双均线策略', linestyle=':', alpha=0.8, color='orange')
    
    ax1.set_title('1. 策略收益对比', fontsize=12, fontweight='bold')
    ax1.set_ylabel('累积收益'); ax1.legend(); ax1.grid(True)
    ax1.set_xticks(tick_indices); ax1.set_xticklabels(tick_labels, rotation=45, ha='right')
    
    # 2. 价格与状态
    ax2 = axes[0, 1]
    ax2.plot(x_axis, df['Close'], color='black', label='黄金价格', linewidth=1.0)
    ax2.plot(x_axis, df['sma_fast'], color='blue', label='快线(10分钟)', linewidth=1.0, alpha=0.7)
    ax2.plot(x_axis, df['sma_slow'], color='red', label='慢线(30分钟)', linewidth=1.0, alpha=0.7)
    
    # 状态背景
    state_colors = {'上涨': 'lightgreen', '下跌': 'lightcoral', '盘整': 'lightyellow'}
    for regime, color in state_colors.items():
        regime_mask = (df['regime'] == regime)
        if regime_mask.any():
            ax2.fill_between(x_axis, df['Close'].min()*0.95, df['Close'].max()*1.05, 
                           where=regime_mask, facecolor=color, alpha=0.3, label=f'{regime}状态')
    
    ax2.set_title('2. 价格走势与状态识别', fontsize=12, fontweight='bold')
    ax2.legend(); ax2.grid(True)
    ax2.set_xticks(tick_indices); ax2.set_xticklabels(tick_labels, rotation=45, ha='right')
    
    # 3. 状态统计
    ax3 = axes[1, 0]
    regimes = list(state_counts.index)
    counts = list(state_counts.values)
    colors = [state_colors.get(regime, 'gray') for regime in regimes]
    
    bars = ax3.bar(regimes, counts, color=colors, alpha=0.7)
    ax3.set_title('3. 状态分布统计', fontsize=12, fontweight='bold')
    ax3.set_ylabel('出现次数')
    
    for bar, count in zip(bars, counts):
        height = bar.get_height()
        ax3.text(bar.get_x() + bar.get_width()/2., height + height*0.01,
                f'{int(count)}', ha='center', va='bottom', fontweight='bold')
    
    ax3.grid(True, alpha=0.3)
    
    # 4. 性能指标表格
    ax4 = axes[1, 1]
    ax4.axis('off')
    
    table_data = [
        ['策略', '总收益', '夏普比率', '最大回撤', '状态切换'],
        ['HMM简化策略', f"{results['strategy']['total_return']:.2%}", 
         f"{results['strategy']['sharpe']:.2f}", f"{results['strategy']['max_drawdown']:.2%}",
         f"{transitions}"],
        ['双均线策略', f"{results['ma']['total_return']:.2%}", 
         f"{results['ma']['sharpe']:.2f}", f"{results['ma']['max_drawdown']:.2%}", 'N/A'],
        ['买入持有', f"{results['bnh']['total_return']:.2%}", 
         f"{results['bnh']['sharpe']:.2f}", f"{results['bnh']['max_drawdown']:.2%}", 'N/A']
    ]
    
    table = ax4.table(cellText=table_data[1:], colLabels=table_data[0], 
                     loc='center', cellLoc='center')
    table.auto_set_font_size(False); table.set_fontsize(10); table.scale(1.1, 1.8)
    
    # 突出显示最佳表现
    for i in range(len(table_data[0])):
        table[(0, i)].set_facecolor('#4CAF50')
        table[(0, i)].set_text_props(weight='bold', color='white')
    
    ax4.set_title('4. 性能指标对比', fontsize=12, fontweight='bold', y=0.85)
    
    plt.tight_layout(rect=[0, 0, 1, 0.96])
    plt.show()

def apply_smart_smoothing(df: pd.DataFrame, min_duration: int = 8, confidence_threshold: float = 0.7):
    """应用智能平滑：只在高置信度时保留状态切换"""
    print(f"\n--- 智能平滑处理 ---")
    print(f"最短持续期: {min_duration}分钟")
    print(f"置信度阈值: {confidence_threshold}")
    
    df = df.copy()
    
    # 计算状态的"置信度"（基于连续性）
    def calculate_confidence(regime_series, window=10):
        """计算每个时点的状态置信度"""
        confidence = pd.Series(index=regime_series.index, dtype=float)
        
        for i in range(len(regime_series)):
            start_idx = max(0, i - window + 1)
            end_idx = min(len(regime_series), i + window)
            
            window_data = regime_series.iloc[start_idx:end_idx]
            current_state = regime_series.iloc[i]
            
            # 置信度 = 窗口内相同状态的比例
            confidence.iloc[i] = (window_data == current_state).sum() / len(window_data)
        
        return confidence
    
    df['regime_confidence'] = calculate_confidence(df['regime'])
    
    # 基于置信度和最短持续期的平滑
    original_transitions = (df['regime'] != df['regime'].shift(1)).sum()
    
    # 标记低置信度的状态切换
    low_confidence = df['regime_confidence'] < confidence_threshold
    regime_changes = df['regime'] != df['regime'].shift(1)
    
    # 对低置信度切换应用最短持续期规则
    df['regime_smoothed'] = df['regime'].copy()
    
    # 找到状态段并过滤短期状态
    current_state = df['regime'].iloc[0]
    segment_start = 0
    
    for i in range(1, len(df)):
        if df['regime'].iloc[i] != current_state:
            segment_length = i - segment_start
            
            # 如果段太短且置信度低，则用前一个状态替代
            if segment_length < min_duration:
                avg_confidence = df['regime_confidence'].iloc[segment_start:i].mean()
                if avg_confidence < confidence_threshold:
                    prev_state = df['regime_smoothed'].iloc[segment_start-1] if segment_start > 0 else current_state
                    df.loc[df.index[segment_start:i], 'regime_smoothed'] = prev_state
            
            current_state = df['regime'].iloc[i]
            segment_start = i
    
    smoothed_transitions = (df['regime_smoothed'] != df['regime_smoothed'].shift(1)).sum()
    
    print(f"原始状态切换次数: {original_transitions}")
    print(f"智能平滑后切换次数: {smoothed_transitions}")
    print(f"切换次数减少: {original_transitions - smoothed_transitions} ({((original_transitions - smoothed_transitions) / original_transitions * 100):.1f}%)")
    
    return df

def main():
    """主函数：简化版HMM策略"""
    try:
        file_path = os.path.join("data/gold", "1m.parquet")
        TRAIN_TEST_SPLIT_DATE = pd.to_datetime("2025-08-16", utc=True)
        
        # 使用简化特征工程
        featured_df, features = load_and_prepare_data_simple(file_path)
        
        train_data = featured_df[featured_df.index < TRAIN_TEST_SPLIT_DATE].copy()
        test_data = featured_df[featured_df.index >= TRAIN_TEST_SPLIT_DATE].copy()
        
        if train_data.empty or test_data.empty:
            raise ValueError("训练或测试数据不足")
        
        print(f"\n训练数据形状: {train_data.shape}")
        print(f"测试数据形状: {test_data.shape}")
        print(f"使用特征: {features}")
        
        # 训练简化模型
        model, scaler = train_hmm_model_simple(train_data[features].values)
        
        # 简化状态识别
        state_map = identify_regime_states_simple(model, features)
        
        # 运行基础回测
        print(f"\n=== 第一步：基础版本测试 ===")
        basic_df, basic_results = run_simple_backtest(test_data, model, state_map, features, scaler, TRAIN_TEST_SPLIT_DATE)
        
        # 如果基础版本效果好，再应用智能平滑
        if basic_results['strategy']['total_return'] > 0:
            print(f"\n=== 第二步：应用智能平滑优化 ===")
            # 应用智能平滑
            smoothed_df = apply_smart_smoothing(basic_df.copy())
            
            # 重新计算平滑后的策略收益
            regime_signal_map = {"上涨": 1, "下跌": -1, "盘整": 0}
            smoothed_df['signal_smoothed'] = smoothed_df['regime_smoothed'].map(regime_signal_map).shift(1).fillna(0)
            smoothed_df['strategy_return_smoothed'] = smoothed_df['log_return'] * smoothed_df['signal_smoothed']
            
            smoothed_total_return = np.exp(smoothed_df['strategy_return_smoothed'].sum()) - 1
            original_total_return = basic_results['strategy']['total_return']
            
            print(f"\n🏆 智能平滑优化结果：")
            print("="*50)
            print(f"基础策略收益: {original_total_return:6.2%}")
            print(f"平滑策略收益: {smoothed_total_return:6.2%}")
            print(f"优化效果: {smoothed_total_return - original_total_return:+6.2%}")
            print("="*50)
        else:
            print(f"\n⚠️ 基础版本效果不佳，需要进一步优化特征工程")
            print(f"当前策略收益: {basic_results['strategy']['total_return']:6.2%}")
        
        return basic_results

    except (FileNotFoundError, ValueError, RuntimeError) as e:
        print(f"\n执行失败: {e}")
        return None
    except Exception as e:
        print(f"\n发生意外错误: {e}")
        return None

if __name__ == "__main__":
    results = main()