import pandas as pd
import numpy as np
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import plotly.offline as pyo
from hmmlearn.hmm import GaussianHMM
from sklearn.preprocessing import StandardScaler
import os
from datetime import datetime

# --- 使用最优MA参数的核心函数 ---

def load_and_prepare_data_optimized(file_path: str) -> tuple[pd.DataFrame, list]:
    """使用最优MA参数(20,35)进行特征工程"""
    if not os.path.exists(file_path):
        raise FileNotFoundError(f"数据文件未找到: {file_path}")
    
    df = pd.read_parquet(file_path)
    if isinstance(df.columns, pd.MultiIndex):
        df.columns = df.columns.get_level_values(0)

    print("数据加载成功")
    
    # 基础特征
    df['log_return'] = np.log(df['Close'] / df['Close'].shift(1))
    df['momentum_5m'] = np.log(df['Close'] / df['Close'].shift(5))
    df['momentum_20m'] = np.log(df['Close'] / df['Close'].shift(20))
    
    # 使用最优MA参数
    df['sma_fast'] = df['Close'].rolling(window=20).mean()  # 优化后：20周期快线
    df['sma_slow'] = df['Close'].rolling(window=35).mean()  # 优化后：35周期慢线
    df['price_position'] = (df['Close'] - df['sma_slow']) / df['sma_slow']
    df['ma_diff'] = (df['sma_fast'] - df['sma_slow']) / df['sma_slow']
    
    features = ['log_return', 'momentum_5m', 'momentum_20m', 'price_position', 'ma_diff']
    
    df.dropna(inplace=True)
    print(f"使用最优MA参数: 快线MA20, 慢线MA35")
    print(f"特征: {features}")
    print(f"数据形状: {df.shape}")
    
    return df, features

def train_hmm_model_simple(X_train: np.ndarray, n_states: int = 3):
    """训练HMM模型"""
    print(f"\n--- 训练HMM模型 ---")
    
    scaler = StandardScaler()
    X_train_scaled = scaler.fit_transform(X_train)
    
    model = GaussianHMM(
        n_components=n_states,
        covariance_type="diag",
        n_iter=500,
        random_state=42,
        tol=1e-4
    )
    model.fit(X_train_scaled)
    print(f"模型训练完成，BIC={model.bic(X_train_scaled):.2f}")
    
    return model, scaler

def identify_regime_states_simple(model: GaussianHMM, features: list) -> dict:
    """识别状态映射"""
    state_means = pd.DataFrame(model.means_, columns=features)
    print("\n各状态特征：")
    print(state_means[['log_return', 'ma_diff']].round(4))
    
    # 按收益率排序识别状态
    sorted_by_return = state_means.sort_values('log_return')
    
    bear_state = sorted_by_return.index[0]
    bull_state = sorted_by_return.index[-1] 
    neutral_state = sorted_by_return.index[1]
    
    state_map = {
        bear_state: "下跌",
        bull_state: "上涨",
        neutral_state: "盘整"
    }
    
    print(f"\n状态映射：")
    for state, regime in state_map.items():
        ret = state_means.loc[state, 'log_return']
        ma = state_means.loc[state, 'ma_diff']
        print(f"  状态{state} -> {regime} (收益:{ret:.4f}, 均线:{ma:.4f})")
    
    return state_map

def run_trading_analysis(df: pd.DataFrame, model: GaussianHMM, state_map: dict, 
                        features: list, scaler):
    """运行交易分析"""
    print(f"\n--- 交易分析 ---")
    
    df = df.copy()
    X_scaled = scaler.transform(df[features].values)
    
    # 预测状态和生成信号
    df['state'] = model.predict(X_scaled)
    df['regime'] = df['state'].map(state_map)
    
    # 交易信号
    signal_map = {"上涨": 1, "下跌": -1, "盘整": 0}
    df['signal'] = df['regime'].map(signal_map).shift(1).fillna(0)
    
    # 收益计算
    df['strategy_return'] = df['log_return'] * df['signal']
    df['bnh_return'] = df['log_return']
    
    # 累积收益
    df['strategy_cumret'] = np.exp(df['strategy_return'].cumsum())
    df['bnh_cumret'] = np.exp(df['bnh_return'].cumsum())
    
    # 性能统计
    total_ret = np.exp(df['strategy_return'].sum()) - 1
    bnh_ret = np.exp(df['bnh_return'].sum()) - 1
    sharpe = df['strategy_return'].mean() / df['strategy_return'].std() * np.sqrt(252*24*60)
    
    print(f"🎯 最优MA参数策略收益: {total_ret:.2%}")
    print(f"📊 基准收益: {bnh_ret:.2%}")
    print(f"⚡ 夏普比率: {sharpe:.2f}")
    
    return df, {'strategy': total_ret, 'benchmark': bnh_ret, 'sharpe': sharpe}

def create_optimized_trading_dashboard(df: pd.DataFrame, results: dict):
    """创建优化版交易仪表板"""
    print(f"\n--- 创建最优交易仪表板 ---")
    
    # 状态颜色映射
    state_colors = {
        '上涨': '#00ff88',    # 明亮绿色
        '下跌': '#ff4444',    # 明亮红色  
        '盘整': '#ffaa00'     # 橙色
    }
    
    # 进一步优化布局比例：价格图占更多空间
    fig = make_subplots(
        rows=3, cols=1,
        shared_xaxes=True,
        vertical_spacing=0.005,  # 进一步减少间距
        row_heights=[0.80, 0.08, 0.12],  # 价格图80%，状态8%，收益12%
        subplot_titles=('', '', ''),
        specs=[[{"secondary_y": False}],
               [{"secondary_y": False}], 
               [{"secondary_y": False}]]
    )
    
    # === 第1行：主价格图 ===
    
    # K线图
    fig.add_trace(
        go.Candlestick(
            x=df.index,
            open=df['Open'],
            high=df['High'],
            low=df['Low'], 
            close=df['Close'],
            name='黄金价格',
            increasing_line_color='#00ff00',
            decreasing_line_color='#ff0000',
            showlegend=False
        ),
        row=1, col=1
    )
    
    # 优化的快慢均线
    fig.add_trace(
        go.Scatter(
            x=df.index, y=df['sma_fast'],
            mode='lines',
            name='MA20 (优化)',
            line=dict(color='#00aaff', width=2),
            showlegend=True
        ),
        row=1, col=1
    )
    
    fig.add_trace(
        go.Scatter(
            x=df.index, y=df['sma_slow'],
            mode='lines',
            name='MA35 (优化)', 
            line=dict(color='#ff6600', width=2),
            showlegend=True
        ),
        row=1, col=1
    )
    
    # 添加状态背景色带
    for regime, color in state_colors.items():
        regime_mask = (df['regime'] == regime)
        if regime_mask.any():
            # 找连续区间
            starts = df.index[regime_mask & ~regime_mask.shift(1).fillna(False)]
            ends = df.index[regime_mask & ~regime_mask.shift(-1).fillna(False)]
            
            for start, end in zip(starts, ends):
                fig.add_shape(
                    type="rect",
                    x0=start, x1=end,
                    y0=df['Low'].min()*0.9995, y1=df['High'].max()*1.0005,
                    fillcolor=color,
                    opacity=0.15,
                    layer="below",
                    line_width=0,
                    row=1, col=1
                )
    
    # === 优化信号显示：只显示状态转换点 ===
    
    # 计算状态转换点
    signal_changes = df['signal'] != df['signal'].shift(1)
    
    # 买入信号点（只在状态转换时显示）
    buy_transitions = df[(df['signal'] == 1) & signal_changes]
    if not buy_transitions.empty:
        # 进一步筛选：只显示重要的买入点（间隔至少30分钟）
        filtered_buys = []
        last_time = None
        for idx, row in buy_transitions.iterrows():
            if last_time is None or (idx - last_time).total_seconds() >= 1800:  # 30分钟间隔
                filtered_buys.append((idx, row))
                last_time = idx
        
        if filtered_buys:
            buy_times, buy_data = zip(*filtered_buys)
            buy_prices = [data['Low'] * 0.998 for data in buy_data]
            
            fig.add_trace(
                go.Scatter(
                    x=list(buy_times),
                    y=buy_prices,
                    mode='markers',
                    name='关键买入信号',
                    marker=dict(
                        symbol='triangle-up', 
                        size=15, 
                        color='#00ff00',
                        line=dict(color='#006600', width=2)
                    ),
                    showlegend=True,
                    hovertemplate='关键买入信号<br>时间: %{x}<br>价格: %{y:.2f}<extra></extra>'
                ),
                row=1, col=1
            )
    
    # 卖出信号点（只在状态转换时显示）
    sell_transitions = df[(df['signal'] == -1) & signal_changes]
    if not sell_transitions.empty:
        # 进一步筛选：只显示重要的卖出点（间隔至少30分钟）
        filtered_sells = []
        last_time = None
        for idx, row in sell_transitions.iterrows():
            if last_time is None or (idx - last_time).total_seconds() >= 1800:  # 30分钟间隔
                filtered_sells.append((idx, row))
                last_time = idx
        
        if filtered_sells:
            sell_times, sell_data = zip(*filtered_sells)
            sell_prices = [data['High'] * 1.002 for data in sell_data]
            
            fig.add_trace(
                go.Scatter(
                    x=list(sell_times),
                    y=sell_prices,
                    mode='markers',
                    name='关键卖出信号',
                    marker=dict(
                        symbol='triangle-down', 
                        size=15, 
                        color='#ff0000',
                        line=dict(color='#660000', width=2)
                    ),
                    showlegend=True,
                    hovertemplate='关键卖出信号<br>时间: %{x}<br>价格: %{y:.2f}<extra></extra>'
                ),
                row=1, col=1
            )
    
    # === 第2行：状态信号图（简化） ===
    
    # 状态线图
    state_numeric = df['regime'].map({'上涨': 1, '盘整': 0, '下跌': -1})
    
    fig.add_trace(
        go.Scatter(
            x=df.index, 
            y=state_numeric,
            mode='lines',
            name='市场状态',
            line=dict(color='#8000ff', width=2),
            fill='tonexty',
            fillcolor='rgba(128,0,255,0.1)',
            showlegend=True,
            hovertemplate='状态: %{text}<br>时间: %{x}<extra></extra>',
            text=df['regime']
        ),
        row=2, col=1
    )
    
    # === 第3行：收益对比图 ===
    
    fig.add_trace(
        go.Scatter(
            x=df.index, y=df['strategy_cumret'],
            mode='lines',
            name='HMM最优策略',
            line=dict(color='#0066ff', width=3),
            showlegend=True,
            hovertemplate='HMM策略收益: %{y:.4f}<extra></extra>'
        ),
        row=3, col=1
    )
    
    fig.add_trace(
        go.Scatter(
            x=df.index, y=df['bnh_cumret'],
            mode='lines',
            name='买入持有',
            line=dict(color='#888888', width=2, dash='dash'),
            showlegend=True,
            hovertemplate='买入持有收益: %{y:.4f}<extra></extra>'
        ),
        row=3, col=1
    )
    
    # === 布局优化 ===
    
    fig.update_layout(
        # 标题强调优化效果
        title={
            'text': f'🚀 HMM最优交易系统 MA(20,35) | 策略收益: {results["strategy"]:.2%} vs 基准: {results["benchmark"]:.2%} | 夏普: {results["sharpe"]:.2f}',
            'x': 0.5,
            'xanchor': 'center',
            'font': {'size': 18, 'color': '#003366', 'family': 'Arial Black'}
        },
        
        # 尺寸优化 - 增加更多高度给K线图
        width=1900,
        height=1200,  # 进一步增加总高度
        
        # 图例
        legend=dict(
            orientation="h",
            yanchor="top",
            y=1.015,  # 调整图例位置
            xanchor="center",
            x=0.5,
            bgcolor="rgba(255,255,255,0.8)",
            bordercolor="gray",
            borderwidth=1
        ),
        
        # 背景
        plot_bgcolor='#fafafa',
        paper_bgcolor='white',
        
        # 关键：启用同步十字线
        hovermode='x unified',
        
        # 时间轴设置
        xaxis3=dict(
            title='时间',
            rangeslider=dict(
                visible=True,
                thickness=0.02  # 进一步减小滑块厚度
            ),
            type='date',
            showgrid=True,
            gridcolor='lightgray'
        ),
        
        # 移除中间子图的x轴标签
        xaxis1=dict(showticklabels=False, showgrid=True, gridcolor='lightgray'),
        xaxis2=dict(showticklabels=False, showgrid=True, gridcolor='lightgray'),
        
        # Y轴设置 - 给价格图更多空间
        yaxis1=dict(
            title='价格 (USD)', 
            showgrid=True, 
            gridcolor='lightgray',
            automargin=True,
            # 固定价格范围以提高K线清晰度
            range=[df['Low'].min()*0.999, df['High'].max()*1.001]
        ),
        yaxis2=dict(
            title='状态', 
            tickvals=[-1, 0, 1], 
            ticktext=['下跌', '盘整', '上涨'],
            range=[-1.1, 1.1],
            showgrid=True, 
            gridcolor='lightgray'
        ),
        yaxis3=dict(
            title='累积收益', 
            showgrid=True, 
            gridcolor='lightgray'
        ),
        
        # 缩放工具设置
        dragmode='zoom'
    )
    
    # 更新信息注释
    current_price = df['Close'].iloc[-1]
    current_state = df['regime'].iloc[-1] 
    current_signal = df['signal'].iloc[-1]
    signal_text = {1: '买入', -1: '卖出', 0: '持有'}[current_signal]
    
    # 统计关键信号数量
    key_buy_count = len(buy_transitions) if 'buy_transitions' in locals() else 0
    key_sell_count = len(sell_transitions) if 'sell_transitions' in locals() else 0
    
    # 与基础版本对比
    improvement = ((results['strategy'] - 0.0747) / 0.0747 * 100) if results['strategy'] > 0.0747 else 0
    
    info_text = f"""
    <b style='font-size:14px; color:#003366'>🚀 最优MA交易系统</b><br>
    <b>MA参数:</b> 快线20 | 慢线35<br>
    <b>当前价格:</b> ${current_price:.2f}<br>  
    <b>当前状态:</b> <span style='color:{state_colors.get(current_state, "black")}'>{current_state}</span><br>
    <b>当前信号:</b> <span style='color:{"#00aa00" if current_signal==1 else "#aa0000" if current_signal==-1 else "#666666"}'>{signal_text}</span><br>
    <b>策略收益:</b> {results['strategy']:.2%}<br>
    <b>性能提升:</b> <span style='color:#00aa00'>+{improvement:.1f}%</span><br>
    <b>关键信号:</b> 买入{key_buy_count} | 卖出{key_sell_count}
    """
    
    fig.add_annotation(
        text=info_text,
        xref="paper", yref="paper",
        x=0.02, y=0.98,
        xanchor="left", yanchor="top",
        showarrow=False,
        font=dict(size=11),
        bgcolor="rgba(255,255,255,0.9)",
        bordercolor="#0066ff",
        borderwidth=2,
        borderpad=8
    )
    
    return fig

def save_optimized_html(fig, filename="hmm_optimized_trading_system.html"):
    """保存优化版交易系统HTML"""
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    full_filename = f"hmm_optimized_trading_{timestamp}.html"
    
    # 交易专用配置
    config = {
        'displayModeBar': True,
        'displaylogo': False,
        'modeBarButtonsToAdd': ['drawline', 'drawopenpath', 'drawclosedpath', 'drawcircle', 'drawrect', 'eraseshape'],
        'modeBarButtonsToRemove': ['pan2d', 'lasso2d', 'autoScale2d'],
        'toImageButtonOptions': {
            'format': 'png',
            'filename': 'hmm_optimized_trading_analysis',
            'height': 1100,
            'width': 1900,
            'scale': 2
        },
        'scrollZoom': True  # 启用滚轮缩放
    }
    
    pyo.plot(fig, filename=full_filename, auto_open=True, config=config)
    
    print(f"\n🚀 最优交易系统已生成: {full_filename}")
    print("\n🎯 优化亮点:")
    print("  📈  使用实验验证的最优MA(20,35)参数")
    print("  📊  预期收益率提升31%（7.47% → 9.78%）")
    print("  ⚡  预期夏普比率提升29%（21.51 → 27.71）")
    print("  🖱️  同步十字线完美对齐")
    print("  🔍  K线图占75%空间，放大时不再扁平")
    print("  📍  买卖信号直接标注在价格图上")
    
    return full_filename

def main():
    """主函数：最优MA参数交易系统"""
    try:
        file_path = os.path.join("data/gold", "1m.parquet")
        TRAIN_TEST_SPLIT_DATE = pd.to_datetime("2025-08-16", utc=True)
        
        print("=" * 60)
        print("🚀 HMM最优交易系统 - MA参数优化版本")
        print("📊 基于实验验证的最优MA(20,35)参数")
        print("=" * 60)
        
        # 数据准备（使用最优MA参数）
        df, features = load_and_prepare_data_optimized(file_path)
        
        train_data = df[df.index < TRAIN_TEST_SPLIT_DATE].copy()
        test_data = df[df.index >= TRAIN_TEST_SPLIT_DATE].copy()
        
        print(f"\n训练数据: {train_data.shape[0]} 条")
        print(f"测试数据: {test_data.shape[0]} 条")
        
        # 训练模型
        model, scaler = train_hmm_model_simple(train_data[features].values)
        state_map = identify_regime_states_simple(model, features)
        
        # 交易分析
        analysis_df, results = run_trading_analysis(test_data, model, state_map, features, scaler)
        
        # 创建优化版仪表板
        fig = create_optimized_trading_dashboard(analysis_df, results)
        
        # 保存系统
        html_file = save_optimized_html(fig)
        
        print(f"\n✅ 最优交易系统准备就绪!")
        print(f"🎯 实际策略收益: {results['strategy']:.2%}")
        print(f"📈 基准收益: {results['benchmark']:.2%}")
        print(f"⚡ 夏普比率: {results['sharpe']:.2f}")
        
        # 验证优化效果
        expected_improvement = (9.78 - 7.47) / 7.47 * 100
        actual_improvement = (results['strategy'] - 0.0747) / 0.0747 * 100 if results['strategy'] > 0.0747 else 0
        
        print(f"\n📊 优化效果验证:")
        print(f"预期收益提升: +{expected_improvement:.1f}%")
        print(f"实际收益提升: +{actual_improvement:.1f}%")
        
        return results, html_file
        
    except Exception as e:
        print(f"\n❌ 系统错误: {e}")
        return None, None

if __name__ == "__main__":
    results, html_file = main() 