import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from hmmlearn.hmm import GaussianHMM
from sklearn.preprocessing import StandardScaler
import os
import warnings
from matplotlib.patches import Patch

warnings.filterwarnings('ignore')

# --- v3: 代码结构优化 + 特征标准化 ---

def load_and_prepare_data(file_path: str) -> tuple[pd.DataFrame, list]:
    """Loads data and engineers features using the optimal V4 feature set."""
    if not os.path.exists(file_path):
        raise FileNotFoundError(f"Data file not found at {file_path}")
    
    df = pd.read_parquet(file_path)
    if isinstance(df.columns, pd.MultiIndex):
        df.columns = df.columns.get_level_values(0)
    print("Data loaded successfully.")

    # Feature Engineering - Optimal V4 Feature Set
    df_features = df.copy()
    
    # Core price features
    df_features['log_return'] = np.log(df_features['Close'] / df_features['Close'].shift(1))
    df_features['log_hl_range'] = np.log(df_features['High'] / df_features['Low'])
    
    # Technical indicator - RSI with 14-period window
    delta = df_features['Close'].diff()
    gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
    rs = gain / loss
    df_features['rsi'] = 100 - (100 / (1 + rs))
    
    # Momentum features with optimal windows
    df_features['momentum_5m'] = np.log(df_features['Close'] / df_features['Close'].shift(5))
    df_features['momentum_20m'] = np.log(df_features['Close'] / df_features['Close'].shift(15))  # Optimal is 15
    
    # Advanced momentum ratio feature
    df_features['momentum_ratio'] = df_features['momentum_5m'] / (df_features['momentum_20m'] + 1e-8)
    
    # Final feature list
    features_list = ['log_return', 'log_hl_range', 'rsi', 'momentum_5m', 'momentum_20m', 'momentum_ratio']
    
    df_features.dropna(inplace=True)
    print(f"Features calculated: {', '.join(features_list)}")
    print(f"Data shape after feature engineering: {df_features.shape}")
    
    return df_features, features_list

def train_hmm_model(X_train: np.ndarray) -> GaussianHMM:
    """Trains HMM model using pre-optimized parameters from V4."""
    print("\n--- 1. Model Training: Using Optimized Parameters ---")
    
    # Optimal parameters from V4 analysis
    model = GaussianHMM(
        n_components=3,
        covariance_type="diag",
        n_iter=800,
        random_state=42,
        tol=1e-5
    )
    model.fit(X_train)
    
    print(f"HMM model trained with {model.n_components} components.")
    return model

def analyze_states(model: GaussianHMM, train_df: pd.DataFrame, features: list, scaler: StandardScaler):
    """Performs comprehensive state analysis and mapping."""
    print("\n--- 2. State Analysis & Regime Mapping ---")
    
    # Decode state means to original scale for interpretation
    state_means_original = scaler.inverse_transform(model.means_)
    state_means = pd.DataFrame(state_means_original, columns=features).sort_values(by='log_return')

    print("\n2.A. State Statistical Profiles (Original Scale):")
    print(state_means.round(6))

    # Map states based on log_return ordering
    bear_state = state_means.index[0]    # Lowest return
    neutral_state = state_means.index[1]  # Middle return  
    bull_state = state_means.index[2]     # Highest return
    
    state_map = {
        bull_state: "Bull",
        neutral_state: "Neutral", 
        bear_state: "Bear"
    }
    
    print(f"\nRegime Mapping -> Bull: State {bull_state}, Neutral: State {neutral_state}, Bear: State {bear_state}")

    # Transition matrix analysis
    print("\n2.B. State Transition Analysis:")
    transition_df = pd.DataFrame(model.transmat_, 
                               columns=[f'To {state_map.get(i, "N/A")}' for i in range(model.n_components)], 
                               index=[f'From {state_map.get(i, "N/A")}' for i in range(model.n_components)])
    print(transition_df.round(3))
    
    # Expected state durations
    expected_durations = 1 / (1 - np.diag(model.transmat_))
    print("\nExpected State Durations (minutes):")
    for i, duration in enumerate(expected_durations):
        print(f"  State {i} ({state_map.get(i, 'Neutral')}): {duration:.1f} minutes")

    # Volatility prediction validation
    train_df_copy = train_df.copy()
    train_df_copy['hidden_state'] = model.predict(scaler.transform(train_df_copy[features].values))
    train_df_copy['next_period_vol'] = train_df_copy['log_return'].shift(-1).abs()
    vol_prediction = train_df_copy.groupby('hidden_state')['next_period_vol'].mean().sort_values(ascending=False)
    
    print("\n2.C. Volatility Prediction Validation:")
    print(vol_prediction.round(6))
    
    if vol_prediction.index[0] == bear_state:
        print("✓ Bear state correctly predicts higher future volatility.")
    else:
        print("⚠ Bear state does not predict higher future volatility.")
        
    return state_map

def run_backtest_and_visualize(df: pd.DataFrame, model: GaussianHMM, state_map: dict, 
                              features: list, scaler: StandardScaler):
    """Executes backtest and creates comprehensive visualization."""
    print("\n--- 3. Backtest & Performance Analysis ---")
    
    # Generate signals and returns
    df_test = df.copy()
    X_test = scaler.transform(df_test[features])
    df_test['hidden_state'] = model.predict(X_test)
    df_test['regime'] = df_test['hidden_state'].map(state_map).fillna("Neutral")
    df_test['signal'] = df_test['regime'].map({"Bull": 1, "Bear": -1, "Neutral": 0}).shift(1).fillna(0)
    df_test['strategy_return'] = df_test['log_return'] * df_test['signal']
    df_test['bnh_return'] = df_test['log_return']

    # Performance metrics calculation
    strategy_total_return = np.exp(df_test['strategy_return'].sum()) - 1
    bnh_total_return = np.exp(df_test['bnh_return'].sum()) - 1
    strategy_sharpe = (df_test['strategy_return'].mean() / df_test['strategy_return'].std() 
                     * np.sqrt(252*24*60) if df_test['strategy_return'].std() > 0 else 0)
    bnh_sharpe = (df_test['bnh_return'].mean() / df_test['bnh_return'].std() 
                 * np.sqrt(252*24*60) if df_test['bnh_return'].std() > 0 else 0)
    
    # Additional risk metrics
    cumulative_strategy = np.exp(df_test['strategy_return'].cumsum())
    running_max = cumulative_strategy.expanding().max()
    drawdown = (cumulative_strategy - running_max) / running_max
    max_drawdown = drawdown.min()
    win_rate = (df_test['strategy_return'] > 0).sum() / len(df_test['strategy_return'])
    
    print(f"\nHMM Strategy v3 Performance Summary:")
    print(f"Strategy Total Return: {strategy_total_return:.2%}")
    print(f"Buy & Hold Total Return: {bnh_total_return:.2%}")
    print(f"Strategy Sharpe Ratio: {strategy_sharpe:.3f}")
    print(f"Buy & Hold Sharpe Ratio: {bnh_sharpe:.3f}")
    print(f"Outperformance: {strategy_total_return - bnh_total_return:.2%}")
    print(f"Maximum Drawdown: {max_drawdown:.2%}")
    print(f"Win Rate: {win_rate:.1%}")

    # --- Visualization ---
    fig, axes = plt.subplots(2, 2, figsize=(18, 12), gridspec_kw={'height_ratios': [3, 2]})
    fig.suptitle('HMM Strategy v3: 代码结构优化 + 特征标准化', fontsize=16, fontweight='bold')
    plt.style.use('seaborn-v0_8-darkgrid')

    # Time axis formatting
    x_axis = np.arange(len(df_test.index))
    num_ticks = 6
    tick_indices = np.linspace(0, len(x_axis) - 1, num_ticks, dtype=int)
    tick_labels = [df_test.index[i].strftime('%Y-%m-%d %H:%M') for i in tick_indices]

    # 1. Cumulative Returns
    ax1 = axes[0, 0]
    ax1.plot(x_axis, np.exp(df_test['strategy_return'].cumsum()), label='HMM Strategy v3', 
             linewidth=2, color='darkblue')
    ax1.plot(x_axis, np.exp(df_test['bnh_return'].cumsum()), label='Buy & Hold', 
             linestyle='--', alpha=0.7, color='gray')
    ax1.set_title('1. Strategy Performance', fontsize=12, fontweight='bold')
    ax1.set_ylabel('Cumulative Return')
    ax1.legend()
    ax1.grid(True)
    ax1.set_xticks(tick_indices)
    ax1.set_xticklabels(tick_labels, rotation=15, ha='right')

    # 2. Price with Regime Coloring
    ax2 = axes[0, 1]
    ax2.plot(x_axis, df_test['Close'], color='black', label='Gold Price', lw=1.0)
    
    # Regime background coloring
    state_colors = {'Bull': 'lightgreen', 'Bear': 'lightcoral', 'Neutral': 'lightyellow'}
    for regime, color in state_colors.items():
        state_mask = (df_test['regime'] == regime)
        ax2.fill_between(x_axis, df_test['Close'].min()*0.99, df_test['Close'].max()*1.01, 
                        where=state_mask, facecolor=color, alpha=0.4)

    ax2.set_ylim(df_test['Close'].min() * 0.99, df_test['Close'].max() * 1.01)
    ax2.set_title('2. Price Action & Market Regimes', fontsize=12, fontweight='bold')
    
    # Legend for regimes
    legend_elements = [Patch(color='black', label='Gold Price')]
    for regime, color in state_colors.items():
        legend_elements.append(Patch(facecolor=color, alpha=0.4, label=f'{regime} Regime'))
    ax2.legend(handles=legend_elements)
    ax2.grid(True)
    ax2.set_xticks(tick_indices)
    ax2.set_xticklabels(tick_labels, rotation=15, ha='right')

    # 3. Regime Timeline
    ax3 = axes[1, 0]
    ax3.plot(x_axis, df_test['hidden_state'], drawstyle='steps-post', linewidth=1.5, color='purple')
    ax3.set_title('3. Hidden State Timeline', fontsize=12, fontweight='bold')
    ax3.set_yticks(range(model.n_components))
    ax3.set_yticklabels([f"State {i} ({state_map.get(i, 'Unknown')})" for i in range(model.n_components)])
    ax3.grid(True)
    ax3.set_ylim(-0.5, model.n_components - 0.5)
    ax3.set_xticks(tick_indices)
    ax3.set_xticklabels(tick_labels, rotation=15, ha='right')

    # 4. Performance Metrics Table
    ax4 = axes[1, 1]
    ax4.axis('off')
    summary_data = [
        ['Strategy Total Return', f"{strategy_total_return:.2%}"],
        ['Buy & Hold Return', f"{bnh_total_return:.2%}"],
        ['Strategy Sharpe Ratio', f"{strategy_sharpe:.3f}"],
        ['Maximum Drawdown', f"{max_drawdown:.2%}"],
        ['Win Rate', f"{win_rate:.1%}"],
        ['Outperformance', f"{strategy_total_return - bnh_total_return:.2%}"]
    ]
    table = ax4.table(cellText=summary_data, colLabels=["Metric", "Value"], 
                     loc='center', cellLoc='left')
    table.auto_set_font_size(False)
    table.set_fontsize(12)
    table.scale(1, 1.8)
    ax4.set_title('4. Performance Metrics', fontsize=12, fontweight='bold', y=0.9)

    plt.tight_layout(rect=[0, 0, 1, 0.96])
    plt.show()
    
    return {
        'strategy_return': strategy_total_return,
        'bnh_return': bnh_total_return,
        'strategy_sharpe': strategy_sharpe,
        'bnh_sharpe': bnh_sharpe,
        'outperformance': strategy_total_return - bnh_total_return,
        'max_drawdown': max_drawdown,
        'win_rate': win_rate
    }

def main():
    """Main function orchestrating the complete HMM strategy workflow."""
    print("HMM Strategy v3: 代码结构优化")
    print("=" * 50)
    print("Combining V4's optimal logic with V1's clean style")
    print("=" * 50)
    
    try:
        # Configuration
        file_path = os.path.join("data/gold", "1m.parquet")
        TRAIN_TEST_SPLIT_DATE = pd.to_datetime("2025-08-16", utc=True)
        
        # Data loading and feature engineering
        featured_df, features_list = load_and_prepare_data(file_path)
        
        # Train/test split
        train_data = featured_df[featured_df.index < TRAIN_TEST_SPLIT_DATE].copy()
        test_data = featured_df[featured_df.index >= TRAIN_TEST_SPLIT_DATE].copy()
        
        if train_data.empty or test_data.empty: 
            raise ValueError("Insufficient data for train/test split.")
        
        print(f"\nData Split Summary:")
        print(f"Training data: {train_data.shape} (up to {TRAIN_TEST_SPLIT_DATE.strftime('%Y-%m-%d')})")
        print(f"Testing data:  {test_data.shape} (from {TRAIN_TEST_SPLIT_DATE.strftime('%Y-%m-%d')})")
        
        # Feature scaling
        print("\nApplying feature standardization...")
        scaler = StandardScaler()
        X_train = scaler.fit_transform(train_data[features_list])
        
        # Model training and evaluation
        model = train_hmm_model(X_train)
        state_map = analyze_states(model, train_data, features_list, scaler)
        results = run_backtest_and_visualize(test_data, model, state_map, features_list, scaler)
        
        # Final summary
        print("\n--- v3 Execution Summary ---")
        if results['strategy_return'] > results['bnh_return']:
            print(f"✓ Strategy successful! Return: {results['strategy_return']:.2%}, "
                  f"outperforming buy-and-hold.")
        else:
            print(f"⚠ Strategy underperformed. Return: {results['strategy_return']:.2%}, "
                  f"below buy-and-hold.")
        
        print(f"Sharpe Ratio: {results['strategy_sharpe']:.3f}")
        print(f"Maximum Drawdown: {results['max_drawdown']:.2%}")
        print("\nv3 execution completed successfully.")

        return results

    except Exception as e:
        print(f"\nExecution failed: {e}")
        return None

if __name__ == "__main__":
    results_v5 = main() 