import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from hmmlearn.hmm import GaussianHMM
from sklearn.preprocessing import StandardScaler
import os
from itertools import product
import warnings
warnings.filterwarnings('ignore')

# 设置matplotlib支持中文显示
plt.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def load_and_prepare_data_ma(file_path: str, ma_fast: int, ma_slow: int) -> tuple[pd.DataFrame, list]:
    """加载数据并使用指定MA参数进行特征工程"""
    if not os.path.exists(file_path):
        raise FileNotFoundError(f"数据文件未找到: {file_path}")
    
    df = pd.read_parquet(file_path)
    if isinstance(df.columns, pd.MultiIndex):
        df.columns = df.columns.get_level_values(0)
    
    # 基础特征
    df['log_return'] = np.log(df['Close'] / df['Close'].shift(1))
    df['momentum_5m'] = np.log(df['Close'] / df['Close'].shift(5))
    df['momentum_20m'] = np.log(df['Close'] / df['Close'].shift(20))
    
    # 使用自定义MA参数
    df['sma_fast'] = df['Close'].rolling(window=ma_fast).mean()
    df['sma_slow'] = df['Close'].rolling(window=ma_slow).mean()
    df['price_position'] = (df['Close'] - df['sma_slow']) / df['sma_slow']
    df['ma_diff'] = (df['sma_fast'] - df['sma_slow']) / df['sma_slow']
    
    features = ['log_return', 'momentum_5m', 'momentum_20m', 'price_position', 'ma_diff']
    
    df.dropna(inplace=True)
    return df, features

def train_hmm_model_quick(X_train: np.ndarray, n_states: int = 3):
    """快速训练HMM模型（优化版）"""
    scaler = StandardScaler()
    X_train_scaled = scaler.fit_transform(X_train)
    
    try:
        model = GaussianHMM(
            n_components=n_states,
            covariance_type="diag",
            n_iter=300,  # 减少迭代次数加快实验
            random_state=42,
            tol=1e-3
        )
        model.fit(X_train_scaled)
        return model, scaler, True
    except:
        return None, None, False

def identify_regime_states_quick(model: GaussianHMM, features: list) -> dict:
    """快速状态识别"""
    state_means = pd.DataFrame(model.means_, columns=features)
    
    # 按收益率排序识别状态
    sorted_by_return = state_means.sort_values('log_return')
    
    bear_state = sorted_by_return.index[0]
    bull_state = sorted_by_return.index[-1]
    neutral_state = sorted_by_return.index[1]
    
    return {
        bear_state: "下跌",
        bull_state: "上涨",
        neutral_state: "盘整"
    }

def evaluate_ma_strategy(df: pd.DataFrame, ma_fast: int, ma_slow: int, split_date: pd.Timestamp):
    """评估特定MA参数组合的策略表现"""
    try:
        # 准备数据
        featured_df, features = load_and_prepare_data_ma("data/GC_1m_last_month_20250726.parquet", ma_fast, ma_slow)
        
        if len(featured_df) < 1000:  # 数据太少
            return None
        
        train_data = featured_df[featured_df.index < split_date].copy()
        test_data = featured_df[featured_df.index >= split_date].copy()
        
        if len(train_data) < 500 or len(test_data) < 100:
            return None
        
        # 训练模型
        model, scaler, success = train_hmm_model_quick(train_data[features].values)
        if not success:
            return None
        
        # 状态识别
        state_map = identify_regime_states_quick(model, features)
        
        # 预测和交易
        X_test_scaled = scaler.transform(test_data[features].values)
        test_data['state'] = model.predict(X_test_scaled)
        test_data['regime'] = test_data['state'].map(state_map)
        
        # 生成信号
        signal_map = {"上涨": 1, "下跌": -1, "盘整": 0}
        test_data['signal'] = test_data['regime'].map(signal_map).shift(1).fillna(0)
        
        # 计算收益
        test_data['strategy_return'] = test_data['log_return'] * test_data['signal']
        test_data['bnh_return'] = test_data['log_return']
        
        # 性能指标
        total_return = np.exp(test_data['strategy_return'].sum()) - 1
        bnh_return = np.exp(test_data['bnh_return'].sum()) - 1
        
        if test_data['strategy_return'].std() > 0:
            sharpe = test_data['strategy_return'].mean() / test_data['strategy_return'].std() * np.sqrt(252*24*60)
        else:
            sharpe = 0
            
        # 最大回撤
        cumulative = np.exp(test_data['strategy_return'].cumsum())
        running_max = cumulative.expanding().max()
        drawdown = (cumulative - running_max) / running_max
        max_drawdown = drawdown.min()
        
        # 胜率
        win_rate = (test_data['strategy_return'] > 0).sum() / len(test_data)
        
        # 交易统计
        state_changes = (test_data['regime'] != test_data['regime'].shift(1)).sum()
        trades = (test_data['signal'] != test_data['signal'].shift(1)).sum()
        
        return {
            'ma_fast': ma_fast,
            'ma_slow': ma_slow,
            'ma_ratio': ma_fast / ma_slow,
            'split_date': split_date.strftime('%Y-%m-%d'),
            'train_days': len(train_data) / (24 * 60),  # 转换为天数
            'test_days': len(test_data) / (24 * 60),
            'total_return': total_return,
            'bnh_return': bnh_return,
            'excess_return': total_return - bnh_return,
            'sharpe': sharpe,
            'max_drawdown': max_drawdown,
            'win_rate': win_rate,
            'state_changes': state_changes,
            'trades': trades,
            'data_points': len(test_data)
        }
        
    except Exception as e:
        print(f"MA({ma_fast},{ma_slow}) 在 {split_date.strftime('%Y-%m-%d')} 失败: {e}")
        return None

def run_ma_optimization_experiment_extended():
    """运行扩展的MA参数优化实验（包含时间段探索）"""
    print("=" * 80)
    print("🔬 扩展MA参数优化实验 - 多时间段对比")
    print("=" * 80)
    
    # 定义测试参数范围
    ma_fast_options = [5, 8, 10, 12, 15, 18, 20]  # 短期MA
    ma_slow_options = [20, 25, 30, 35, 40, 45, 50]  # 长期MA
    
    # 定义多个分割日期进行对比
    split_dates = [
        pd.to_datetime("2025-07-13", utc=True),  # 较早分割
        pd.to_datetime("2025-07-16", utc=True),  # 原始分割
        pd.to_datetime("2025-07-19", utc=True),  # 较晚分割
    ]
    
    all_results = []
    
    print(f"测试范围: 短期MA {ma_fast_options}, 长期MA {ma_slow_options}")
    print(f"时间分割点: {[d.strftime('%Y-%m-%d') for d in split_dates]}")
    print(f"总共需要测试: {len(ma_fast_options) * len(ma_slow_options) * len(split_dates)} 种组合")
    print()
    
    # 遍历所有时间段
    for split_idx, split_date in enumerate(split_dates):
        print(f"\n📅 测试时间分割: {split_date.strftime('%Y-%m-%d')} ({split_idx+1}/{len(split_dates)})")
        print("-" * 60)
        
        results = []
        total_tests = 0
        
        # 遍历所有MA组合
        for ma_fast, ma_slow in product(ma_fast_options, ma_slow_options):
            if ma_fast >= ma_slow:  # 跳过快线大于等于慢线的情况
                continue
                
            total_tests += 1
            if total_tests % 5 == 0:  # 每5个组合打印一次进度
                print(f"进度: {total_tests}/{len([1 for f,s in product(ma_fast_options, ma_slow_options) if f < s])} 组合...")
            
            result = evaluate_ma_strategy(None, ma_fast, ma_slow, split_date)
            
            if result is not None:
                results.append(result)
                all_results.append(result)
        
        print(f"✅ 分割点 {split_date.strftime('%Y-%m-%d')} 完成: {len(results)}/{total_tests} 个组合成功")
    
    print(f"\n🎉 全部测试完成: 总共 {len(all_results)} 个成功组合")
    
    if not all_results:
        print("❌ 没有成功的测试结果")
        return None
    
    # 转换为DataFrame进行分析
    df_all_results = pd.DataFrame(all_results)
    
    return df_all_results

def analyze_time_split_comparison(df_results: pd.DataFrame):
    """分析不同时间分割的效果对比"""
    print("\n" + "=" * 80)
    print("📊 时间分割效果对比分析")
    print("=" * 80)
    
    # 按时间分割分组分析
    split_summary = df_results.groupby('split_date').agg({
        'total_return': ['mean', 'std', 'max', 'min'],
        'sharpe': ['mean', 'std', 'max', 'min'],
        'max_drawdown': ['mean', 'std', 'max', 'min'],
        'win_rate': ['mean', 'std'],
        'train_days': 'mean',
        'test_days': 'mean'
    }).round(4)
    
    print("📈 各时间分割整体表现统计:")
    print(split_summary)
    print()
    
    # 找出每个时间分割的最佳组合
    print("🏆 各时间分割的最佳策略 (按夏普比率):")
    for split_date in df_results['split_date'].unique():
        subset = df_results[df_results['split_date'] == split_date]
        best = subset.loc[subset['sharpe'].idxmax()]
        
        print(f"\n📅 {split_date}:")
        print(f"   最佳组合: MA({int(best['ma_fast'])}, {int(best['ma_slow'])})")
        print(f"   训练天数: {best['train_days']:.1f}, 测试天数: {best['test_days']:.1f}")
        print(f"   收益率: {best['total_return']:.2%}")
        print(f"   夏普比率: {best['sharpe']:.2f}")
        print(f"   最大回撤: {best['max_drawdown']:.2%}")
        print(f"   胜率: {best['win_rate']:.1%}")
    
    # 可视化对比
    fig, axes = plt.subplots(2, 3, figsize=(20, 12))
    fig.suptitle('多时间段MA参数优化对比分析', fontsize=16, fontweight='bold')
    
    # 1. 收益率对比
    ax1 = axes[0, 0]
    df_results.boxplot(column='total_return', by='split_date', ax=ax1)
    ax1.set_title('收益率分布对比')
    ax1.set_xlabel('时间分割点')
    ax1.set_ylabel('策略收益率')
    
    # 2. 夏普比率对比
    ax2 = axes[0, 1]
    df_results.boxplot(column='sharpe', by='split_date', ax=ax2)
    ax2.set_title('夏普比率分布对比')
    ax2.set_xlabel('时间分割点')
    ax2.set_ylabel('夏普比率')
    
    # 3. 最大回撤对比
    ax3 = axes[0, 2]
    df_results.boxplot(column='max_drawdown', by='split_date', ax=ax3)
    ax3.set_title('最大回撤分布对比')
    ax3.set_xlabel('时间分割点')
    ax3.set_ylabel('最大回撤')
    
    # 4. MA参数稳定性分析
    ax4 = axes[1, 0]
    for split_date in df_results['split_date'].unique():
        subset = df_results[df_results['split_date'] == split_date]
        top_10_subset = subset.nlargest(10, 'sharpe')
        ax4.scatter(top_10_subset['ma_fast'], top_10_subset['ma_slow'], 
                   label=f'{split_date}', alpha=0.7, s=50)
    ax4.set_xlabel('快线MA')
    ax4.set_ylabel('慢线MA')
    ax4.set_title('Top 10 策略的MA参数分布')
    ax4.legend()
    ax4.grid(True, alpha=0.3)
    
    # 5. 训练/测试数据量对比
    ax5 = axes[1, 1]
    split_dates = df_results['split_date'].unique()
    train_days = [df_results[df_results['split_date']==sd]['train_days'].iloc[0] for sd in split_dates]
    test_days = [df_results[df_results['split_date']==sd]['test_days'].iloc[0] for sd in split_dates]
    
    x = np.arange(len(split_dates))
    width = 0.35
    ax5.bar(x - width/2, train_days, width, label='训练天数', alpha=0.8)
    ax5.bar(x + width/2, test_days, width, label='测试天数', alpha=0.8)
    ax5.set_xlabel('时间分割点')
    ax5.set_ylabel('天数')
    ax5.set_title('训练/测试数据量对比')
    ax5.set_xticks(x)
    ax5.set_xticklabels(split_dates)
    ax5.legend()
    
    # 6. 相关性热力图
    ax6 = axes[1, 2]
    # 计算各时间段Top策略的相关性
    correlation_data = {}
    for split_date in df_results['split_date'].unique():
        subset = df_results[df_results['split_date'] == split_date]
        top_strategies = subset.nlargest(5, 'sharpe')
        correlation_data[f'{split_date}_返回'] = top_strategies['total_return'].values[:5]
        correlation_data[f'{split_date}_夏普'] = top_strategies['sharpe'].values[:5]
    
    # 简化的相关性分析 - 比较平均表现
    avg_metrics = {}
    for split_date in df_results['split_date'].unique():
        subset = df_results[df_results['split_date'] == split_date]
        avg_metrics[split_date] = {
            '平均收益': subset['total_return'].mean(),
            '平均夏普': subset['sharpe'].mean(),
            '平均回撤': subset['max_drawdown'].mean()
        }
    
    metric_df = pd.DataFrame(avg_metrics).T
    sns.heatmap(metric_df.corr(), annot=True, cmap='coolwarm', center=0, ax=ax6)
    ax6.set_title('时间段间指标相关性')
    
    plt.suptitle('')  # 移除自动生成的标题
    plt.tight_layout()
    
    # 保存图片
    timestamp = pd.Timestamp.now().strftime("%Y%m%d_%H%M%S")
    plt.savefig(f'ma_time_split_comparison_{timestamp}.png', dpi=300, bbox_inches='tight')
    print(f"\n📊 时间对比图表已保存: ma_time_split_comparison_{timestamp}.png")
    plt.show()

def get_cross_time_optimal_recommendations(df_results: pd.DataFrame):
    """给出跨时间段的最优MA参数建议"""
    print("\n" + "=" * 80)
    print("🎯 跨时间段最优参数稳健性分析")
    print("=" * 80)
    
    # 1. 找出在所有时间段都表现良好的参数
    print("🔍 寻找稳健策略 (在所有时间段都进入Top 30%)...")
    
    all_robust_strategies = []
    
    # 为每个MA组合计算跨时间段表现
    unique_ma_combinations = df_results[['ma_fast', 'ma_slow']].drop_duplicates()
    
    for _, ma_combo in unique_ma_combinations.iterrows():
        ma_fast, ma_slow = ma_combo['ma_fast'], ma_combo['ma_slow']
        
        # 获取该组合在所有时间段的表现
        combo_results = df_results[
            (df_results['ma_fast'] == ma_fast) & 
            (df_results['ma_slow'] == ma_slow)
        ]
        
        if len(combo_results) == 3:  # 确保在所有3个时间段都有结果
            # 计算在每个时间段的排名百分位
            percentiles = []
            performances = []
            
            for split_date in df_results['split_date'].unique():
                subset = df_results[df_results['split_date'] == split_date]
                combo_performance = combo_results[combo_results['split_date'] == split_date]
                
                if len(combo_performance) > 0:
                    rank = (subset['sharpe'] <= combo_performance['sharpe'].iloc[0]).sum()
                    percentile = rank / len(subset)
                    percentiles.append(percentile)
                    performances.append(combo_performance.iloc[0])
            
            # 如果在所有时间段都进入前30%，则认为是稳健策略
            if len(percentiles) == 3 and all(p >= 0.7 for p in percentiles):
                # 只选择数值字段进行计算
                numeric_cols = ['total_return', 'sharpe', 'max_drawdown', 'win_rate']
                perf_data = pd.DataFrame([p[numeric_cols] for p in performances])
                
                all_robust_strategies.append({
                    'ma_fast': ma_fast,
                    'ma_slow': ma_slow,
                    'min_percentile': min(percentiles),
                    'avg_return': perf_data['total_return'].mean(),
                    'avg_sharpe': perf_data['sharpe'].mean(),
                    'avg_drawdown': perf_data['max_drawdown'].mean(),
                    'std_return': perf_data['total_return'].std(),
                    'std_sharpe': perf_data['sharpe'].std()
                })
    
    if all_robust_strategies:
        df_robust = pd.DataFrame(all_robust_strategies)
        df_robust = df_robust.sort_values('avg_sharpe', ascending=False)
        
        print(f"✅ 发现 {len(df_robust)} 个稳健策略组合:")
        print(df_robust.head(10).to_string(index=False, float_format='%.3f'))
        print()
        
        # 最稳健策略推荐
        most_robust = df_robust.iloc[0]
        print(f"🏆 最稳健策略: MA({int(most_robust['ma_fast'])}, {int(most_robust['ma_slow'])})")
        print(f"   平均收益率: {most_robust['avg_return']:.2%}")
        print(f"   平均夏普比率: {most_robust['avg_sharpe']:.2f}")
        print(f"   平均最大回撤: {most_robust['avg_drawdown']:.2%}")
        print(f"   收益率标准差: {most_robust['std_return']:.3f}")
        print(f"   夏普比率标准差: {most_robust['std_sharpe']:.3f}")
    else:
        print("❌ 未发现在所有时间段都表现优秀的稳健策略")
    
    print("\n" + "-" * 60)
    
    # 2. 各时间段最优参数汇总
    print("📋 各时间段最优参数汇总:")
    best_by_time = {}
    
    for split_date in df_results['split_date'].unique():
        subset = df_results[df_results['split_date'] == split_date]
        best = subset.loc[subset['sharpe'].idxmax()]
        best_by_time[split_date] = (int(best['ma_fast']), int(best['ma_slow']))
        print(f"   {split_date}: MA({int(best['ma_fast'])}, {int(best['ma_slow'])}) - 夏普{best['sharpe']:.2f}")
    
    # 3. 参数稳定性分析
    print(f"\n🔬 参数稳定性分析:")
    all_fast = [combo[0] for combo in best_by_time.values()]
    all_slow = [combo[1] for combo in best_by_time.values()]
    
    print(f"   快线MA范围: {min(all_fast)} - {max(all_fast)} (标准差: {np.std(all_fast):.1f})")
    print(f"   慢线MA范围: {min(all_slow)} - {max(all_slow)} (标准差: {np.std(all_slow):.1f})")
    
    if len(set(best_by_time.values())) == 1:
        print("   ✅ 所有时间段最优参数完全一致！方法非常稳健。")
    elif len(set(best_by_time.values())) == len(best_by_time):
        print("   ⚠️  每个时间段最优参数都不同，需要谨慎选择。")
    else:
        print("   ✅ 部分时间段最优参数一致，方法具有一定稳健性。")
    
    print("\n💡 投资建议:")
    if all_robust_strategies:
        print("   - 推荐使用稳健策略，在多种市场环境下都有良好表现")
        print("   - 可以考虑动态调整策略，根据市场状态选择最优参数")
    print("   - 建议进行更长周期的回测验证")
    print("   - 考虑加入其他技术指标提升策略稳健性")

def main():
    """主函数"""
    try:
        # 运行扩展优化实验
        df_results = run_ma_optimization_experiment_extended()
        
        if df_results is None:
            return
        
        # 分析时间分割对比
        analyze_time_split_comparison(df_results)
        
        # 跨时间段稳健性分析
        get_cross_time_optimal_recommendations(df_results)
        
        # 保存详细结果
        timestamp = pd.Timestamp.now().strftime("%Y%m%d_%H%M%S")
        df_results.to_csv(f'ma_time_split_detailed_{timestamp}.csv', index=False)
        print(f"\n📄 详细结果已保存: ma_time_split_detailed_{timestamp}.csv")
        
        print("\n✅ 扩展MA参数优化实验完成！")
        
    except Exception as e:
        print(f"\n❌ 实验失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main() 