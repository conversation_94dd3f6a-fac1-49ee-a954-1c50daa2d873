import pandas as pd
import numpy as np
from lightweight_charts import Chart
from hmmlearn.hmm import GaussianHMM
from sklearn.preprocessing import StandardScaler
import os
from datetime import datetime, timezone
import time
import threading
from typing import Optional, Dict, List, Callable

class HMMRealtimeChart:
    """基于TradingView Lightweight Charts的实时HMM交易可视化系统"""
    
    def __init__(self, 
                 data_file: str = None,
                 n_states: int = 3,
                 ma_fast: int = 20,
                 ma_slow: int = 35,
                 width: int = 1400,
                 height: int = 800):
        """
        初始化实时HMM图表
        
        Args:
            data_file: 数据文件路径
            n_states: HMM状态数量
            ma_fast: 快速移动平均线周期
            ma_slow: 慢速移动平均线周期
            width: 图表宽度
            height: 图表高度
        """
        self.data_file = data_file or os.path.join("data/gold", "1m.parquet")
        self.n_states = n_states
        self.ma_fast = ma_fast
        self.ma_slow = ma_slow
        
        # 初始化图表
        self.chart = Chart(
            width=width,
            height=height,
            toolbox=True
        )
        
        # 配置图表样式
        self._setup_chart_style()
        
        # HMM相关
        self.model: Optional[GaussianHMM] = None
        self.scaler: Optional[StandardScaler] = None
        self.state_map: Dict[int, str] = {}
        self.features = ['log_return', 'momentum_5m', 'momentum_20m', 'price_position', 'ma_diff']
        
        # 数据存储
        self.df: Optional[pd.DataFrame] = None
        self.last_processed_index = 0
        
        # 状态颜色映射
        self.state_colors = {
            '上涨': '#00C851',    # 明亮绿色
            '下跌': '#ff4444',    # 明亮红色  
            '盘整': '#ffbb33'     # 橙黄色
        }
        
        self.signal_shapes = {
            '上涨': 'arrow_up',
            '下跌': 'arrow_down', 
            '盘整': 'circle'  # 圆形表示振荡
        }
        
        # 创建指标线
        self.ma_fast_line = self.chart.create_line(f'MA{ma_fast}', color='#00aaff', width=2)
        self.ma_slow_line = self.chart.create_line(f'MA{ma_slow}', color='#ff6600', width=2)
        
        # 回调函数
        self.on_signal_change: Optional[Callable] = None
        
        print(f"🚀 HMM实时图表初始化完成")
        print(f"📊 MA参数: 快线{ma_fast}, 慢线{ma_slow}")
        print(f"🎯 HMM状态数: {n_states}")
        
    def _setup_chart_style(self):
        """配置图表样式"""
        # 基础布局 - 白色主题
        self.chart.layout(
            background_color='#ffffff',
            text_color='#333333',
            font_size=12,
            font_family='Arial'
        )
        
        # K线样式 - 清晰的绿红配色
        self.chart.candle_style(
            up_color='#26a69a',
            down_color='#ef5350',
            border_up_color='#26a69a',
            border_down_color='#ef5350',
            wick_up_color='#26a69a',
            wick_down_color='#ef5350'
        )
        
        # 成交量样式
        self.chart.volume_config(
            up_color='rgba(38, 166, 154, 0.3)',
            down_color='rgba(239, 83, 80, 0.3)'
        )
        
        # 十字线
        self.chart.crosshair(
            mode='normal',
            vert_color='#cccccc',
            vert_style='dotted',
            horz_color='#cccccc',
            horz_style='dotted'
        )
        
        # 水印
        self.chart.watermark('HMM实时交易系统', color='rgba(200, 200, 200, 0.5)')
        
        # 图例
        self.chart.legend(visible=True, font_size=12)
        
        # 网格线
        self.chart.grid(vert_enabled=True, horz_enabled=True, color='#f0f0f0')
        
    def load_and_prepare_data(self) -> bool:
        """加载并准备数据"""
        try:
            if not os.path.exists(self.data_file):
                raise FileNotFoundError(f"数据文件未找到: {self.data_file}")
            
            # 加载数据
            df = pd.read_parquet(self.data_file)
            if isinstance(df.columns, pd.MultiIndex):
                df.columns = df.columns.get_level_values(0)
            
            # 特征工程
            df['log_return'] = np.log(df['Close'] / df['Close'].shift(1))
            df['momentum_5m'] = np.log(df['Close'] / df['Close'].shift(5))
            df['momentum_20m'] = np.log(df['Close'] / df['Close'].shift(20))
            
            # 移动平均线
            df['ma_fast'] = df['Close'].rolling(window=self.ma_fast).mean()
            df['ma_slow'] = df['Close'].rolling(window=self.ma_slow).mean()
            df['price_position'] = (df['Close'] - df['ma_slow']) / df['ma_slow']
            df['ma_diff'] = (df['ma_fast'] - df['ma_slow']) / df['ma_slow']
            
            # 清理数据
            df.dropna(inplace=True)
            
            # 时间格式转换（确保时区信息）
            if df.index.tz is None:
                df.index = df.index.tz_localize('UTC')
            
            self.df = df
            print(f"✅ 数据加载成功: {df.shape[0]} 条记录")
            print(f"📅 时间范围: {df.index[0]} ~ {df.index[-1]}")
            
            return True
            
        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            return False
    
    def train_hmm_model(self, train_ratio: float = 0.7) -> bool:
        """训练HMM模型"""
        if self.df is None:
            print("❌ 请先加载数据")
            return False
        
        try:
            # 分割数据
            split_idx = int(len(self.df) * train_ratio)
            train_data = self.df.iloc[:split_idx]
            
            # 准备训练特征
            X_train = train_data[self.features].values
            
            # 标准化
            self.scaler = StandardScaler()
            X_train_scaled = self.scaler.fit_transform(X_train)
            
            # 训练HMM
            self.model = GaussianHMM(
                n_components=self.n_states,
                covariance_type="diag",
                n_iter=500,
                random_state=42,
                tol=1e-4
            )
            self.model.fit(X_train_scaled)
            
            # 识别状态映射
            self._identify_state_mapping()
            
            print(f"✅ HMM模型训练完成")
            print(f"📊 BIC分数: {self.model.bic(X_train_scaled):.2f}")
            
            return True
            
        except Exception as e:
            print(f"❌ 模型训练失败: {e}")
            return False
    
    def _identify_state_mapping(self):
        """识别状态映射"""
        state_means = pd.DataFrame(self.model.means_, columns=self.features)
        
        # 按收益率排序识别状态
        sorted_by_return = state_means.sort_values('log_return')
        
        bear_state = sorted_by_return.index[0]
        bull_state = sorted_by_return.index[-1] 
        neutral_state = sorted_by_return.index[1] if self.n_states == 3 else None
        
        self.state_map = {
            bear_state: "下跌",
            bull_state: "上涨"
        }
        
        if neutral_state is not None:
            self.state_map[neutral_state] = "盘整"
        
        print("🎯 状态映射:")
        for state, regime in self.state_map.items():
            ret = state_means.loc[state, 'log_return']
            ma = state_means.loc[state, 'ma_diff']
            print(f"  状态{state} -> {regime} (收益:{ret:.4f}, 均线:{ma:.4f})")
    
    def predict_states(self, start_idx: int = 0) -> pd.DataFrame:
        """预测状态序列"""
        if self.model is None or self.scaler is None:
            raise ValueError("请先训练模型")
        
        # 预测状态
        data_subset = self.df.iloc[start_idx:]
        X = data_subset[self.features].values
        X_scaled = self.scaler.transform(X)
        
        states = self.model.predict(X_scaled)
        
        # 生成结果DataFrame
        result_df = data_subset.copy()
        result_df['state'] = states
        result_df['regime'] = result_df['state'].map(self.state_map)
        
        # 交易信号
        signal_map = {"上涨": 1, "下跌": -1, "盘整": 0}
        result_df['signal'] = result_df['regime'].map(signal_map).shift(1).fillna(0)
        
        return result_df
    
    def display_initial_data(self, days_back: int = 7):
        """显示初始数据"""
        if self.df is None:
            print("❌ 请先加载数据")
            return
        
        # 获取最近几天的数据
        recent_data = self.df.tail(days_back * 24 * 60)  # 假设1分钟数据
        
        # 预测状态
        pred_df = self.predict_states(len(self.df) - len(recent_data))
        pred_recent = pred_df.tail(len(recent_data))
        
        # 准备K线数据 - 转换为pandas DataFrame格式
        kline_df = pd.DataFrame({
            'time': pred_recent.index,
            'open': pred_recent['Open'],
            'high': pred_recent['High'],
            'low': pred_recent['Low'],
            'close': pred_recent['Close'],
            'volume': pred_recent['Volume'] if 'Volume' in pred_recent.columns else 1000
        })
        
        # 准备移动平均线数据
        ma_fast_df = pd.DataFrame({
            'time': pred_recent.index,
            f'MA{self.ma_fast}': pred_recent['ma_fast']
        }).dropna()
        
        ma_slow_df = pd.DataFrame({
            'time': pred_recent.index,
            f'MA{self.ma_slow}': pred_recent['ma_slow']
        }).dropna()
        
        # 设置数据
        self.chart.set(kline_df)
        if not ma_fast_df.empty:
            self.ma_fast_line.set(ma_fast_df)
        if not ma_slow_df.empty:
            self.ma_slow_line.set(ma_slow_df)
        
        # 添加状态变化标记
        self._add_regime_markers(pred_recent)
        
        self.last_processed_index = len(self.df)
        
        print(f"📊 初始数据显示完成，包含 {len(kline_df)} 根K线")
    
    def _add_regime_markers(self, df: pd.DataFrame):
        """添加状态变化标记"""
        regime_changes = df['regime'] != df['regime'].shift(1)
        signal_changes = df['signal'] != df['signal'].shift(1)
        
        for idx, row in df[signal_changes].iterrows():
            signal = row['signal']
            regime = row['regime']
            
            if signal == 1:  # 买入信号
                self.chart.marker(
                    time=idx,
                    position='below',
                    shape='arrow_up',
                    color=self.state_colors['上涨'],
                    text=f'买入'
                )
            elif signal == -1:  # 卖出信号
                self.chart.marker(
                    time=idx,
                    position='above', 
                    shape='arrow_down',
                    color=self.state_colors['下跌'],
                    text=f'卖出'
                )
            elif signal == 0 and regime == '盘整':  # 盘整信号
                self.chart.marker(
                    time=idx,
                    position='below',
                    shape='circle',
                    color=self.state_colors['盘整'],
                    text=f'盘整'
                )
    
    def simulate_realtime_update(self, update_interval: float = 1.0, max_updates: int = 100):
        """模拟实时数据更新"""
        if self.df is None or self.model is None:
            print("❌ 请先加载数据并训练模型")
            return
        
        print(f"🔄 开始模拟实时更新 (间隔: {update_interval}秒)")
        
        update_count = 0
        current_idx = self.last_processed_index
        
        while update_count < max_updates and current_idx < len(self.df):
            try:
                # 获取新数据点
                new_row = self.df.iloc[current_idx]
                
                # 预测状态
                if current_idx >= max(self.ma_slow, 20):  # 确保有足够历史数据
                    X_new = new_row[self.features].values.reshape(1, -1)
                    X_scaled = self.scaler.transform(X_new)
                    state = self.model.predict(X_scaled)[0]
                    regime = self.state_map[state]
                    
                    # 生成交易信号
                    signal_map = {"上涨": 1, "下跌": -1, "盘整": 0}
                    signal = signal_map[regime]
                    
                    # 更新图表 - 使用pandas Series格式
                    update_data = pd.Series({
                        'time': new_row.name,
                        'open': float(new_row['Open']),
                        'high': float(new_row['High']),
                        'low': float(new_row['Low']),
                        'close': float(new_row['Close']),
                        'volume': float(new_row['Volume']) if 'Volume' in new_row else 1000
                    })
                    
                    self.chart.update(update_data)
                    
                    # 更新移动平均线
                    if not pd.isna(new_row['ma_fast']):
                        ma_fast_update = pd.Series({
                            'time': new_row.name,
                            f'MA{self.ma_fast}': float(new_row['ma_fast'])
                        })
                        self.ma_fast_line.update(ma_fast_update)
                    
                    if not pd.isna(new_row['ma_slow']):
                        ma_slow_update = pd.Series({
                            'time': new_row.name,
                            f'MA{self.ma_slow}': float(new_row['ma_slow'])
                        })
                        self.ma_slow_line.update(ma_slow_update)
                    
                    # 检查信号变化
                    if hasattr(self, '_last_signal') and signal != self._last_signal:
                        if signal == 1:
                            self.chart.marker(
                                time=new_row.name,
                                position='below',
                                shape='arrow_up',
                                color=self.state_colors['上涨'],
                                text=f'买入信号'
                            )
                            print(f"🟢 {new_row.name}: 买入信号 ({regime})")
                        elif signal == -1:
                            self.chart.marker(
                                time=new_row.name,
                                position='above',
                                shape='arrow_down', 
                                color=self.state_colors['下跌'],
                                text=f'卖出信号'
                            )
                            print(f"🔴 {new_row.name}: 卖出信号 ({regime})")
                        elif signal == 0 and regime == '盘整':
                            self.chart.marker(
                                time=new_row.name,
                                position='below',
                                shape='circle',
                                color=self.state_colors['盘整'],
                                text=f'盘整信号'
                            )
                            print(f"🟡 {new_row.name}: 盘整信号 ({regime})")
                        
                        # 触发回调
                        if self.on_signal_change:
                            self.on_signal_change(signal, regime, new_row)
                    
                    self._last_signal = signal
                    
                    print(f"📊 {new_row.name}: 价格={new_row['Close']:.2f}, 状态={regime}, 信号={signal}")
                
                current_idx += 1
                update_count += 1
                time.sleep(update_interval)
                
            except Exception as e:
                print(f"❌ 更新失败: {e}")
                break
        
        print(f"✅ 实时更新完成，共处理 {update_count} 个数据点")
    
    def show(self, block: bool = True):
        """显示图表"""
        self.chart.show(block=block)
    
    def set_signal_callback(self, callback: Callable):
        """设置信号变化回调函数"""
        self.on_signal_change = callback

def demo_hmm_realtime():
    """演示HMM实时图表"""
    print("=" * 60)
    print("🚀 HMM实时交易可视化演示")
    print("📊 基于TradingView Lightweight Charts")
    print("=" * 60)
    
    # 创建图表
    chart = HMMRealtimeChart(
        ma_fast=20,
        ma_slow=35,
        n_states=3
    )
    
    # 设置信号回调
    def on_signal_change(signal, regime, data_row):
        action = "买入" if signal == 1 else "卖出" if signal == -1 else "盘整"
        print(f"🎯 交易信号变化: {action} | 状态: {regime} | 价格: {data_row['Close']:.2f}")
    
    chart.set_signal_callback(on_signal_change)
    
    # 加载数据
    if not chart.load_and_prepare_data():
        return
    
    # 训练模型
    if not chart.train_hmm_model(train_ratio=0.8):
        return
    
    # 显示初始数据
    chart.display_initial_data(days_back=3)
    
    # 启动图表
    print(f"\n📊 正在启动图表窗口...")
    print(f"💡 提示: 关闭图表窗口即可结束程序")
    
    try:
        # 在一个线程中运行模拟更新
        import threading
        def run_simulation():
            time.sleep(3)  # 等待图表完全加载
            chart.simulate_realtime_update(update_interval=0.5, max_updates=50)
        
        sim_thread = threading.Thread(target=run_simulation)
        sim_thread.daemon = True
        sim_thread.start()
        
        # 显示图表 (阻塞模式，直到窗口关闭)
        chart.show(block=True)
        
    except KeyboardInterrupt:
        print("\n\n用户中断程序")
    except Exception as e:
        print(f"\n\n程序执行异常: {e}")
    
    print("\n✅ 演示结束")

if __name__ == "__main__":
    demo_hmm_realtime() 