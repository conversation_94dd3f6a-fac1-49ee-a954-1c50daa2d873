#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
黄金HMM策略V8增强版 - 激进创新
=============================

基于SPY策略的突破性创新，应用到黄金分钟级交易：
1. 多时间框架融合 (1分钟+15分钟+1小时)
2. 动态状态数量选择 (2-4状态自适应)
3. 市场微观结构特征 (订单流、价差等)
4. 机器学习增强的信号过滤

目标：实现对V7的显著超越 (夏普比率 >15)
"""

import pandas as pd
import numpy as np
import yfinance as yf
from hmmlearn.hmm import GaussianHMM
from sklearn.preprocessing import StandardScaler
from sklearn.ensemble import RandomForestClassifier
import os
import warnings

warnings.filterwarnings('ignore')

def load_multi_timeframe_data():
    """加载多时间框架数据 - SPY策略经验"""
    print("📊 加载多时间框架黄金数据...")
    
    # 加载不同时间框架的数据
    data_files = {
        '1m': "/Users/<USER>/Documents/Git/quant-hmm/data/gold/1m.parquet",
        '15m': "/Users/<USER>/Documents/Git/quant-hmm/data/gold/15m.parquet", 
        '1h': "/Users/<USER>/Documents/Git/quant-hmm/data/gold/1h.parquet"
    }
    
    dfs = {}
    for timeframe, file_path in data_files.items():
        if os.path.exists(file_path):
            df = pd.read_parquet(file_path)
            if isinstance(df.columns, pd.MultiIndex):
                df.columns = df.columns.get_level_values(0)
            dfs[timeframe] = df
            print(f"✅ {timeframe}数据: {df.shape}")
    
    return dfs

def engineer_enhanced_features(dfs):
    """增强特征工程 - 多时间框架融合 (已修正前视偏差)"""
    print("🔧 构建增强特征...")
    
    df = dfs['1m'].copy()
    
    # 用于计算收益和标签的真实回报，不滞后
    df['log_return'] = np.log(df['Close'] / df['Close'].shift(1))

    # --- 所有特征都进行.shift(1)滞后处理 ---
    base_features = pd.DataFrame(index=df.index)
    
    # 1. V7核心特征 (滞后)
    base_features['sma_fast'] = df['Close'].rolling(window=20).mean()
    base_features['sma_slow'] = df['Close'].rolling(window=35).mean()
    base_features['ma_diff'] = (base_features['sma_fast'] - base_features['sma_slow']) / (base_features['sma_slow'] + 1e-8)
    base_features['price_position'] = (df['Close'] - base_features['sma_slow']) / (base_features['sma_slow'] + 1e-8)
    base_features['momentum_5m'] = np.log(df['Close'] / df['Close'].shift(5))
    
    # 2. 市场微观结构特征 (滞后)
    base_features['bid_ask_proxy'] = (df['High'] - df['Low']) / df['Close']
    base_features['volume_price_trend'] = df['Volume'] * np.sign(df['Close'].diff())
    base_features['intraday_momentum'] = (df['Close'] - df['Open']) / (df['Open'] + 1e-8)
    
    # 3. 波动率聚类特征 (滞后)
    log_return_shifted = df['log_return'].shift(1) # 确保波动率计算也基于历史
    base_features['volatility_1m'] = log_return_shifted.rolling(window=60).std()
    base_features['volatility_5m'] = log_return_shifted.rolling(window=300).std()
    base_features['vol_ratio'] = base_features['volatility_1m'] / (base_features['volatility_5m'] + 1e-8)
    
    # 4. 多时间框架趋势 (滞后)
    if '15m' in dfs and '1h' in dfs:
        df_15m = dfs['15m'].resample('15T').last()
        df_1h = dfs['1h'].resample('1H').last()
        
        df_15m['trend_15m'] = (df_15m['Close'] / df_15m['Close'].shift(4) - 1)
        base_features['trend_15m'] = df_15m['trend_15m'].reindex(df.index, method='ffill')
        
        df_1h['trend_1h'] = (df_1h['Close'] / df_1h['Close'].shift(6) - 1)
        base_features['trend_1h'] = df_1h['trend_1h'].reindex(df.index, method='ffill')
    else:
        base_features['trend_15m'] = 0
        base_features['trend_1h'] = 0

    # 统一应用 .shift(1)
    df_shifted_features = base_features.shift(1)
    
    # 将原始log_return合并回来
    df = df[['log_return']].join(df_shifted_features)

    # 5. 清理异常值
    feature_cols = [col for col in df.columns if col != 'log_return']
    
    for col in feature_cols:
        if col in df.columns:
            df[col] = df[col].replace([np.inf, -np.inf], np.nan)
            # 在dropna之前填充，避免丢失过多数据
            if df[col].isnull().any():
                df[col] = df[col].fillna(0)
            q99 = df[col].quantile(0.99)
            q01 = df[col].quantile(0.01)
            df[col] = df[col].clip(q01, q99)
    
    df.dropna(inplace=True)
    print(f"✅ 增强特征工程完成: {df.shape}")
    print(f"   特征数量: {len(feature_cols)}")
    
    return df, feature_cols

def adaptive_hmm_training(train_data, features_list):
    """自适应HMM训练 - (已修正状态映射前视偏差)"""
    print("🧠 自适应HMM训练...")
    
    scaler = StandardScaler()
    X_train = scaler.fit_transform(train_data[features_list])
    
    best_model, best_bic, best_n_states = None, np.inf, 3
    
    for n_states in [2, 3, 4]:
        try:
            model = GaussianHMM(n_components=n_states, covariance_type="diag", n_iter=500, random_state=42)
            model.fit(X_train)
            bic = model.bic(X_train)
            print(f"   {n_states}状态模型 BIC: {bic:.2f}")
            if bic < best_bic:
                best_bic, best_model, best_n_states = bic, model, n_states
        except Exception as e:
            print(f"   {n_states}状态模型训练失败: {e}")
    
    print(f"✅ 最优模型: {best_n_states}状态 (BIC: {best_bic:.2f})")
    
    # 状态映射 (无前视偏差)
    # 使用模型内在的均值来定义状态，假设第一个特征是收益率或其代表
    primary_feature_idx = 0 
    state_means = best_model.means_[:, primary_feature_idx]
    sorted_indices = np.argsort(state_means)

    state_map = {}
    if best_n_states == 2:
        state_map = {sorted_indices[0]: "下跌", sorted_indices[1]: "上涨"}
    elif best_n_states == 3:
        state_map = {sorted_indices[0]: "下跌", sorted_indices[1]: "盘整", sorted_indices[2]: "上涨"}
    else:  # 4状态
        state_map = {sorted_indices[0]: "强跌", sorted_indices[1]: "弱跌", sorted_indices[2]: "弱涨", sorted_indices[3]: "强涨"}
    
    print("\n状态映射 (无前视偏差):")
    for state_idx, name in state_map.items():
        print(f"  状态 {state_idx} -> {name}")

    return best_model, scaler, state_map, best_n_states

def ml_enhanced_signal_filter(test_results, train_data):
    """机器学习增强的信号过滤 - (已二次修正数据泄漏和过滤逻辑)"""
    print("🤖 应用ML信号过滤...")
    
    train_features = ['ma_diff', 'momentum_5m', 'vol_ratio', 'intraday_momentum']
    available_features = [f for f in train_features if f in train_data.columns]
    
    if len(available_features) < 3:
        print("   特征不足，跳过ML过滤")
        return test_results['base_signal']
    
    # 构建标签 (无数据泄漏): 用t时刻的真实收益方向作为标签，预测特征是t-1时刻的
    train_data['label'] = np.sign(train_data['log_return'])
    
    rf_data = train_data[available_features + ['label']].dropna()
    if rf_data['label'].nunique() < 2 or len(rf_data) < 1000:
        print("   标签数据不足或类别太少，跳过ML过滤")
        return test_results['base_signal']

    rf = RandomForestClassifier(n_estimators=50, random_state=42, max_depth=5, class_weight='balanced')
    rf.fit(rf_data[available_features], rf_data['label'])
    
    test_features = test_results[available_features].fillna(0)
    ml_signals = rf.predict(test_features)
    
    # 修正后的信号融合逻辑：仅当两个模型的预测方向一致时，才采用HMM的信号
    hmm_signs = np.sign(test_results['base_signal'])
    ml_signs = np.sign(ml_signals) # ML预测值已经是-1, 0, 1

    # 核心修正：比较符号，而不是值
    final_signals = np.where(hmm_signs == ml_signs, test_results['base_signal'], 0)
    
    print(f"✅ ML信号过滤完成")
    return final_signals


def run_v8_enhanced_backtest():
    """运行V8增强版回测"""
    print("\n🚀 开始V8增强版回测...")
    
    # 1. 加载多时间框架数据
    dfs = load_multi_timeframe_data()
    
    # 2. 增强特征工程
    df, features_list = engineer_enhanced_features(dfs)
    
    # 3. 训练测试分割 (与V7相同)
    TRAIN_TEST_SPLIT_DATE = pd.to_datetime("2025-08-16", utc=True)
    
    train_data = df[df.index < TRAIN_TEST_SPLIT_DATE].copy()
    test_data = df[df.index >= TRAIN_TEST_SPLIT_DATE].copy()
    
    print(f"训练数据: {train_data.shape}")
    print(f"测试数据: {test_data.shape}")
    
    # 4. 自适应HMM训练
    model, scaler, state_map, n_states = adaptive_hmm_training(train_data, features_list)
    
    # 5. 测试集预测
    X_test = scaler.transform(test_data[features_list])
    predicted_states = model.predict(X_test)
    
    test_results = test_data.copy()
    test_results['tactical_state'] = pd.Series(predicted_states, index=test_data.index).map(state_map)
    
    # 6. 基础信号生成
    if n_states == 2:
        signal_map = {"下跌": -1, "上涨": 1}
    elif n_states == 3:
        signal_map = {"下跌": -1, "盘整": 0, "上涨": 1}
    else:  # 4状态
        signal_map = {"强跌": -1.5, "弱跌": -0.5, "弱涨": 0.5, "强涨": 1.5}
    
    test_results['base_signal'] = test_results['tactical_state'].map(signal_map).fillna(0)
    
    # 7. ML增强信号过滤
    enhanced_signals = ml_enhanced_signal_filter(test_results, train_data)
    test_results['enhanced_signal'] = enhanced_signals
    
    # 8. 最终信号 (限制在±1)
    test_results['signal'] = np.clip(test_results['enhanced_signal'], -1, 1)
    test_results['signal'] = test_results['signal'].shift(1).fillna(0)
    
    # 9. 计算收益
    test_results['strategy_return'] = test_results['log_return'] * test_results['signal']
    test_results['benchmark_return'] = test_results['log_return']
    
    # 10. 性能指标 (已二次修正胜率计算)
    strategy_total_return = np.exp(test_results['strategy_return'].sum()) - 1
    benchmark_total_return = np.exp(test_results['benchmark_return'].sum()) - 1
    
    minutes_in_year = 252 * 23 * 60 
    strategy_sharpe = (test_results['strategy_return'].mean() / test_results['strategy_return'].std() 
                      * np.sqrt(minutes_in_year) if test_results['strategy_return'].std() > 0 else 0)
    
    # 修正胜率和交易次数的计算
    actual_trades = test_results[test_results['strategy_return'] != 0]
    total_trades = len(actual_trades)
    win_rate = (actual_trades['strategy_return'] > 0).sum() / total_trades if total_trades > 0 else 0
    
    # 最大回撤
    cumulative = np.exp(test_results['strategy_return'].cumsum())
    running_max = cumulative.expanding().max()
    drawdown = (cumulative - running_max) / running_max
    max_drawdown = drawdown.min()
    
    return {
        'strategy_total_return': strategy_total_return,
        'benchmark_total_return': benchmark_total_return,
        'strategy_sharpe': strategy_sharpe,
        'max_drawdown': max_drawdown,
        'win_rate': win_rate,
        'total_trades': total_trades,
        'n_states': n_states,
        'features_count': len(features_list)
    }

def main():
    """主函数 - V8增强版"""
    print("🚀 黄金HMM策略V8增强版 - 激进创新")
    print("="*60)
    print("突破性创新:")
    print("  🔄 多时间框架融合 (1m+15m+1h)")
    print("  🧠 自适应状态数量 (2-4状态)")
    print("  📈 市场微观结构特征")
    print("  🤖 ML增强信号过滤")
    print("="*60)
    
    try:
        # 运行V8增强版
        v8_performance = run_v8_enhanced_backtest()
        
        # V7基准
        v7_return = 0.0223
        v7_sharpe = 13.46
        v7_drawdown = -0.0077
        
        print(f"\n📊 V7 vs V8增强版性能对比:")
        print("="*70)
        print(f"{'指标':<20} | {'V7基准':<15} | {'V8增强':<15} | {'改进':<15}")
        print("-"*70)
        print(f"{'总收益率':<20} | {v7_return:14.2%} | {v8_performance['strategy_total_return']:14.2%} | {v8_performance['strategy_total_return']-v7_return:14.2%}")
        print(f"{'夏普比率':<20} | {v7_sharpe:14.2f} | {v8_performance['strategy_sharpe']:14.2f} | {v8_performance['strategy_sharpe']-v7_sharpe:14.2f}")
        print(f"{'最大回撤':<20} | {v7_drawdown:14.2%} | {v8_performance['max_drawdown']:14.2%} | {v8_performance['max_drawdown']-v7_drawdown:14.2%}")
        print(f"{'胜率':<20} | {'~50%':<15} | {v8_performance['win_rate']:14.2%} | {'N/A':<15}")
        print("="*70)
        
        print(f"\n🎯 V8增强版技术指标:")
        print(f"   自适应状态数: {v8_performance['n_states']}")
        print(f"   特征维度: {v8_performance['features_count']}")
        print(f"   总交易次数: {v8_performance['total_trades']}")
        
        # 最终评估
        if v8_performance['strategy_sharpe'] > v7_sharpe:
            improvement = v8_performance['strategy_sharpe'] - v7_sharpe
            print(f"\n🏆 BREAKTHROUGH! V8增强版成功超越V7!")
            print(f"   夏普比率提升: +{improvement:.2f}")
            print(f"   多时间框架+ML策略生效!")
        elif v8_performance['strategy_sharpe'] > v7_sharpe * 0.8:
            print(f"\n📊 V8增强版表现良好:")
            print(f"   夏普比率: {v8_performance['strategy_sharpe']:.2f}")
            print(f"   创新方向正确，需要进一步调优")
        else:
            print(f"\n⚠️ V8增强版需要重新设计:")
            print(f"   当前夏普: {v8_performance['strategy_sharpe']:.2f}")
            print(f"   复杂度可能过高，建议回归简单策略")
        
        print(f"\n✅ V8增强版测试完成!")
        
    except Exception as e:
        print(f"❌ V8增强版失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
