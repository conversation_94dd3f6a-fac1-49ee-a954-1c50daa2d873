import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from hmmlearn.hmm import GaussianHMM
from sklearn.preprocessing import StandardScaler
import os
import warnings
from matplotlib.patches import Patch

warnings.filterwarnings('ignore')
plt.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

# --- v4: 混合策略方法 (V5特征 + Regime状态逻辑) ---

def load_and_prepare_data(file_path: str) -> tuple[pd.DataFrame, list]:
    """Loads data and engineers features using the optimal V5 feature set."""
    if not os.path.exists(file_path):
        raise FileNotFoundError(f"Data file not found at {file_path}")
    
    df = pd.read_parquet(file_path)
    if isinstance(df.columns, pd.MultiIndex):
        df.columns = df.columns.get_level_values(0)
    print("Data loaded successfully.")

    # Feature Engineering - Optimal V5 Feature Set
    df_features = df.copy()
    
    df_features['log_return'] = np.log(df_features['Close'] / df_features['Close'].shift(1))
    df_features['log_hl_range'] = np.log(df_features['High'] / df_features['Low'])
    
    delta = df_features['Close'].diff()
    gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
    rs = gain / loss
    df_features['rsi'] = 100 - (100 / (1 + rs))
    
    df_features['momentum_5m'] = np.log(df_features['Close'] / df_features['Close'].shift(5))
    df_features['momentum_20m'] = np.log(df_features['Close'] / df_features['Close'].shift(15))
    df_features['momentum_ratio'] = df_features['momentum_5m'] / (df_features['momentum_20m'] + 1e-8)
    
    # Add features needed for Regime's logic
    df_features['sma_fast'] = df_features['Close'].rolling(window=10).mean()
    df_features['sma_slow'] = df_features['Close'].rolling(window=30).mean()
    df_features['ma_diff'] = (df_features['sma_fast'] - df_features['sma_slow']) / df_features['sma_slow']

    features_list = ['log_return', 'log_hl_range', 'rsi', 'momentum_5m', 'momentum_20m', 'momentum_ratio']
    
    df_features.dropna(inplace=True)
    print(f"Features calculated: {len(features_list)} features from V5")
    
    return df_features, features_list

def train_hmm_model(X_train: np.ndarray) -> tuple[GaussianHMM, StandardScaler]:
    """Trains HMM model using pre-optimized parameters from V5."""
    print("\n--- 1. Model Training: Using V5 Optimized Parameters (3 States) ---")
    
    scaler = StandardScaler()
    X_train_scaled = scaler.fit_transform(X_train)

    model = GaussianHMM(
        n_components=3,
        covariance_type="diag",
        n_iter=800,
        random_state=42,
        tol=1e-5
    )
    model.fit(X_train_scaled)
    
    print(f"HMM model trained with {model.n_components} components.")
    return model, scaler

def analyze_states_regime_logic(model: GaussianHMM, train_df: pd.DataFrame, features: list, scaler: StandardScaler) -> dict:
    """Analyzes states using the robust logic from the 'Regime' version."""
    print("\n--- 2. State Analysis using Regime's Logic ---")
    
    # Create a temporary dataframe with all necessary features for analysis
    train_df_analysis = train_df.copy()
    X_train_scaled = scaler.transform(train_df_analysis[features])
    train_df_analysis['hidden_state'] = model.predict(X_train_scaled)

    # Group by state to find the mean of the underlying features
    state_means = train_df_analysis.groupby('hidden_state')[['log_return', 'ma_diff']].mean()
    print("\n2.A. State Mean Characteristics (log_return & ma_diff):")
    print(state_means.round(6))

    # Regime mapping based on a combination of return and trend
    sorted_by_return = state_means.sort_values('log_return')
    
    bear_state = sorted_by_return.index[0]
    bull_state = sorted_by_return.index[-1]
    neutral_state = sorted_by_return.index[1]

    state_map = {
        bull_state: "Bull",
        neutral_state: "Neutral",
        bear_state: "Bear"
    }

    # Verification
    bull_ma_diff = state_means.loc[bull_state, 'ma_diff']
    bear_ma_diff = state_means.loc[bear_state, 'ma_diff']
    print(f"\nBull State ({bull_state}) ma_diff: {bull_ma_diff:.6f}")
    print(f"Bear State ({bear_state}) ma_diff: {bear_ma_diff:.6f}")
    if bull_ma_diff > 0 and bear_ma_diff < 0:
        print("✓ Logic Validated: Bull state has positive trend, Bear state has negative trend.")
    else:
        print("⚠️ Logic Warning: State characteristics do not align with trend expectations.")

    print(f"\nRegime Mapping -> Bull: {bull_state}, Neutral: {neutral_state}, Bear: {bear_state}")
    return state_map

def run_backtest_and_visualize(df: pd.DataFrame, model: GaussianHMM, state_map: dict, 
                              features: list, scaler: StandardScaler):
    """Executes backtest and creates visualization."""
    print("\n--- 3. Backtest & Performance Analysis ---")
    
    df_test = df.copy()
    X_test = scaler.transform(df_test[features])
    df_test['hidden_state'] = model.predict(X_test)
    df_test['regime'] = df_test['hidden_state'].map(state_map).fillna("Neutral")
    df_test['signal'] = df_test['regime'].map({"Bull": 1, "Bear": -1, "Neutral": 0}).shift(1).fillna(0)
    df_test['strategy_return'] = df_test['log_return'] * df_test['signal']
    df_test['bnh_return'] = df_test['log_return']

    # Performance metrics
    strategy_total_return = np.exp(df_test['strategy_return'].sum()) - 1
    strategy_sharpe = (df_test['strategy_return'].mean() / df_test['strategy_return'].std() 
                     * np.sqrt(252*24*60) if df_test['strategy_return'].std() > 0 else 0)
    
    print(f"\nHMM Strategy v4 Performance Summary:")
    print(f"Strategy Total Return: {strategy_total_return:.2%}")
    print(f"Strategy Sharpe Ratio: {strategy_sharpe:.3f}")

    # Visualization
    plt.figure(figsize=(15, 7))
    plt.plot(np.exp(df_test['strategy_return'].cumsum()), label='HMM Strategy v4', linewidth=2)
    plt.plot(np.exp(df_test['bnh_return'].cumsum()), label='Buy & Hold', linestyle='--', alpha=0.7)
    plt.axhline(y=1.117, color='gold', linestyle=':', label='V5 Baseline (11.70%)')
    plt.title('HMM Strategy v4: 混合策略方法 (V5特征 + Regime逻辑)')
    plt.legend()
    plt.grid(True)
    plt.show()

    return {'strategy_sharpe': strategy_sharpe}

def main():
    """Main function for the v4 Hybrid strategy."""
    print("HMM Strategy v4: 混合策略方法")
    print("=" * 50)
    
    try:
        file_path = os.path.join("data/gold", "1m.parquet")
        TRAIN_TEST_SPLIT_DATE = pd.to_datetime("2025-08-16", utc=True)
        
        featured_df, features_list = load_and_prepare_data(file_path)
        
        train_data = featured_df[featured_df.index < TRAIN_TEST_SPLIT_DATE].copy()
        test_data = featured_df[featured_df.index >= TRAIN_TEST_SPLIT_DATE].copy()
        
        model, scaler = train_hmm_model(train_data[features_list].values)
        
        # Use the regime logic for state mapping, passing the full train_data df
        state_map = analyze_states_regime_logic(model, train_data, features_list, scaler)
        
        results = run_backtest_and_visualize(test_data, model, state_map, features_list, scaler)
        
        print("\n--- v4 Execution Summary ---")
        v5_sharpe = 32.285
        if results['strategy_sharpe'] > v5_sharpe:
            print(f"🏆 SUCCESS! New model outperforms V5. Sharpe: {results['strategy_sharpe']:.3f} > {v5_sharpe}")
        else:
            print(f"ℹ️ INFO: New model did not outperform V5. Sharpe: {results['strategy_sharpe']:.3f} <= {v5_sharpe}")

    except Exception as e:
        print(f"\nExecution failed: {e}")

if __name__ == "__main__":
    main()
