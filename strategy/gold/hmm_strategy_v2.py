import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from hmmlearn.hmm import GaussianHMM
from sklearn.preprocessing import StandardScaler
import os
from matplotlib.patches import Patch

# --- Core Functions ---

def load_and_prepare_data(file_path: str) -> pd.DataFrame:
    """Loads, flattens columns, and engineers features for the HMM model."""
    if not os.path.exists(file_path):
        raise FileNotFoundError(f"Data file not found at {file_path}")
    
    df = pd.read_parquet(file_path)
    if isinstance(df.columns, pd.MultiIndex):
        df.columns = df.columns.get_level_values(0)

    print("Data loaded successfully.")
    
    # Feature Engineering - V8 Enhanced Version (V7.1最优特征组合)
    # 1. V1核心特征（保持不变）
    df['log_return'] = np.log(df['Close'] / df['Close'].shift(1))
    df['log_hl_range'] = np.log(df['High'] / df['Low'])  # V1超级特征
    
    # 2. V1 RSI（优化窗口：14）
    delta = df['Close'].diff()
    gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
    rs = gain / loss
    df['rsi'] = 100 - (100 / (1 + rs))
    
    # 3. V7突破性动量特征（最优窗口：5分钟和15分钟）
    df['momentum_5m'] = np.log(df['Close'] / df['Close'].shift(5))
    df['momentum_20m'] = np.log(df['Close'] / df['Close'].shift(15))  # 注意：V7最优是15而非20
    
    # 4. V7核心创新：动量比率特征
    df['momentum_ratio'] = df['momentum_5m'] / (df['momentum_20m'] + 1e-8)
    
    df.dropna(inplace=True)
    print("Features calculated: log_return, log_hl_range, rsi, momentum_5m, momentum_20m, momentum_ratio")
    print(f"Data shape after feature engineering: {df.shape}")
    
    return df

def train_hmm_model(X_train: np.ndarray, n_states_range: range = range(2, 5)):
    """Finds the best HMM model using BIC with V8 optimized parameters."""
    print("\n--- 1. Quantitative Evaluation: Finding Optimal Number of States (BIC) ---")
    best_model, best_bic = None, np.inf

    # 使用StandardScaler进行特征标准化（V1成功经验）
    scaler = StandardScaler()
    X_train_scaled = scaler.fit_transform(X_train)

    for n_components in n_states_range:
        try:
            # V8最优参数配置
            model = GaussianHMM(
                n_components=n_components,
                covariance_type="diag",  # V7发现：diag比full更适合6特征
                n_iter=800,             # V7优化：适度降低迭代次数
                random_state=42,
                tol=1e-5               # V7优化：更严格的收敛标准
            )
            model.fit(X_train_scaled)
            bic = model.bic(X_train_scaled)
            print(f"N_components={n_components}, BIC={bic:.2f}")
            if bic < best_bic:
                best_bic, best_model = bic, model
        except Exception as e:
            print(f"Error fitting model with {n_components} components: {e}")

    if best_model is None:
        raise RuntimeError("Failed to train any HMM model.")
    print(f"\nBest model found with {best_model.n_components} components.")
    return best_model, scaler

def perform_full_evaluation(model: GaussianHMM, train_df: pd.DataFrame, features: list, scaler):
    """Performs a comprehensive qualitative and quantitative evaluation of the HMM model."""
    
    print("\n--- 2. Qualitative & Quantitative Evaluation ---")
    
    # 使用标准化数据进行预测
    X_train_scaled = scaler.transform(train_df[features].values)
    
    state_means = pd.DataFrame(model.means_, columns=features).sort_values(by='log_return')
    print("\n2.A. State Statistical Profiles:")
    print(state_means.round(4))

    bear_market_state, bull_market_state = state_means.index[0], state_means.index[-1]
    if bear_market_state == bull_market_state:
        raise ValueError("Bull and Bear states are the same.")
    
    state_map = {bull_market_state: "Bull", bear_market_state: "Bear"}
    for i in range(model.n_components):
        if i not in [bull_market_state, bear_market_state]:
            state_map[i] = "Neutral"
    
    print(f"\nRegime Interpretation -> Bull: State {bull_market_state}, Bear: State {bear_market_state}")

    print("\n2.B. State Persistence & Transition Analysis:")
    transition_df = pd.DataFrame(model.transmat_,
                               columns=[f'To {i}' for i in range(model.n_components)],
                               index=[f'From {i}' for i in range(model.n_components)])
    print(transition_df.round(3))
    
    expected_durations = 1 / (1 - np.diag(model.transmat_))
    print("\nExpected State Durations (in minutes):")
    for i, duration in enumerate(expected_durations):
        print(f"  - State {i} ({state_map.get(i, 'Neutral')}): {duration:.1f} minutes")

    train_df['hidden_state'] = model.predict(X_train_scaled)
    train_df['next_period_vol'] = train_df['log_return'].shift(-1).abs()
    vol_prediction = train_df.groupby('hidden_state')['next_period_vol'].mean().sort_values(ascending=False)
    print("\n2.C. Predictive Power for Next-Period Volatility:")
    print(vol_prediction.round(6))
    
    if vol_prediction.index[0] == bear_market_state:
        print("✓ Conclusion: Correct. The 'Bear' state correctly predicts higher future volatility.")
    else:
        print("⚠️ Conclusion: Warning. The 'Bear' state does not predict higher future volatility.")
        
    return state_map

def run_backtest_and_visualize(df: pd.DataFrame, model: GaussianHMM, state_map: dict, features: list, scaler, start_date: pd.Timestamp):
    """Runs the backtest and creates a consolidated visualization with clear background shading."""
    print("\n--- 3. Final Backtest & Visualization --- ")
    
    # 使用标准化数据进行预测
    X_test_scaled = scaler.transform(df[features].values)
    
    df['hidden_state'] = model.predict(X_test_scaled)
    df['regime'] = df['hidden_state'].map(state_map).fillna("Neutral")
    df['signal'] = df['regime'].map({"Bull": 1, "Bear": -1, "Neutral": 0}).shift(1).fillna(0)
    df['strategy_return'] = df['log_return'] * df['signal']
    df['bnh_return'] = df['log_return']

    # 计算性能指标
    strategy_total_return = np.exp(df['strategy_return'].sum()) - 1
    bnh_total_return = np.exp(df['bnh_return'].sum()) - 1
    strategy_sharpe = df['strategy_return'].mean() / df['strategy_return'].std() * np.sqrt(252*24*60) if df['strategy_return'].std() > 0 else 0
    bnh_sharpe = df['bnh_return'].mean() / df['bnh_return'].std() * np.sqrt(252*24*60) if df['bnh_return'].std() > 0 else 0
    
    # 额外性能指标
    cumulative_strategy = np.exp(df['strategy_return'].cumsum())
    running_max = cumulative_strategy.expanding().max()
    drawdown = (cumulative_strategy - running_max) / running_max
    max_drawdown = drawdown.min()
    win_rate = (df['strategy_return'] > 0).sum() / len(df['strategy_return'])
    
    print(f"\n📊 HMM Strategy v2 Performance Summary:")
    print(f"Strategy Total Return: {strategy_total_return:.2%}")
    print(f"Buy & Hold Total Return: {bnh_total_return:.2%}")
    print(f"Strategy Sharpe Ratio: {strategy_sharpe:.3f}")
    print(f"Buy & Hold Sharpe Ratio: {bnh_sharpe:.3f}")
    print(f"Outperformance: {strategy_total_return - bnh_total_return:.2%}")
    print(f"Max Drawdown: {max_drawdown:.2%}")
    print(f"Win Rate: {win_rate:.1%}")

    # --- Visualization --- 
    fig, axes = plt.subplots(2, 2, figsize=(18, 12), gridspec_kw={'height_ratios': [3, 2]})
    fig.suptitle('HMM Strategy v2: 引入动量特征 (momentum_5m/20m/ratio)', fontsize=16, fontweight='bold')
    plt.style.use('seaborn-v0_8-darkgrid')

    x_axis = np.arange(len(df.index))
    num_ticks = 6
    tick_indices = np.linspace(0, len(x_axis) - 1, num_ticks, dtype=int)
    tick_labels = [df.index[i].strftime('%Y-%m-%d') for i in tick_indices]

    # 1. Cumulative Returns
    ax1 = axes[0, 0]
    ax1.plot(x_axis, np.exp(df['strategy_return'].cumsum()), label='HMM Strategy v2', linewidth=2, color='darkblue')
    ax1.plot(x_axis, np.exp(df['bnh_return'].cumsum()), label='Buy & Hold', linestyle='--', alpha=0.7, color='gray')
    ax1.axhline(y=1.0369, color='gold', linestyle=':', label='V1 Baseline (3.69%)', alpha=0.8)
    ax1.set_title('1. Strategy Performance Comparison', fontsize=12, fontweight='bold')
    ax1.set_ylabel('Cumulative Return'); ax1.legend(); ax1.grid(True)
    ax1.set_xticks(tick_indices); ax1.set_xticklabels(tick_labels, rotation=15, ha='right')

    # 2. Price with Regimes (Corrected Visualization)
    ax2 = axes[0, 1]
    ax2.plot(x_axis, df['Close'], color='black', label='Gold Price', lw=1.0)
    
    state_map_inv = {v: k for k, v in state_map.items()}
    bull_state, bear_state = state_map_inv.get("Bull"), state_map_inv.get("Bear")
    other_states = [s for s in range(model.n_components) if s not in [bull_state, bear_state]]
    state_colors = {bull_state: 'lightgreen', bear_state: 'lightcoral'}
    color_palette = ['lightyellow', 'lightblue', 'lightgrey']
    for i, state in enumerate(other_states):
        state_colors[state] = color_palette[i % len(color_palette)]

    # Use fill_between to avoid overlapping color blocks
    for i in range(model.n_components):
        state_mask = (df['hidden_state'] == i)
        ax2.fill_between(x_axis, 0, df['Close'].max()*1.1, where=state_mask,
                        facecolor=state_colors[i], alpha=0.4)

    # Set y-axis to auto-adapt to price range
    min_val = float(df['Close'].min())
    max_val = float(df['Close'].max())
    ax2.set_ylim(min_val * 0.95, max_val * 1.05)
    
    ax2.set_title('2. Visual Inspection: Price & Regimes', fontsize=12, fontweight='bold')
    legend_elements = [
        Patch(color='black', label='Gold Price'),
        Patch(facecolor=state_colors[bull_state], alpha=0.4, label=f'Bull (State {bull_state})'),
        Patch(facecolor=state_colors[bear_state], alpha=0.4, label=f'Bear (State {bear_state})'),
    ]
    for state in other_states:
        legend_elements.append(Patch(facecolor=state_colors[state], alpha=0.4, label=f'Neutral (State {state})'))
    ax2.legend(handles=legend_elements); ax2.grid(True)
    ax2.set_xticks(tick_indices); ax2.set_xticklabels(tick_labels, rotation=15, ha='right')

    # 3. Regime Timeline
    ax3 = axes[1, 0]
    ax3.plot(x_axis, df['hidden_state'], drawstyle='steps-post', linewidth=1.5)
    ax3.set_title('3. Regime Timeline', fontsize=12, fontweight='bold')
    ax3.set_yticks(range(model.n_components))
    ax3.set_yticklabels([f"State {i} ({state_map.get(i, 'Neutral')})" for i in range(model.n_components)])
    ax3.grid(True)
    ax3.set_xticks(tick_indices); ax3.set_xticklabels(tick_labels, rotation=15, ha='right')

    # 4. Performance Metrics
    ax4 = axes[1, 1]
    ax4.axis('off')
    def calculate_metrics(returns, name):
        if returns.empty: return {k: "0.00%" for k in ["Total Return", "Annualized Return"]} | {"Sharpe Ratio": "0.00", "Max DD": "0.00%", "Win Rate": "0.00%"}
        median_td = returns.index.to_series().diff().median()
        ppy = (pd.Timedelta(days=365) / median_td) if pd.notna(median_td) else 252*24*60
        sr = np.exp(returns) - 1
        tr = (1 + sr).prod() - 1
        ar = (1 + sr.mean())**ppy - 1
        sh = ar / (sr.std() * np.sqrt(ppy)) if sr.std() != 0 else 0
        
        # Calculate drawdown for this specific series
        cumulative = np.exp(returns.cumsum())
        running_max = cumulative.expanding().max()
        dd = ((cumulative - running_max) / running_max).min()
        wr = (returns > 0).sum() / len(returns)
        
        return {"Name": name, "Total Return": f"{tr:.2%}", "Annualized Return": f"{ar:.2%}", 
                "Sharpe Ratio": f"{sh:.2f}", "Max DD": f"{dd:.2%}", "Win Rate": f"{wr:.1%}"}
    
    metrics = [calculate_metrics(df['strategy_return'], "HMM Strategy v2"), calculate_metrics(df['bnh_return'], "Buy & Hold")]
    table = ax4.table(cellText=[list(m.values()) for m in metrics], colLabels=list(metrics[0].keys()), loc='center', cellLoc='center')
    table.auto_set_font_size(False); table.set_fontsize(9); table.scale(1.1, 1.5)
    ax4.set_title('4. Performance Metrics', fontsize=12, fontweight='bold', y=0.8)

    plt.tight_layout(rect=[0, 0, 1, 0.96])
    plt.show()
    
    return {
        'strategy_return': strategy_total_return,
        'bnh_return': bnh_total_return,
        'strategy_sharpe': strategy_sharpe,
        'bnh_sharpe': bnh_sharpe,
        'outperformance': strategy_total_return - bnh_total_return,
        'max_drawdown': max_drawdown,
        'win_rate': win_rate
    }

def main():
    """Main function to orchestrate the HMM strategy evaluation."""
    try:
        file_path = os.path.join("data/gold", "1m.parquet")
        TRAIN_TEST_SPLIT_DATE = pd.to_datetime("2025-08-16", utc=True)
        
        # V8 Enhanced feature set - 6个最优特征
        features = ['log_return', 'log_hl_range', 'rsi', 'momentum_5m', 'momentum_20m', 'momentum_ratio']
        
        featured_df = load_and_prepare_data(file_path)
        
        train_data = featured_df[featured_df.index < TRAIN_TEST_SPLIT_DATE].copy()
        test_data = featured_df[featured_df.index >= TRAIN_TEST_SPLIT_DATE].copy()
        
        if train_data.empty or test_data.empty:
            raise ValueError("Not enough data for train/test split.")
        
        print(f"\nTraining data shape: {train_data.shape}")
        print(f"Testing data shape: {test_data.shape}")
        
        model, scaler = train_hmm_model(train_data[features].values)
        state_map = perform_full_evaluation(model, train_data, features, scaler)
        results = run_backtest_and_visualize(test_data, model, state_map, features, scaler, TRAIN_TEST_SPLIT_DATE)
        
        print(f"\n🏆 v2 vs Historical Versions:")
        print("="*50)
        print(f"V0 Baseline:   收益 1.83%,  夏普 4.578")
        print(f"V1 Golden:     收益 3.69%,  夏普 9.544  ⭐")
        print(f"v2 Enhanced:   收益{results['strategy_return']:5.2%}, 夏普{results['strategy_sharpe']:6.3f}  🚀")
        print("="*50)
        
        return results

    except (FileNotFoundError, ValueError, RuntimeError) as e:
        print(f"\nExecution failed: {e}")
        return None
    except Exception as e:
        print(f"\nAn unexpected critical error occurred: {e}")
        return None

if __name__ == "__main__":
    results = main()