#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的策略参数优化脚本
====================

专为gold子目录设计的简单易用的参数优化工具
支持命令行使用，一键优化任意策略版本

使用示例:
python optimize_strategy.py v6                    # 优化v6版本
python optimize_strategy.py v6 --combinations 50  # 限制50个组合  
python optimize_strategy.py all --quick          # 快速优化所有版本
"""

import argparse
import sys
from pathlib import Path

# 添加当前目录到Python路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

try:
    from parameter_optimizer import ParameterOptimizer
except ImportError:
    print("❌ 无法导入parameter_optimizer模块")
    print("请确保parameter_optimizer.py在同一目录下")
    sys.exit(1)

def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(
        description='Gold Strategy Parameter Optimizer',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog='''
使用示例:
  python optimize_strategy.py v6                    # 优化v6版本
  python optimize_strategy.py v6 --combinations 50  # 限制50个组合
  python optimize_strategy.py all --quick          # 快速优化所有版本
  python optimize_strategy.py v5 --splits 2        # 使用2个时间分割点
        '''
    )
    
    parser.add_argument(
        'strategy',
        choices=['v0', 'v1', 'v2', 'v3', 'v4', 'v5', 'v6', 'v7', 'all'],
        help='要优化的策略版本 (v0-v7) 或 all (所有版本)'
    )
    
    parser.add_argument(
        '--combinations', '-c',
        type=int,
        default=50,
        help='最大参数组合数量 (默认: 50)'
    )
    
    parser.add_argument(
        '--splits', '-s',
        type=int,
        default=3,
        help='时间分割点数量 (默认: 3)'
    )
    
    parser.add_argument(
        '--quick', '-q',
        action='store_true',
        help='快速模式: 减少参数组合和时间分割点'
    )
    
    parser.add_argument(
        '--workers', '-w',
        type=int,
        default=4,
        help='并行工作进程数 (默认: 4)'
    )
    
    return parser.parse_args()

def format_strategy_name(version):
    """格式化策略名称"""
    if version == 'all':
        return version
    return f'hmm_strategy_v{version}'

def generate_split_dates(num_splits):
    """生成时间分割点"""
    base_dates = [
        "2025-08-10", "2025-08-13", "2025-08-16", 
        "2025-08-19", "2025-08-22", "2025-08-25"
    ]
    return base_dates[:num_splits]

def main():
    """主函数"""
    args = parse_arguments()
    
    # 快速模式调整
    if args.quick:
        max_combinations = min(args.combinations, 20)
        num_splits = min(args.splits, 2)
        print("🚀 快速模式启用: 减少参数组合和时间分割点")
    else:
        max_combinations = args.combinations
        num_splits = args.splits
    
    print(f"""
🔧 Gold Strategy Parameter Optimizer
=====================================
策略版本: {args.strategy}
参数组合: {max_combinations}
时间分割: {num_splits}
并行进程: {args.workers}
=====================================
    """)
    
    try:
        # 初始化优化器
        optimizer = ParameterOptimizer(max_workers=args.workers)
        
        if args.strategy == 'all':
            # 批量优化所有策略
            print("📊 开始批量优化所有策略版本...")
            
            all_results = optimizer.batch_optimize_all_strategies(
                max_combinations_per_strategy=max_combinations
            )
            
            print(f"\n✅ 批量优化完成！")
            print(f"📁 结果保存在: {optimizer.results_dir}")
            
            # 显示简要对比
            print(f"\n📊 策略版本简要对比:")
            for strategy, data in all_results.items():
                if 'analysis' in data and data['analysis']:
                    metrics = data['analysis'].get('best_metrics', {})
                    sharpe = metrics.get('sharpe', 0)
                    return_rate = metrics.get('total_return', 0)
                    print(f"   {strategy:20s} | 夏普: {sharpe:6.2f} | 收益: {return_rate:6.2%}")
        
        else:
            # 优化单个策略版本
            strategy_name = format_strategy_name(args.strategy)
            split_dates = generate_split_dates(num_splits)
            
            print(f"📊 开始优化策略: {strategy_name}")
            print(f"📅 测试时间点: {split_dates}")
            
            results = optimizer.optimize_strategy_version(
                strategy_name,
                split_dates=split_dates,
                max_combinations=max_combinations
            )
            
            if results:
                # 分析结果
                analysis = optimizer.analyze_optimization_results(strategy_name, results)
                
                if analysis:
                    print(f"\n🏆 优化完成！最佳参数:")
                    best_params = analysis.get('best_parameters', {})
                    for param, value in best_params.items():
                        print(f"   {param}: {value}")
                    
                    best_metrics = analysis.get('best_metrics', {})
                    print(f"\n📈 最佳性能:")
                    for metric, value in best_metrics.items():
                        if metric == 'total_return':
                            print(f"   收益率: {value:.2%}")
                        elif metric == 'sharpe':
                            print(f"   夏普比率: {value:.2f}")
                        elif metric == 'max_drawdown':
                            print(f"   最大回撤: {value:.2%}")
                        elif metric == 'win_rate':
                            print(f"   胜率: {value:.1%}")
                
                print(f"\n📁 详细结果保存在: {optimizer.results_dir}")
            else:
                print("❌ 优化失败：没有获得有效结果")
    
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断优化过程")
        sys.exit(1)
    
    except Exception as e:
        print(f"\n❌ 优化过程出错: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
