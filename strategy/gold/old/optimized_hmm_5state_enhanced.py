#!/usr/bin/env python3
"""
增强5状态HMM黄金1分钟数据分析 - 基于经典3状态架构优化
=======================================================

基于optimized_hmm_3state_classic.py的优秀架构，专门针对5状态进行深度优化：
1. 强上涨 - 明显的强势上升动量
2. 弱上涨 - 温和的上升趋势  
3. 震荡整理 - 横盘或小幅波动
4. 弱下跌 - 温和的下降趋势
5. 强下跌 - 明显的强势下降动量

优化策略：
- 继承3状态版本的精简特征工程理念
- 专门调优5状态的分类阈值和特征组合
- 增强状态间的区分度和业务解释性
- 提供更精细的交易信号

基于参考：optimized_hmm_3state_classic.py (经典架构)
全面分析：comprehensive_hmm_comparison.py (科学验证)
"""

import numpy as np
import pandas as pd
import warnings
from datetime import datetime
import matplotlib.pyplot as plt
from sklearn.preprocessing import StandardScaler, RobustScaler
from sklearn.metrics import classification_report, confusion_matrix
from hmmlearn.hmm import GaussianHMM
import seaborn as sns
from typing import Tuple, Dict, List
import talib

# 配置中文字体和警告
warnings.filterwarnings('ignore')
plt.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def RSI(series, period=14):
    """计算相对强弱指数(RSI)"""
    try:
        if hasattr(series, 'to_numpy'):
            prices = pd.Series(series)
        else:
            prices = pd.Series(series)
        
        delta = prices.diff()
        gains = delta.where(delta > 0, 0)
        losses = -delta.where(delta < 0, 0)
        
        avg_gains = gains.rolling(window=period, min_periods=1).mean()
        avg_losses = losses.rolling(window=period, min_periods=1).mean()
        
        rs = avg_gains / avg_losses
        rsi = 100 - (100 / (1 + rs))
        rsi = rsi.fillna(50)
        
        return rsi.values
    except Exception as e:
        return np.full(len(series), 50.0)

class KalmanFilter:
    """卡尔曼滤波器实现 - 继承自3状态版本"""
    
    def __init__(self, transition_matrices=None, observation_matrices=None,
                 initial_state_mean=None, n_dim_state=2):
        self.n_dim_state = n_dim_state
        
        if transition_matrices is None:
            self.transition_matrices = np.eye(n_dim_state)
        else:
            self.transition_matrices = transition_matrices
            
        if observation_matrices is None:
            self.observation_matrices = np.eye(1, n_dim_state)
        else:
            self.observation_matrices = observation_matrices
            
        if initial_state_mean is None:
            self.initial_state_mean = np.zeros(n_dim_state)
        else:
            self.initial_state_mean = initial_state_mean
            
        self.transition_covariance = np.eye(n_dim_state) * 0.01
        self.observation_covariance = np.array([[1.0]])
        self.initial_state_covariance = np.eye(n_dim_state)
        
    def filter(self, observations):
        """执行卡尔曼滤波"""
        n_observations = len(observations)
        state_estimates = np.zeros((n_observations, self.n_dim_state))
        state_covariances = np.zeros((n_observations, self.n_dim_state, self.n_dim_state))
        
        current_state = self.initial_state_mean.copy()
        current_covariance = self.initial_state_covariance.copy()
        
        for t in range(n_observations):
            predicted_state = self.transition_matrices @ current_state
            predicted_covariance = (self.transition_matrices @ current_covariance @ 
                                   self.transition_matrices.T + self.transition_covariance)
            
            innovation = observations[t] - self.observation_matrices @ predicted_state
            innovation_covariance = (self.observation_matrices @ predicted_covariance @ 
                                   self.observation_matrices.T + self.observation_covariance)
            
            kalman_gain = (predicted_covariance @ self.observation_matrices.T @ 
                          np.linalg.inv(innovation_covariance))
            
            current_state = predicted_state + kalman_gain @ innovation
            current_covariance = ((np.eye(self.n_dim_state) - 
                                 kalman_gain @ self.observation_matrices) @ predicted_covariance)
            
            state_estimates[t] = current_state
            state_covariances[t] = current_covariance
            
        return state_estimates, state_covariances

class Enhanced5StateHMM:
    """
    增强5状态HMM处理器 - 基于3状态架构优化
    """
    
    def __init__(self, use_kalman_filter=True):
        self.model = None
        self.state_means = None
        self.scaler = RobustScaler()
        self.feature_names = []
        self.use_kalman_filter = use_kalman_filter
        
    def load_gold_data(self, file_path: str) -> pd.DataFrame:
        """加载黄金1分钟数据 - 继承3状态版本逻辑"""
        print("📊 加载黄金1分钟数据...")
        
        column_names = ['Datetime', 'Close', 'High', 'Low', 'Open', 'Volume']
        df = pd.read_csv(file_path, skiprows=3, names=column_names)
        df['Datetime'] = pd.to_datetime(df['Datetime'])
        df = df.set_index('Datetime')
        
        numeric_columns = ['Close', 'High', 'Low', 'Open', 'Volume']
        for col in numeric_columns:
            df[col] = pd.to_numeric(df[col], errors='coerce')
        
        df = df.dropna()
        df['Returns'] = np.log(df['Close']).diff()
        df.dropna(inplace=True)
        
        print(f"✅ 数据加载完成，共{len(df)}条记录")
        print(f"📅 时间范围: {df.index.min()} 到 {df.index.max()}")
        
        return df
    
    def apply_kalman_filter(self, data):
        """应用卡尔曼滤波器 - 继承3状态版本"""
        if not self.use_kalman_filter:
            data['Kalman'] = data['Close']
            return data
            
        print("🔧 应用卡尔曼滤波器...")
        
        prices = data['Close'].values.reshape(-1, 1)
        kf = KalmanFilter(n_dim_state=2)
        state_estimates, _ = kf.filter(prices)
        filtered_prices = state_estimates[:, 0]
        
        data_with_kalman = data.copy()
        data_with_kalman['Kalman'] = filtered_prices
        
        print("✅ 卡尔曼滤波完成")
        return data_with_kalman
    
    def engineer_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        增强5状态专用特征工程 - 基于3状态版本扩展
        """
        print("🔧 增强5状态特征工程...")
        
        features = pd.DataFrame(index=df.index)
        
        # 1. 基础收益率特征（保持3状态的优秀设计）
        features['returns'] = df['Returns']
        features['returns_smooth'] = features['returns'].rolling(3, center=True).mean().fillna(features['returns'])
        
        # 2. 多层次波动率（5状态需要更精细的波动率区分）
        features['volatility_short'] = features['returns_smooth'].rolling(10).std()  # 短期
        features['volatility_medium'] = features['returns_smooth'].rolling(20).std()  # 中期
        features['volatility_long'] = features['returns_smooth'].rolling(40).std()   # 长期
        
        # 3. 波动率比率（5状态关键特征）
        features['vol_ratio_sm'] = (features['volatility_short'] / features['volatility_medium']).fillna(1)
        features['vol_ratio_ml'] = (features['volatility_medium'] / features['volatility_long']).fillna(1)
        
        # 4. 增强趋势指标（5状态需要区分强弱趋势）
        if len(df) > 30:
            features['rsi'] = RSI(df['Close'], period=20)  # 稍微延长周期
            features['rsi_norm'] = ((features['rsi'] - 50) / 50).rolling(5).mean().fillna((features['rsi'] - 50) / 50)
            
            # 多时间框架动量（5状态核心特征）
            features['momentum_5m'] = (df['Close'] / df['Close'].shift(5) - 1).rolling(3).mean()
            features['momentum_15m'] = (df['Close'] / df['Close'].shift(15) - 1).rolling(5).mean()
            features['momentum_30m'] = (df['Close'] / df['Close'].shift(30) - 1).rolling(5).mean()
            
            # 动量强度指标（区分强弱）
            features['momentum_strength'] = (
                np.abs(features['momentum_15m']) * np.sign(features['momentum_15m'])
            ).rolling(5).mean()
        
        # 5. 精细化成交量特征
        if 'Volume' in df.columns and df['Volume'].sum() > 0:
            volume_ma = df['Volume'].rolling(30).mean()
            features['volume_zscore'] = ((df['Volume'] - volume_ma) / df['Volume'].rolling(30).std()).fillna(0)
            features['volume_trend'] = (features['volume_zscore'] * features['returns_smooth']).rolling(5).mean()
            features['volume_momentum'] = features['volume_zscore'].rolling(10).mean()
        else:
            features['volume_zscore'] = np.abs(features['returns_smooth']) * 2
            features['volume_trend'] = features['volume_zscore'] * features['returns_smooth']
            features['volume_momentum'] = features['volume_trend'].rolling(10).mean()
        
        # 6. K线形态特征（5状态重要区分特征）
        features['body_ratio'] = np.abs(df['Close'] - df['Open']) / (df['High'] - df['Low'] + 1e-8)
        features['upper_shadow'] = (df['High'] - np.maximum(df['Open'], df['Close'])) / (df['High'] - df['Low'] + 1e-8)
        features['lower_shadow'] = (np.minimum(df['Open'], df['Close']) - df['Low']) / (df['High'] - df['Low'] + 1e-8)
        
        # 7. 卡尔曼滤波趋势（继承并增强）
        if 'Kalman' in df.columns:
            features['kalman_trend_short'] = (df['Kalman'] / df['Kalman'].shift(10) - 1).rolling(3).mean()
            features['kalman_trend_long'] = (df['Kalman'] / df['Kalman'].shift(30) - 1).rolling(5).mean()
            features['kalman_acceleration'] = features['kalman_trend_short'] - features['kalman_trend_long']
        
        # 8. 价格位置特征（5状态辅助特征）
        features['price_position_20'] = (df['Close'] - df['Close'].rolling(20).min()) / (
            df['Close'].rolling(20).max() - df['Close'].rolling(20).min() + 1e-8
        )
        
        # 移除NaN并选择最适合5状态的特征
        features = features.dropna()
        
        # 专门为5状态选择的核心特征组合
        selected_features = [
            'returns_smooth', 'volatility_short', 'volatility_medium', 'vol_ratio_sm'
        ]
        
        if 'rsi_norm' in features.columns:
            selected_features.extend(['rsi_norm'])
        if 'momentum_5m' in features.columns:
            selected_features.extend(['momentum_5m', 'momentum_15m', 'momentum_strength'])
        if 'kalman_trend_short' in features.columns:
            selected_features.extend(['kalman_trend_short', 'kalman_acceleration'])
        
        selected_features.extend(['volume_trend', 'body_ratio', 'price_position_20'])
        
        # 确保特征存在
        available_features = [f for f in selected_features if f in features.columns]
        features = features[available_features]
        
        print(f"✅ 增强5状态特征工程完成，特征: {list(features.columns)}")
        return features
    
    def train_5state_hmm(self, features: pd.DataFrame):
        """专门训练5状态HMM模型"""
        print("🧠 训练增强5状态HMM模型（强上涨/弱上涨/震荡/弱下跌/强下跌）...")
        
        # 标准化特征
        scaled_features = self.scaler.fit_transform(features)
        self.feature_names = list(features.columns)
        
        # 训练HMM模型 - 为5状态专门优化参数
        self.model = GaussianHMM(n_components=5, 
                                covariance_type="full", 
                                n_iter=3000,  # 增加迭代次数
                                random_state=42,
                                algorithm='viterbi',
                                tol=1e-6)
        
        self.model.fit(scaled_features)
        
        # 预测状态
        states = self.model.predict(scaled_features)
        
        # 分析状态特征并进行增强5状态映射
        state_analysis = []
        for i in range(5):
            state_mask = states == i
            if np.sum(state_mask) > 0:
                state_features = features[state_mask]
                avg_return = state_features['returns_smooth'].mean()
                volatility = state_features['returns_smooth'].std()
                
                # 计算趋势强度和动量
                momentum_cols = [col for col in state_features.columns if 'momentum' in col]
                if momentum_cols:
                    momentum = state_features[momentum_cols].mean().mean()
                else:
                    momentum = avg_return * 1000
                
                # 计算波动率强度
                vol_strength = state_features['volatility_short'].mean() if 'volatility_short' in state_features.columns else volatility
                
                frequency = np.sum(state_mask) / len(states)
                
                state_analysis.append({
                    'state': i,
                    'avg_return': avg_return,
                    'volatility': volatility,
                    'vol_strength': vol_strength,
                    'momentum': momentum,
                    'frequency': frequency,
                    'observations': np.sum(state_mask)
                })
                
                print(f"状态{i}: 收益={avg_return:.6f}, 波动={volatility:.6f}, 动量={momentum:.6f}, 频率={frequency:.1%}")
        
        # 增强5状态映射：基于收益率和波动率进行精细分类
        state_analysis.sort(key=lambda x: x['avg_return'])
        
        self.state_mapping = {}
        
        # 基于五分位数进行精细分类
        returns = [sa['avg_return'] for sa in state_analysis]
        volatilities = [sa['vol_strength'] for sa in state_analysis]
        
        for i, sa in enumerate(state_analysis):
            avg_ret = sa['avg_return']
            vol_str = sa['vol_strength']
            
            if i == 0:  # 最低收益
                if vol_str > np.median(volatilities):
                    self.state_mapping[sa['state']] = '强下跌'
                else:
                    self.state_mapping[sa['state']] = '弱下跌'
            elif i == 1:  # 第二低收益
                if avg_ret < -0.000002:
                    self.state_mapping[sa['state']] = '弱下跌'
                else:
                    self.state_mapping[sa['state']] = '震荡偏弱'
            elif i == 2:  # 中等收益
                self.state_mapping[sa['state']] = '震荡整理'
            elif i == 3:  # 第二高收益
                if avg_ret > 0.000002:
                    self.state_mapping[sa['state']] = '弱上涨'
                else:
                    self.state_mapping[sa['state']] = '震荡偏强'
            else:  # 最高收益
                if vol_str > np.median(volatilities):
                    self.state_mapping[sa['state']] = '强上涨'
                else:
                    self.state_mapping[sa['state']] = '弱上涨'
        
        # 确保有明确的强/弱分类
        if max(returns) > 0.000003:
            max_idx = np.argmax(returns)
            max_vol = state_analysis[max_idx]['vol_strength']
            if max_vol > np.median(volatilities):
                self.state_mapping[state_analysis[max_idx]['state']] = '强上涨'
            else:
                self.state_mapping[state_analysis[max_idx]['state']] = '弱上涨'
        
        if min(returns) < -0.000003:
            min_idx = np.argmin(returns)
            min_vol = state_analysis[min_idx]['vol_strength']
            if min_vol > np.median(volatilities):
                self.state_mapping[state_analysis[min_idx]['state']] = '强下跌'
            else:
                self.state_mapping[state_analysis[min_idx]['state']] = '弱下跌'
        
        print(f"✅ 增强5状态HMM模型训练完成")
        print(f"📊 状态映射: {self.state_mapping}")
        
        return states, state_analysis
    
    def predict_states(self, features: pd.DataFrame):
        """预测新数据的状态"""
        scaled_features = self.scaler.transform(features[self.feature_names])
        states = self.model.predict(scaled_features)
        state_probs = self.model.predict_proba(scaled_features)
        return states, state_probs
    
    def get_state_characteristics(self, features: pd.DataFrame, states: np.ndarray):
        """获取状态特征分析"""
        unique_states = np.unique(states)
        analysis = []
        
        for state in unique_states:
            mask = states == state
            state_data = features[mask]
            
            char = {
                'State': self.state_mapping.get(state, f'状态{state}'),
                'Frequency': f"{np.sum(mask)}/{len(states)} ({100*np.sum(mask)/len(states):.1f}%)",
                'Avg_Return': state_data['returns_smooth'].mean() if 'returns_smooth' in state_data.columns else 0,
                'Volatility': state_data['returns_smooth'].std() if 'returns_smooth' in state_data.columns else 0,
                'Duration_Avg': self._calculate_avg_duration(states, state)
            }
            analysis.append(char)
        
        return pd.DataFrame(analysis)
    
    def _calculate_avg_duration(self, states, target_state):
        """计算状态平均持续时间"""
        durations = []
        current_duration = 0
        
        for state in states:
            if state == target_state:
                current_duration += 1
            else:
                if current_duration > 0:
                    durations.append(current_duration)
                current_duration = 0
        
        if current_duration > 0:
            durations.append(current_duration)
        
        return np.mean(durations) if durations else 0

def comprehensive_5state_validation(train_data: pd.DataFrame, test_data: pd.DataFrame) -> Dict:
    """
    全面的5状态模型验证 - 对比不同配置
    """
    print("\n🔄 全面5状态模型验证...")
    
    configurations = [
        {'kalman': True, 'name': '5状态+卡尔曼滤波'},
        {'kalman': False, 'name': '5状态+原始数据'},
    ]
    
    results = {}
    
    for config in configurations:
        print(f"\n🧪 测试 {config['name']}...")
        
        # 创建处理器
        processor = Enhanced5StateHMM(use_kalman_filter=config['kalman'])
        
        # 数据处理
        if config['kalman']:
            train_filtered = processor.apply_kalman_filter(train_data)
            test_filtered = processor.apply_kalman_filter(test_data)
        else:
            train_filtered = train_data.copy()
            test_filtered = test_data.copy()
        
        # 特征工程
        train_features = processor.engineer_features(train_filtered)
        test_features = processor.engineer_features(test_filtered)
        
        # 确保特征对齐
        common_features = train_features.columns.intersection(test_features.columns)
        train_features = train_features[common_features]
        test_features = test_features[common_features]
        
        # 训练模型
        train_states, state_analysis = processor.train_5state_hmm(train_features)
        
        # 测试预测
        test_states, test_probs = processor.predict_states(test_features)
        
        # 评估指标
        log_likelihood = processor.model.score(processor.scaler.transform(test_features))
        
        # 稳定性分析
        state_changes = np.sum(np.diff(test_states) != 0)
        avg_duration = len(test_states) / (state_changes + 1)
        
        # 5状态特有的评估：状态分布和区分度
        state_counts = [np.sum(test_states == i) for i in range(5)]
        state_balance = 1 - np.std(state_counts) / np.mean(state_counts)
        
        # 收益率分离度（5状态关键指标）
        state_returns = []
        for state in range(5):
            mask = test_states == state
            if np.sum(mask) > 0:
                state_returns.append(test_features.loc[mask, 'returns_smooth'].mean())
        return_separation = np.std(state_returns) if len(state_returns) > 1 else 0
        
        # 综合评分（针对5状态优化）
        stability_score = min(avg_duration / 15.0, 1.0) * 25  # 5状态期望更短持续时间
        likelihood_score = max(0, (log_likelihood + 30000) / 1000) * 25
        balance_score = state_balance * 20
        separation_score = return_separation * 5000 * 20  # 放大收益率分离度的权重
        complexity_penalty = 10  # 5状态的复杂度奖励
        
        final_score = stability_score + likelihood_score + balance_score + separation_score + complexity_penalty
        
        results[config['name']] = {
            'processor': processor,
            'log_likelihood': log_likelihood,
            'avg_duration': avg_duration,
            'state_changes': state_changes,
            'state_balance': state_balance,
            'return_separation': return_separation,
            'final_score': final_score,
            'train_states': train_states,
            'test_states': test_states,
            'train_features': train_features,
            'test_features': test_features,
            'state_analysis': state_analysis
        }
        
        print(f"📊 {config['name']}结果:")
        print(f"   对数似然: {log_likelihood:.2f}")
        print(f"   平均持续时间: {avg_duration:.1f}分钟")
        print(f"   状态均衡性: {state_balance:.3f}")
        print(f"   收益率分离度: {return_separation:.6f}")
        print(f"   综合评分: {final_score:.1f}/100")
    
    return results

def visualize_5state_results(results: Dict, test_data: pd.DataFrame):
    """可视化5状态结果"""
    print("\n📊 生成5状态分析图表...")
    
    # 选择最佳配置
    best_config = max(results.items(), key=lambda x: x[1]['final_score'])
    best_name, best_result = best_config
    processor = best_result['processor']
    test_states = best_result['test_states']
    test_features = best_result['test_features']
    
    fig, axes = plt.subplots(3, 3, figsize=(21, 18))
    fig.suptitle(f'🏆 增强5状态HMM分析 - {best_name}', fontsize=16, fontweight='bold')
    
    # 对齐数据
    aligned_test_data = test_data.loc[test_features.index]
    
    # 1. 价格序列与状态
    ax1 = axes[0, 0]
    colors = ['red', 'orange', 'gray', 'lightgreen', 'green']  # 强下跌、弱下跌、震荡、弱上涨、强上涨
    
    time_indices = np.arange(len(aligned_test_data))
    for state in range(5):
        mask = test_states == state
        if np.sum(mask) > 0:
            actual_state_name = processor.state_mapping.get(state, f'状态{state}')
            ax1.scatter(time_indices[mask], 
                       aligned_test_data['Close'].values[mask],
                       c=colors[state], alpha=0.8, s=12, label=actual_state_name)
    
    ax1.plot(range(len(aligned_test_data)), aligned_test_data['Close'], 
             color='black', alpha=0.2, linewidth=0.5)
    ax1.set_title('💰 价格与5状态识别')
    ax1.set_xlabel('时间点')
    ax1.set_ylabel('黄金价格 ($)')
    ax1.legend(fontsize=8)
    ax1.grid(True, alpha=0.3)
    
    # 2. 状态分布饼图
    ax2 = axes[0, 1]
    state_counts = {}
    for state in range(5):
        count = np.sum(test_states == state)
        state_name = processor.state_mapping.get(state, f'状态{state}')
        state_counts[state_name] = count
    
    ax2.pie(state_counts.values(), labels=state_counts.keys(), autopct='%1.1f%%',
            colors=colors)
    ax2.set_title('📊 5状态分布')
    
    # 3. 收益率分布对比
    ax3 = axes[0, 2]
    for state in range(5):
        mask = test_states == state
        if np.sum(mask) > 0:
            state_name = processor.state_mapping.get(state, f'状态{state}')
            returns_data = test_features.loc[mask, 'returns_smooth'] * 100
            ax3.hist(returns_data, alpha=0.6, bins=25, 
                    color=colors[state], label=state_name, density=True)
    
    ax3.set_title('📈 各状态收益率分布')
    ax3.set_xlabel('收益率 (%)')
    ax3.set_ylabel('密度')
    ax3.legend(fontsize=8)
    ax3.grid(True, alpha=0.3)
    
    # 4. 状态转移矩阵热图
    ax4 = axes[1, 0]
    transition_matrix = np.zeros((5, 5))
    for i in range(len(test_states) - 1):
        transition_matrix[test_states[i], test_states[i + 1]] += 1
    
    # 归一化
    for i in range(5):
        if transition_matrix[i].sum() > 0:
            transition_matrix[i] = transition_matrix[i] / transition_matrix[i].sum()
    
    state_labels = [processor.state_mapping.get(i, f'状态{i}') for i in range(5)]
    sns.heatmap(transition_matrix, annot=True, fmt='.2f', cmap='Blues', ax=ax4,
                xticklabels=state_labels, yticklabels=state_labels, cbar_kws={'shrink': 0.8})
    ax4.set_title('🔄 状态转移概率矩阵')
    ax4.tick_params(axis='x', rotation=45)
    ax4.tick_params(axis='y', rotation=0)
    
    # 5. 状态持续时间分布
    ax5 = axes[1, 1]
    durations = {state: [] for state in range(5)}
    current_state = test_states[0]
    duration = 1
    
    for i in range(1, len(test_states)):
        if test_states[i] == current_state:
            duration += 1
        else:
            durations[current_state].append(duration)
            current_state = test_states[i]
            duration = 1
    durations[current_state].append(duration)
    
    for state in range(5):
        if durations[state]:
            state_name = processor.state_mapping.get(state, f'状态{state}')
            ax5.hist(durations[state], alpha=0.6, bins=15,
                    color=colors[state], label=state_name, density=True)
    
    ax5.set_title('⏱️ 状态持续时间分布')
    ax5.set_xlabel('持续时间 (分钟)')
    ax5.set_ylabel('密度')
    ax5.legend(fontsize=8)
    ax5.grid(True, alpha=0.3)
    
    # 6. 波动率vs收益率散点图
    ax6 = axes[1, 2]
    for state in range(5):
        mask = test_states == state
        if np.sum(mask) > 0:
            state_name = processor.state_mapping.get(state, f'状态{state}')
            returns = test_features.loc[mask, 'returns_smooth'] * 100
            volatility = test_features.loc[mask, 'volatility_short'] * 100
            ax6.scatter(volatility, returns, alpha=0.6, c=colors[state], 
                       label=state_name, s=8)
    
    ax6.set_title('📈 波动率 vs 收益率')
    ax6.set_xlabel('波动率 (%)')
    ax6.set_ylabel('收益率 (%)')
    ax6.legend(fontsize=8)
    ax6.grid(True, alpha=0.3)
    
    # 7. 配置对比柱状图
    ax7 = axes[2, 0]
    config_names = list(results.keys())
    scores = [results[name]['final_score'] for name in config_names]
    separations = [results[name]['return_separation'] * 10000 for name in config_names]
    
    x = np.arange(len(config_names))
    width = 0.35
    
    ax7_twin = ax7.twinx()
    bars1 = ax7.bar(x - width/2, scores, width, label='综合评分', color='lightblue', alpha=0.8)
    bars2 = ax7_twin.bar(x + width/2, separations, width, label='收益率分离度×10000', color='lightcoral', alpha=0.8)
    
    ax7.set_xlabel('配置')
    ax7.set_ylabel('综合评分', color='blue')
    ax7_twin.set_ylabel('收益率分离度×10000', color='red')
    ax7.set_title('⚖️ 配置对比')
    ax7.set_xticks(x)
    ax7.set_xticklabels(config_names, rotation=15)
    
    # 添加数值标签
    for bar, score in zip(bars1, scores):
        ax7.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 2,
                f'{score:.1f}', ha='center', va='bottom', fontsize=9)
    
    for bar, sep in zip(bars2, separations):
        ax7_twin.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.1,
                     f'{sep:.2f}', ha='center', va='bottom', fontsize=9)
    
    # 8. 状态特征雷达图
    ax8 = axes[2, 1]
    
    # 计算各状态的特征均值
    state_features_summary = {}
    feature_names = ['returns_smooth', 'volatility_short', 'momentum_15m']
    available_features = [f for f in feature_names if f in test_features.columns]
    
    if len(available_features) >= 2:
        for state in range(5):
            mask = test_states == state
            if np.sum(mask) > 0:
                state_name = processor.state_mapping.get(state, f'状态{state}')
                values = []
                for feature in available_features:
                    val = test_features.loc[mask, feature].mean()
                    values.append(val)
                state_features_summary[state_name] = values
        
        # 绘制特征对比
        x_pos = np.arange(len(available_features))
        bar_width = 0.15
        
        for i, (state_name, values) in enumerate(state_features_summary.items()):
            ax8.bar(x_pos + i * bar_width, values, bar_width, 
                   label=state_name, color=colors[i], alpha=0.8)
        
        ax8.set_xlabel('特征')
        ax8.set_ylabel('特征值')
        ax8.set_title('🎯 状态特征对比')
        ax8.set_xticks(x_pos + bar_width * 2)
        ax8.set_xticklabels(available_features, rotation=15)
        ax8.legend(fontsize=8)
        ax8.grid(True, alpha=0.3)
    
    # 9. 时间序列状态图
    ax9 = axes[2, 2]
    time_slice = slice(0, min(500, len(test_states)))  # 显示前500个点
    time_points = np.arange(len(test_states[time_slice]))
    
    # 创建状态时间序列
    for state in range(5):
        mask = test_states[time_slice] == state
        state_name = processor.state_mapping.get(state, f'状态{state}')
        ax9.scatter(time_points[mask], [state] * np.sum(mask), 
                   c=colors[state], alpha=0.8, s=3, label=state_name)
    
    ax9.set_title('📅 状态时间序列 (前500点)')
    ax9.set_xlabel('时间点')
    ax9.set_ylabel('状态')
    ax9.set_yticks(range(5))
    ax9.set_yticklabels([processor.state_mapping.get(i, f'状态{i}') for i in range(5)], fontsize=8)
    ax9.grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    # 保存图表
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f'enhanced_5state_hmm_analysis_{timestamp}.png'
    plt.savefig(filename, dpi=300, bbox_inches='tight')
    plt.show()
    
    print(f"✅ 图表已保存为: {filename}")
    
    return processor.get_state_characteristics(test_features, test_states)

def main():
    """主函数"""
    print("🚀 增强5状态HMM黄金分析 - 基于经典3状态架构优化")
    print("=" * 80)
    print("验证精细化的5状态分类：强上涨、弱上涨、震荡、弱下跌、强下跌")
    print("=" * 80)
    
    # 1. 加载数据
    processor = Enhanced5StateHMM(use_kalman_filter=True)
    gold_data = processor.load_gold_data("202507/gold_1m_20250701_20250725_132644.csv")
    
    # 2. 划分训练测试集
    train_end = pd.Timestamp('2025-07-15 23:59:59', tz='UTC')
    train_data = gold_data[gold_data.index <= train_end].copy()
    test_data = gold_data[gold_data.index > train_end].copy()
    
    print(f"📈 训练数据: {len(train_data)} 条 (7月1-15日)")
    print(f"📉 测试数据: {len(test_data)} 条 (7月16-25日)")
    
    # 3. 全面验证
    results = comprehensive_5state_validation(train_data, test_data)
    
    # 4. 可视化结果
    state_analysis = visualize_5state_results(results, test_data)
    
    # 5. 输出分析报告
    best_config = max(results.items(), key=lambda x: x[1]['final_score'])
    best_name, best_result = best_config
    
    print(f"\n📋 增强5状态分析结果:")
    print("=" * 80)
    print(f"🏆 最佳配置: {best_name}")
    print(f"📊 综合评分: {best_result['final_score']:.1f}/100")
    print(f"⏱️ 平均持续时间: {best_result['avg_duration']:.1f}分钟")
    print(f"🔄 状态切换次数: {best_result['state_changes']}")
    print(f"⚖️ 状态均衡性: {best_result['state_balance']:.3f}")
    print(f"📈 收益率分离度: {best_result['return_separation']:.6f}")
    
    print(f"\n📈 状态特征分析:")
    print(state_analysis.to_string(index=False))
    
    print(f"\n🎯 增强5状态的核心优势:")
    print(f"✅ 精细化市场分类：强弱区分更明确")
    print(f"✅ 更好的趋势捕捉能力")
    print(f"✅ 适合复杂交易策略制定")
    print(f"✅ 更高的市场适应性")
    
    # 交易建议
    processor = best_result['processor']
    test_states = best_result['test_states']
    
    print(f"\n💡 基于5状态的精细化交易策略:")
    for state in range(5):
        count = np.sum(test_states == state)
        percentage = 100 * count / len(test_states)
        state_name = processor.state_mapping.get(state, f'状态{state}')
        
        if '强上涨' in state_name:
            advice = "积极加仓策略"
        elif '弱上涨' in state_name:
            advice = "谨慎持仓策略"
        elif '强下跌' in state_name:
            advice = "快速止损策略"
        elif '弱下跌' in state_name:
            advice = "逐步减仓策略"
        else:
            advice = "中性观望策略"
            
        print(f"• {state_name}: {count}次 ({percentage:.1f}%) → {advice}")
    
    # 保存结果
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    state_analysis.to_csv(f'enhanced_5state_analysis_{timestamp}.csv', index=False)
    
    print(f"\n✅ 分析完成!")
    print(f"📄 详细数据已保存: enhanced_5state_analysis_{timestamp}.csv")
    print(f"📊 图表已保存: enhanced_5state_hmm_analysis_{timestamp}.png")
    
    return best_result, state_analysis

if __name__ == "__main__":
    # 运行分析
    result, analysis = main() 