#!/usr/bin/env python3
"""
经典3状态HMM黄金1分钟数据分析 - 专门优化版
==========================================

基于V2.1版本的优秀架构，专门针对最经典的3状态进行深度优化：
1. 上涨趋势 - 明确的上升动量
2. 震荡整理 - 横盘或小幅波动  
3. 下跌趋势 - 明确的下降动量

优化策略：
- 保留V2.1的精简特征工程
- 专门调优3状态的分类阈值
- 增强状态稳定性和持续时间
- 提供更直观的交易信号

基于参考：optimized_hmm_1min_analysis.py V2.1 (最优版本)
"""

import numpy as np
import pandas as pd
import warnings
from datetime import datetime
import matplotlib.pyplot as plt
from sklearn.preprocessing import StandardScaler, RobustScaler
from sklearn.metrics import classification_report, confusion_matrix
from hmmlearn.hmm import GaussianHMM
import seaborn as sns
from typing import Tuple, Dict, List
import talib

# 配置中文字体和警告
warnings.filterwarnings('ignore')
plt.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def RSI(series, period=14):
    """计算相对强弱指数(RSI)"""
    try:
        if hasattr(series, 'to_numpy'):
            prices = pd.Series(series)
        else:
            prices = pd.Series(series)
        
        delta = prices.diff()
        gains = delta.where(delta > 0, 0)
        losses = -delta.where(delta < 0, 0)
        
        avg_gains = gains.rolling(window=period, min_periods=1).mean()
        avg_losses = losses.rolling(window=period, min_periods=1).mean()
        
        rs = avg_gains / avg_losses
        rsi = 100 - (100 / (1 + rs))
        rsi = rsi.fillna(50)
        
        return rsi.values
    except Exception as e:
        return np.full(len(series), 50.0)

class KalmanFilter:
    """卡尔曼滤波器实现"""
    
    def __init__(self, transition_matrices=None, observation_matrices=None,
                 initial_state_mean=None, n_dim_state=2):
        self.n_dim_state = n_dim_state
        
        if transition_matrices is None:
            self.transition_matrices = np.eye(n_dim_state)
        else:
            self.transition_matrices = transition_matrices
            
        if observation_matrices is None:
            self.observation_matrices = np.eye(1, n_dim_state)
        else:
            self.observation_matrices = observation_matrices
            
        if initial_state_mean is None:
            self.initial_state_mean = np.zeros(n_dim_state)
        else:
            self.initial_state_mean = initial_state_mean
            
        self.transition_covariance = np.eye(n_dim_state) * 0.01
        self.observation_covariance = np.array([[1.0]])
        self.initial_state_covariance = np.eye(n_dim_state)
        
    def filter(self, observations):
        """执行卡尔曼滤波"""
        n_observations = len(observations)
        state_estimates = np.zeros((n_observations, self.n_dim_state))
        state_covariances = np.zeros((n_observations, self.n_dim_state, self.n_dim_state))
        
        current_state = self.initial_state_mean.copy()
        current_covariance = self.initial_state_covariance.copy()
        
        for t in range(n_observations):
            predicted_state = self.transition_matrices @ current_state
            predicted_covariance = (self.transition_matrices @ current_covariance @ 
                                   self.transition_matrices.T + self.transition_covariance)
            
            innovation = observations[t] - self.observation_matrices @ predicted_state
            innovation_covariance = (self.observation_matrices @ predicted_covariance @ 
                                   self.observation_matrices.T + self.observation_covariance)
            
            kalman_gain = (predicted_covariance @ self.observation_matrices.T @ 
                          np.linalg.inv(innovation_covariance))
            
            current_state = predicted_state + kalman_gain @ innovation
            current_covariance = ((np.eye(self.n_dim_state) - 
                                 kalman_gain @ self.observation_matrices) @ predicted_covariance)
            
            state_estimates[t] = current_state
            state_covariances[t] = current_covariance
            
        return state_estimates, state_covariances

class Classic3StateHMM:
    """
    经典3状态HMM处理器 - 专门优化上涨/震荡/下跌
    """
    
    def __init__(self, use_kalman_filter=True):
        self.model = None
        self.state_means = None
        self.scaler = RobustScaler()
        self.feature_names = []
        self.use_kalman_filter = use_kalman_filter
        
    def load_gold_data(self, file_path: str) -> pd.DataFrame:
        """加载黄金1分钟数据"""
        print("📊 加载黄金1分钟数据...")
        
        column_names = ['Datetime', 'Close', 'High', 'Low', 'Open', 'Volume']
        df = pd.read_csv(file_path, skiprows=3, names=column_names)
        df['Datetime'] = pd.to_datetime(df['Datetime'])
        df = df.set_index('Datetime')
        
        numeric_columns = ['Close', 'High', 'Low', 'Open', 'Volume']
        for col in numeric_columns:
            df[col] = pd.to_numeric(df[col], errors='coerce')
        
        df = df.dropna()
        df['Returns'] = np.log(df['Close']).diff()
        df.dropna(inplace=True)
        
        print(f"✅ 数据加载完成，共{len(df)}条记录")
        print(f"📅 时间范围: {df.index.min()} 到 {df.index.max()}")
        
        return df
    
    def apply_kalman_filter(self, data):
        """应用卡尔曼滤波器"""
        if not self.use_kalman_filter:
            data['Kalman'] = data['Close']
            return data
            
        print("🔧 应用卡尔曼滤波器...")
        
        prices = data['Close'].values.reshape(-1, 1)
        kf = KalmanFilter(n_dim_state=2)
        state_estimates, _ = kf.filter(prices)
        filtered_prices = state_estimates[:, 0]
        
        data_with_kalman = data.copy()
        data_with_kalman['Kalman'] = filtered_prices
        
        print("✅ 卡尔曼滤波完成")
        return data_with_kalman
    
    def engineer_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        经典3状态专用特征工程 - 基于V2.1优化
        """
        print("🔧 经典3状态特征工程...")
        
        features = pd.DataFrame(index=df.index)
        
        # 1. 核心特征：平滑收益率（更适合3状态分类）
        features['returns'] = df['Returns']
        features['returns_smooth'] = features['returns'].rolling(5, center=True).mean().fillna(features['returns'])  # 延长到5点
        
        # 2. 多层次波动率（3状态需要更清晰的区分）
        features['volatility_short'] = features['returns_smooth'].rolling(15).std()  # 15分钟短期
        features['volatility_long'] = features['returns_smooth'].rolling(45).std()   # 45分钟长期
        
        # 3. 趋势强度指标（3状态的关键）
        if len(df) > 30:
            features['rsi'] = RSI(df['Close'], period=25)  # 延长RSI周期
            features['rsi_norm'] = ((features['rsi'] - 50) / 50).rolling(10).mean().fillna((features['rsi'] - 50) / 50)  # 更长平滑
            
            # 价格动量（对3状态分类很重要）
            features['price_momentum'] = (df['Close'] / df['Close'].shift(10) - 1).rolling(5).mean()
        
        # 4. 成交量确认指标
        if 'Volume' in df.columns and df['Volume'].sum() > 0:
            volume_ma = df['Volume'].rolling(45).mean()  # 延长成交量窗口
            features['volume_zscore'] = ((df['Volume'] - volume_ma) / df['Volume'].rolling(45).std()).fillna(0)
            features['volume_trend'] = (features['volume_zscore'] * features['returns_smooth']).rolling(10).mean()
        else:
            features['volume_zscore'] = np.abs(features['returns_smooth'])
            features['volume_trend'] = features['volume_zscore'] * features['returns_smooth']
        
        # 5. 卡尔曼滤波趋势（3状态关键特征）
        if 'Kalman' in df.columns:
            features['kalman_trend'] = (df['Kalman'] / df['Kalman'].shift(20) - 1).rolling(5).mean()
        
        # 移除NaN并选择最适合3状态的特征
        features = features.dropna()
        
        # 专门为3状态选择的特征组合
        selected_features = ['returns_smooth', 'volatility_short', 'volatility_long']
        
        if 'rsi_norm' in features.columns:
            selected_features.append('rsi_norm')
        if 'price_momentum' in features.columns:
            selected_features.append('price_momentum')
        if 'volume_trend' in features.columns:
            selected_features.append('volume_trend')
        
        features = features[selected_features]
        
        print(f"✅ 经典3状态特征工程完成，特征: {list(features.columns)}")
        return features
    
    def train_3state_hmm(self, features: pd.DataFrame):
        """专门训练3状态HMM模型"""
        print("🧠 训练经典3状态HMM模型（上涨/震荡/下跌）...")
        
        # 标准化特征
        scaled_features = self.scaler.fit_transform(features)
        self.feature_names = list(features.columns)
        
        # 训练HMM模型 - 为3状态专门优化参数
        self.model = GaussianHMM(n_components=3, 
                                covariance_type="full", 
                                n_iter=2500,  # 增加迭代次数确保收敛
                                random_state=42,
                                algorithm='viterbi',
                                tol=1e-6)  # 更严格的收敛条件
        
        self.model.fit(scaled_features)
        
        # 预测状态
        states = self.model.predict(scaled_features)
        
        # 分析状态特征并进行经典3状态映射
        state_analysis = []
        for i in range(3):
            state_mask = states == i
            if np.sum(state_mask) > 0:
                state_features = features[state_mask]
                avg_return = state_features['returns_smooth'].mean()
                volatility = state_features['returns_smooth'].std()
                
                # 计算趋势强度
                if 'price_momentum' in state_features.columns:
                    momentum = state_features['price_momentum'].mean()
                else:
                    momentum = avg_return * 1000  # 放大以便分析
                
                frequency = np.sum(state_mask) / len(states)
                
                state_analysis.append({
                    'state': i,
                    'avg_return': avg_return,
                    'volatility': volatility,
                    'momentum': momentum,
                    'frequency': frequency,
                    'observations': np.sum(state_mask)
                })
                
                print(f"状态{i}: 收益={avg_return:.6f}, 波动={volatility:.6f}, 动量={momentum:.6f}, 频率={frequency:.1%}")
        
        # 经典3状态映射：基于收益率进行清晰分类
        state_analysis.sort(key=lambda x: x['avg_return'])
        
        self.state_mapping = {}
        
        # 基于收益率的三分位数进行分类，确保状态清晰
        low_state = state_analysis[0]
        mid_state = state_analysis[1] 
        high_state = state_analysis[2]
        
        # 更严格的阈值，确保状态区分度
        if low_state['avg_return'] < -0.000003:  # 明确下跌
            self.state_mapping[low_state['state']] = '下跌趋势'
        else:
            self.state_mapping[low_state['state']] = '震荡偏弱'
            
        # 中间状态：震荡为主
        if abs(mid_state['avg_return']) < 0.000008:  # 接近平衡
            self.state_mapping[mid_state['state']] = '震荡整理'
        else:
            self.state_mapping[mid_state['state']] = '震荡整理'
            
        # 高收益状态
        if high_state['avg_return'] > 0.000003:  # 明确上涨
            self.state_mapping[high_state['state']] = '上涨趋势'
        else:
            self.state_mapping[high_state['state']] = '震荡偏强'
        
        # 特殊处理：确保至少有一个明确的上涨和下跌状态
        returns = [sa['avg_return'] for sa in state_analysis]
        if max(returns) > 0:
            max_idx = np.argmax(returns)
            self.state_mapping[state_analysis[max_idx]['state']] = '上涨趋势'
        
        if min(returns) < 0:
            min_idx = np.argmin(returns)
            self.state_mapping[state_analysis[min_idx]['state']] = '下跌趋势'
        
        print(f"✅ 经典3状态HMM模型训练完成")
        print(f"📊 状态映射: {self.state_mapping}")
        
        return states, state_analysis
    
    def predict_states(self, features: pd.DataFrame):
        """预测新数据的状态"""
        scaled_features = self.scaler.transform(features[self.feature_names])
        states = self.model.predict(scaled_features)
        state_probs = self.model.predict_proba(scaled_features)
        return states, state_probs
    
    def get_state_characteristics(self, features: pd.DataFrame, states: np.ndarray):
        """获取状态特征分析"""
        unique_states = np.unique(states)
        analysis = []
        
        for state in unique_states:
            mask = states == state
            state_data = features[mask]
            
            char = {
                'State': self.state_mapping.get(state, f'状态{state}'),
                'Frequency': f"{np.sum(mask)}/{len(states)} ({100*np.sum(mask)/len(states):.1f}%)",
                'Avg_Return': state_data['returns_smooth'].mean() if 'returns_smooth' in state_data.columns else 0,
                'Volatility': state_data['returns_smooth'].std() if 'returns_smooth' in state_data.columns else 0,
                'Duration_Avg': self._calculate_avg_duration(states, state)
            }
            analysis.append(char)
        
        return pd.DataFrame(analysis)
    
    def _calculate_avg_duration(self, states, target_state):
        """计算状态平均持续时间"""
        durations = []
        current_duration = 0
        
        for state in states:
            if state == target_state:
                current_duration += 1
            else:
                if current_duration > 0:
                    durations.append(current_duration)
                current_duration = 0
        
        if current_duration > 0:
            durations.append(current_duration)
        
        return np.mean(durations) if durations else 0

def comprehensive_3state_validation(train_data: pd.DataFrame, test_data: pd.DataFrame) -> Dict:
    """
    全面的3状态模型验证 - 对比不同参数配置
    """
    print("\n🔄 全面3状态模型验证...")
    
    configurations = [
        {'kalman': True, 'name': '3状态+卡尔曼滤波'},
        {'kalman': False, 'name': '3状态+原始数据'},
    ]
    
    results = {}
    
    for config in configurations:
        print(f"\n🧪 测试 {config['name']}...")
        
        # 创建处理器
        processor = Classic3StateHMM(use_kalman_filter=config['kalman'])
        
        # 数据处理
        if config['kalman']:
            train_filtered = processor.apply_kalman_filter(train_data)
            test_filtered = processor.apply_kalman_filter(test_data)
        else:
            train_filtered = train_data.copy()
            test_filtered = test_data.copy()
        
        # 特征工程
        train_features = processor.engineer_features(train_filtered)
        test_features = processor.engineer_features(test_filtered)
        
        # 确保特征对齐
        common_features = train_features.columns.intersection(test_features.columns)
        train_features = train_features[common_features]
        test_features = test_features[common_features]
        
        # 训练模型
        train_states, state_analysis = processor.train_3state_hmm(train_features)
        
        # 测试预测
        test_states, test_probs = processor.predict_states(test_features)
        
        # 评估指标
        log_likelihood = processor.model.score(processor.scaler.transform(test_features))
        
        # 稳定性分析
        state_changes = np.sum(np.diff(test_states) != 0)
        avg_duration = len(test_states) / (state_changes + 1)
        
        # 3状态特有的评估：状态分布均衡性
        state_counts = [np.sum(test_states == i) for i in range(3)]
        state_balance = 1 - np.std(state_counts) / np.mean(state_counts)  # 均衡性指标
        
        # 综合评分（针对3状态优化）
        stability_score = min(avg_duration / 20.0, 1.0) * 30  # 稳定性（权重30%）
        likelihood_score = max(0, (log_likelihood + 50000) / 1000) * 25  # 似然性（权重25%）
        balance_score = state_balance * 25  # 均衡性（权重25%）
        simplicity_score = 20  # 简洁性奖励（权重20%）
        
        final_score = stability_score + likelihood_score + balance_score + simplicity_score
        
        results[config['name']] = {
            'processor': processor,
            'log_likelihood': log_likelihood,
            'avg_duration': avg_duration,
            'state_changes': state_changes,
            'state_balance': state_balance,
            'final_score': final_score,
            'train_states': train_states,
            'test_states': test_states,
            'train_features': train_features,
            'test_features': test_features,
            'state_analysis': state_analysis
        }
        
        print(f"📊 {config['name']}结果:")
        print(f"   对数似然: {log_likelihood:.2f}")
        print(f"   平均持续时间: {avg_duration:.1f}分钟")
        print(f"   状态均衡性: {state_balance:.3f}")
        print(f"   综合评分: {final_score:.1f}/100")
    
    return results

def visualize_3state_results(results: Dict, test_data: pd.DataFrame):
    """可视化3状态结果"""
    print("\n📊 生成3状态分析图表...")
    
    # 选择最佳配置
    best_config = max(results.items(), key=lambda x: x[1]['final_score'])
    best_name, best_result = best_config
    processor = best_result['processor']
    test_states = best_result['test_states']
    test_features = best_result['test_features']
    
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    fig.suptitle(f'🏆 经典3状态HMM分析 - {best_name}', fontsize=16, fontweight='bold')
    
    # 对齐数据
    aligned_test_data = test_data.loc[test_features.index]
    
    # 1. 价格序列与状态
    ax1 = axes[0, 0]
    colors = ['red', 'gray', 'green']  # 下跌、震荡、上涨
    state_names = ['下跌趋势', '震荡整理', '上涨趋势']
    
    time_indices = np.arange(len(aligned_test_data))
    for state in range(3):
        mask = test_states == state
        if np.sum(mask) > 0:
            actual_state_name = processor.state_mapping.get(state, f'状态{state}')
            ax1.scatter(time_indices[mask], 
                       aligned_test_data['Close'].values[mask],
                       c=colors[state], alpha=0.7, s=8, label=actual_state_name)
    
    ax1.plot(range(len(aligned_test_data)), aligned_test_data['Close'], 
             color='black', alpha=0.3, linewidth=0.5)
    ax1.set_title('💰 价格与3状态识别')
    ax1.set_xlabel('时间点')
    ax1.set_ylabel('黄金价格 ($)')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 2. 状态分布饼图
    ax2 = axes[0, 1]
    state_counts = {}
    for state in range(3):
        count = np.sum(test_states == state)
        state_name = processor.state_mapping.get(state, f'状态{state}')
        state_counts[state_name] = count
    
    ax2.pie(state_counts.values(), labels=state_counts.keys(), autopct='%1.1f%%',
            colors=colors)
    ax2.set_title('📊 3状态分布')
    
    # 3. 收益率分布对比
    ax3 = axes[0, 2]
    for state in range(3):
        mask = test_states == state
        if np.sum(mask) > 0:
            state_name = processor.state_mapping.get(state, f'状态{state}')
            returns_data = test_features.loc[mask, 'returns_smooth'] * 100
            ax3.hist(returns_data, alpha=0.6, bins=30, 
                    color=colors[state], label=state_name, density=True)
    
    ax3.set_title('📈 各状态收益率分布')
    ax3.set_xlabel('收益率 (%)')
    ax3.set_ylabel('密度')
    ax3.legend()
    ax3.grid(True, alpha=0.3)
    
    # 4. 状态转移矩阵
    ax4 = axes[1, 0]
    transition_matrix = np.zeros((3, 3))
    for i in range(len(test_states) - 1):
        transition_matrix[test_states[i], test_states[i + 1]] += 1
    
    # 归一化
    for i in range(3):
        if transition_matrix[i].sum() > 0:
            transition_matrix[i] = transition_matrix[i] / transition_matrix[i].sum()
    
    state_labels = [processor.state_mapping.get(i, f'状态{i}') for i in range(3)]
    sns.heatmap(transition_matrix, annot=True, fmt='.3f', cmap='Blues', ax=ax4,
                xticklabels=state_labels, yticklabels=state_labels)
    ax4.set_title('🔄 状态转移概率')
    
    # 5. 状态持续时间
    ax5 = axes[1, 1]
    durations = {state: [] for state in range(3)}
    current_state = test_states[0]
    duration = 1
    
    for i in range(1, len(test_states)):
        if test_states[i] == current_state:
            duration += 1
        else:
            durations[current_state].append(duration)
            current_state = test_states[i]
            duration = 1
    durations[current_state].append(duration)
    
    for state in range(3):
        if durations[state]:
            state_name = processor.state_mapping.get(state, f'状态{state}')
            ax5.hist(durations[state], alpha=0.6, bins=20,
                    color=colors[state], label=state_name, density=True)
    
    ax5.set_title('⏱️ 状态持续时间分布')
    ax5.set_xlabel('持续时间 (分钟)')
    ax5.set_ylabel('密度')
    ax5.legend()
    ax5.grid(True, alpha=0.3)
    
    # 6. 配置对比
    ax6 = axes[1, 2]
    config_names = list(results.keys())
    scores = [results[name]['final_score'] for name in config_names]
    durations = [results[name]['avg_duration'] for name in config_names]
    
    x = np.arange(len(config_names))
    width = 0.35
    
    ax6_twin = ax6.twinx()
    bars1 = ax6.bar(x - width/2, scores, width, label='综合评分', color='lightblue')
    bars2 = ax6_twin.bar(x + width/2, durations, width, label='持续时间', color='lightcoral')
    
    ax6.set_xlabel('配置')
    ax6.set_ylabel('综合评分', color='blue')
    ax6_twin.set_ylabel('平均持续时间(分钟)', color='red')
    ax6.set_title('⚖️ 配置对比')
    ax6.set_xticks(x)
    ax6.set_xticklabels(config_names, rotation=45)
    
    # 添加数值标签
    for bar, score in zip(bars1, scores):
        ax6.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 1,
                f'{score:.1f}', ha='center', va='bottom')
    
    for bar, duration in zip(bars2, durations):
        ax6_twin.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.5,
                     f'{duration:.1f}', ha='center', va='bottom')
    
    plt.tight_layout()
    
    # 保存图表
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f'classic_3state_hmm_analysis_{timestamp}.png'
    plt.savefig(filename, dpi=300, bbox_inches='tight')
    plt.show()
    
    print(f"✅ 图表已保存为: {filename}")
    
    return processor.get_state_characteristics(test_features, test_states)

def main():
    """主函数"""
    print("🚀 经典3状态HMM黄金分析 - 专门优化版")
    print("=" * 70)
    print("验证最简单直观的3状态分类：上涨、震荡、下跌")
    print("=" * 70)
    
    # 1. 加载数据
    processor = Classic3StateHMM(use_kalman_filter=True)
    gold_data = processor.load_gold_data("202507/gold_1m_20250701_20250725_132644.csv")
    
    # 2. 划分训练测试集
    train_end = pd.Timestamp('2025-07-15 23:59:59', tz='UTC')
    train_data = gold_data[gold_data.index <= train_end].copy()
    test_data = gold_data[gold_data.index > train_end].copy()
    
    print(f"📈 训练数据: {len(train_data)} 条 (7月1-15日)")
    print(f"📉 测试数据: {len(test_data)} 条 (7月16-25日)")
    
    # 3. 全面验证
    results = comprehensive_3state_validation(train_data, test_data)
    
    # 4. 可视化结果
    state_analysis = visualize_3state_results(results, test_data)
    
    # 5. 输出分析报告
    best_config = max(results.items(), key=lambda x: x[1]['final_score'])
    best_name, best_result = best_config
    
    print(f"\n📋 经典3状态分析结果:")
    print("=" * 70)
    print(f"🏆 最佳配置: {best_name}")
    print(f"📊 综合评分: {best_result['final_score']:.1f}/100")
    print(f"⏱️ 平均持续时间: {best_result['avg_duration']:.1f}分钟")
    print(f"🔄 状态切换次数: {best_result['state_changes']}")
    print(f"⚖️ 状态均衡性: {best_result['state_balance']:.3f}")
    
    print(f"\n📈 状态特征分析:")
    print(state_analysis.to_string(index=False))
    
    print(f"\n🎯 经典3状态的核心优势:")
    print(f"✅ 最直观的市场分类：上涨、震荡、下跌")
    print(f"✅ 简单易懂的交易信号")
    print(f"✅ 适合所有投资者理解")
    print(f"✅ 经典技术分析理论基础")
    
    # 交易建议
    processor = best_result['processor']
    test_states = best_result['test_states']
    
    print(f"\n💡 基于3状态的交易策略:")
    for state in range(3):
        count = np.sum(test_states == state)
        percentage = 100 * count / len(test_states)
        state_name = processor.state_mapping.get(state, f'状态{state}')
        
        if '上涨' in state_name:
            advice = "持有/加仓策略"
        elif '下跌' in state_name:
            advice = "减仓/止损策略"
        else:
            advice = "观望/震荡策略"
            
        print(f"• {state_name}: {count}次 ({percentage:.1f}%) → {advice}")
    
    # 保存结果
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    state_analysis.to_csv(f'classic_3state_analysis_{timestamp}.csv', index=False)
    
    print(f"\n✅ 分析完成!")
    print(f"📄 详细数据已保存: classic_3state_analysis_{timestamp}.csv")
    print(f"📊 图表已保存: classic_3state_hmm_analysis_{timestamp}.png")
    
    return best_result, state_analysis

if __name__ == "__main__":
    # 运行分析
    result, analysis = main() 