import pandas as pd
import numpy as np
from lightweight_charts import Chart
from hmmlearn.hmm import GaussianHMM
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import accuracy_score
import os
from datetime import datetime, timezone, timedelta
import time
import threading
from typing import Optional, Dict, List, Callable, Tuple
import yfinance as yf
import warnings
warnings.filterwarnings('ignore')

class HMMTradingSystem:
    """HMM黄金期货状态识别系统 - 基于完整K线数据的实时状态监控"""
    
    def __init__(self, 
                 n_states: int = 3,
                 ma_fast: int = 20,
                 ma_slow: int = 35,
                 width: int = 1600,
                 height: int = 900):
        """
        初始化HMM交易系统
        
        Args:
            n_states: HMM状态数量
            ma_fast: 快速移动平均线周期
            ma_slow: 慢速移动平均线周期
            width: 图表宽度
            height: 图表高度
        """
        self.n_states = n_states
        self.ma_fast = ma_fast
        self.ma_slow = ma_slow
        
        # 初始化图表
        self.chart = Chart(
            width=width,
            height=height,
            toolbox=True
        )
        
        # 配置图表样式
        self._setup_chart_style()
        
        # HMM相关
        self.model: Optional[GaussianHMM] = None
        self.scaler: Optional[StandardScaler] = None
        self.state_map: Dict[int, str] = {}
        self.features = ['log_return', 'momentum_5m', 'momentum_20m', 'price_position', 'ma_diff']
        
        # 数据存储
        self.df: Optional[pd.DataFrame] = None
        self.last_processed_index = 0
        self.current_data_buffer = pd.DataFrame()  # 实时数据缓冲
        
        # 状态颜色映射
        self.state_colors = {
            '上涨': '#00C851',    # 明亮绿色
            '下跌': '#ff4444',    # 明亮红色  
            '盘整': '#ffbb33'     # 橙黄色
        }
        
        # 专注于状态识别，不显示均线
        
        # 性能指标
        self.best_params = {}
        self.retrain_frequency_hours = 24  # 默认24小时重训练一次
        
        # 回调函数
        self.on_signal_change: Optional[Callable] = None
        
        print(f"🚀 HMM状态识别系统初始化完成")
        print(f"🎯 HMM状态数: {n_states}个状态")
        print(f"📊 技术指标: MA{ma_fast}/{ma_slow}周期")
        
    def _setup_chart_style(self):
        """配置图表样式 - 简洁专业主题，专注状态识别"""
        # 基础布局
        self.chart.layout(
            background_color='#ffffff',
            text_color='#333333',
            font_size=13,
            font_family='Segoe UI, Arial, sans-serif'
        )
        
        # K线样式
        self.chart.candle_style(
            up_color='#26a69a',
            down_color='#ef5350',
            border_up_color='#26a69a',
            border_down_color='#ef5350',
            wick_up_color='#26a69a',
            wick_down_color='#ef5350'
        )
        
        # 成交量样式
        self.chart.volume_config(
            up_color='rgba(38, 166, 154, 0.2)',
            down_color='rgba(239, 83, 80, 0.2)'
        )
        
        # 十字线
        self.chart.crosshair(
            mode='normal',
            vert_color='#e0e0e0',
            vert_style='dotted',
            horz_color='#e0e0e0',
            horz_style='dotted'
        )
        
        # 水印
        self.chart.watermark('HMM状态识别系统', color='rgba(200, 200, 200, 0.3)')
        
        # 图例
        self.chart.legend(visible=False, font_size=13)
        
        # 网格线
        self.chart.grid(vert_enabled=True, horz_enabled=True, color='#f5f5f5')
    
    def download_gold_data(self, days: int = 20) -> bool:
        """下载黄金期货数据 (GC=F) - 专注单一可靠数据源"""
        print(f"📥 开始下载过去{days}天的黄金期货数据 (GC=F)...")
        
        try:
            end_date = datetime.now()
            start_date = end_date - timedelta(days=days)
            
            print("📊 数据源: COMEX黄金期货 (GC=F)")
            data = self._download_futures_data(start_date, end_date)
            
            if data is None or data.empty:
                print("❌ 期货数据获取失败")
                return False
            
            # 保存数据
            os.makedirs("data", exist_ok=True)
            timestamp = datetime.now().strftime("%Y%m%d")
            filename = f"data/GC_1m_last_{days}days_{timestamp}.parquet"
            data.to_parquet(filename)
            
            self.data_file = filename
            print(f"✅ 数据下载完成: {len(data)} 条记录")
            print(f"📅 时间范围: {data.index[0].strftime('%m-%d %H:%M')} ~ {data.index[-1].strftime('%m-%d %H:%M')}")
            print(f"💾 已保存至: {filename}")
            
            return True
            
        except Exception as e:
            print(f"❌ 数据下载失败: {e}")
            return False
    
    def _download_futures_data(self, start_date, end_date):
        """下载期货数据 (GC=F) - 简化批次处理"""
        try:
            all_data = []
            batch_delta = timedelta(days=5)
            current_start = start_date
            batch_num = 1
            
            while current_start < end_date:
                batch_end = min(current_start + batch_delta, end_date)
                print(f"  批次 {batch_num}: {current_start.strftime('%m-%d')} -> {batch_end.strftime('%m-%d')}")
                
                try:
                    batch_data = yf.download(
                        "GC=F", 
                        start=current_start, 
                        end=batch_end, 
                        interval="1m", 
                        progress=False,
                        auto_adjust=False
                    )
                    
                    if not batch_data.empty:
                        # 处理多级列名
                        if isinstance(batch_data.columns, pd.MultiIndex):
                            batch_data.columns = [col[0] for col in batch_data.columns]
                        
                        batch_data = batch_data.dropna().sort_index()
                        all_data.append(batch_data)
                        print(f"    ✅ 获取 {len(batch_data)} 条记录")
                    else:
                        print(f"    ⚠️ 无数据")
                        
                except Exception as e:
                    print(f"    ❌ 批次失败: {e}")
                
                current_start += batch_delta
                batch_num += 1
                time.sleep(0.3)
            
            if not all_data:
                return None
                
            # 合并数据
            combined_data = pd.concat(all_data)
            combined_data = combined_data[~combined_data.index.duplicated(keep='first')].sort_index()
            
            # 成交量统计
            if 'Volume' in combined_data.columns:
                non_zero_vol = (combined_data['Volume'] > 0).sum()
                print(f"  📊 成交量统计: {non_zero_vol}/{len(combined_data)} ({non_zero_vol/len(combined_data)*100:.1f}%) 非零")
            
            return combined_data
            
        except Exception as e:
            print(f"  ❌ 期货数据获取失败: {e}")
            return None
    

    
    def load_and_prepare_data(self, data_file: str = None) -> bool:
        """加载并准备数据"""
        try:
            if data_file:
                self.data_file = data_file
            
            if not hasattr(self, 'data_file') or not os.path.exists(self.data_file):
                raise FileNotFoundError(f"数据文件未找到: {getattr(self, 'data_file', 'None')}")
            
            # 加载数据
            df = pd.read_parquet(self.data_file)
            if isinstance(df.columns, pd.MultiIndex):
                df.columns = df.columns.get_level_values(0)
            
            # 特征工程
            df['log_return'] = np.log(df['Close'] / df['Close'].shift(1))
            df['momentum_5m'] = np.log(df['Close'] / df['Close'].shift(5))
            df['momentum_20m'] = np.log(df['Close'] / df['Close'].shift(20))
            
            # 移动平均线
            df['ma_fast'] = df['Close'].rolling(window=self.ma_fast).mean()
            df['ma_slow'] = df['Close'].rolling(window=self.ma_slow).mean()
            df['price_position'] = (df['Close'] - df['ma_slow']) / df['ma_slow']
            df['ma_diff'] = (df['ma_fast'] - df['ma_slow']) / df['ma_slow']
            
            # 清理数据
            df.dropna(inplace=True)
            
            # 时间格式转换
            if df.index.tz is None:
                df.index = df.index.tz_localize('UTC')
            
            self.df = df
            print(f"✅ 数据加载成功: {df.shape[0]} 条记录")
            
            return True
            
        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            return False
    
    def walk_forward_analysis(self, min_train_ratio: float = 0.6, max_train_ratio: float = 0.8, step: float = 0.05) -> dict:
        """前向展开分析优化训练窗口"""
        print(f"\n🔍 开始前向展开分析...")
        
        if self.df is None:
            print("❌ 请先加载数据")
            return {}
        
        results = []
        train_ratios = np.arange(min_train_ratio, max_train_ratio + step, step)
        
        for train_ratio in train_ratios:
            print(f"  测试训练比例: {train_ratio:.2f}")
            
            try:
                # 分割数据
                split_idx = int(len(self.df) * train_ratio)
                train_data = self.df.iloc[:split_idx]
                test_data = self.df.iloc[split_idx:]
                
                if len(test_data) < 100:  # 确保测试集足够大
                    continue
                
                # 训练模型
                X_train = train_data[self.features].values
                scaler = StandardScaler()
                X_train_scaled = scaler.fit_transform(X_train)
                
                model = GaussianHMM(
                    n_components=self.n_states,
                    covariance_type="diag",
                    n_iter=300,
                    random_state=42,
                    tol=1e-4
                )
                model.fit(X_train_scaled)
                
                # 状态映射
                state_means = pd.DataFrame(model.means_, columns=self.features)
                sorted_by_return = state_means.sort_values('log_return')
                
                state_map = {
                    sorted_by_return.index[0]: "下跌",
                    sorted_by_return.index[-1]: "上涨"
                }
                if self.n_states == 3:
                    state_map[sorted_by_return.index[1]] = "盘整"
                
                # 测试集预测
                X_test = test_data[self.features].values
                X_test_scaled = scaler.transform(X_test)
                
                test_states = model.predict(X_test_scaled)
                test_regimes = [state_map[s] for s in test_states]
                
                # 计算收益
                signal_map = {"上涨": 1, "下跌": -1, "盘整": 0}
                signals = [signal_map[r] for r in test_regimes]
                
                test_returns = test_data['log_return'].values[1:]  # 滞后一期
                strategy_returns = test_returns * np.array(signals[:-1])
                
                # 性能指标
                sharpe_ratio = np.mean(strategy_returns) / np.std(strategy_returns) * np.sqrt(252*24*60) if np.std(strategy_returns) > 0 else 0
                max_drawdown = self._calculate_max_drawdown(strategy_returns)
                total_return = np.exp(np.sum(strategy_returns)) - 1
                
                results.append({
                    'train_ratio': train_ratio,
                    'sharpe_ratio': sharpe_ratio,
                    'max_drawdown': max_drawdown,
                    'total_return': total_return,
                    'test_days': len(test_data) / (24 * 60)  # 测试天数
                })
                
                print(f"    夏普比率: {sharpe_ratio:.2f}, 最大回撤: {max_drawdown:.2%}, 收益: {total_return:.2%}")
                
            except Exception as e:
                print(f"    ❌ 训练比例 {train_ratio:.2f} 失败: {e}")
                continue
        
        if not results:
            print("❌ 前向展开分析失败")
            return {}
        
        # 选择最优参数
        results_df = pd.DataFrame(results)
        
        # 综合评分：夏普比率权重70%，回撤权重30%
        results_df['score'] = results_df['sharpe_ratio'] * 0.7 - results_df['max_drawdown'] * 0.3
        best_result = results_df.loc[results_df['score'].idxmax()]
        
        self.best_params = best_result.to_dict()
        
        print(f"\n🎯 最优训练窗口分析结果:")
        print(f"  最佳训练比例: {best_result['train_ratio']:.2f}")
        print(f"  样本外夏普比率: {best_result['sharpe_ratio']:.2f}")
        print(f"  样本外最大回撤: {best_result['max_drawdown']:.2%}")
        print(f"  样本外总收益: {best_result['total_return']:.2%}")
        print(f"  测试天数: {best_result['test_days']:.1f}天")
        
        # 计算建议的重训练频率
        test_period_hours = best_result['test_days'] * 24
        self.retrain_frequency_hours = max(12, min(72, test_period_hours / 3))  # 12-72小时之间
        
        print(f"💡 建议重训练频率: 每 {self.retrain_frequency_hours:.0f} 小时")
        
        return self.best_params
    
    def _calculate_max_drawdown(self, returns: np.array) -> float:
        """计算最大回撤"""
        cumulative = np.exp(np.cumsum(returns))
        running_max = np.maximum.accumulate(cumulative)
        drawdown = (cumulative - running_max) / running_max
        return abs(drawdown.min())
    
    def train_optimal_model(self) -> bool:
        """使用最优参数训练模型"""
        if self.df is None:
            print("❌ 请先加载数据")
            return False
        
        try:
            # 使用最优训练比例
            train_ratio = self.best_params.get('train_ratio', 0.75)
            split_idx = int(len(self.df) * train_ratio)
            train_data = self.df.iloc[:split_idx]
            
            # 准备训练特征
            X_train = train_data[self.features].values
            
            # 标准化
            self.scaler = StandardScaler()
            X_train_scaled = self.scaler.fit_transform(X_train)
            
            # 训练HMM
            self.model = GaussianHMM(
                n_components=self.n_states,
                covariance_type="diag",
                n_iter=500,
                random_state=42,
                tol=1e-4
            )
            self.model.fit(X_train_scaled)
            
            # 识别状态映射
            self._identify_state_mapping()
            
            print(f"✅ 最优模型训练完成")
            print(f"📊 使用训练比例: {train_ratio:.2f}")
            print(f"📊 BIC分数: {self.model.bic(X_train_scaled):.2f}")
            
            return True
            
        except Exception as e:
            print(f"❌ 模型训练失败: {e}")
            return False
    
    def _identify_state_mapping(self):
        """识别状态映射"""
        state_means = pd.DataFrame(self.model.means_, columns=self.features)
        
        # 按收益率排序识别状态
        sorted_by_return = state_means.sort_values('log_return')
        
        bear_state = sorted_by_return.index[0]
        bull_state = sorted_by_return.index[-1] 
        neutral_state = sorted_by_return.index[1] if self.n_states == 3 else None
        
        self.state_map = {
            bear_state: "下跌",
            bull_state: "上涨"
        }
        
        if neutral_state is not None:
            self.state_map[neutral_state] = "盘整"
        
        print("🎯 状态映射:")
        for state, regime in self.state_map.items():
            ret = state_means.loc[state, 'log_return']
            ma = state_means.loc[state, 'ma_diff']
            print(f"  状态{state} -> {regime} (收益:{ret:.4f}, 均线:{ma:.4f})")
    
    def predict_states(self, start_idx: int = 0) -> pd.DataFrame:
        """预测状态序列"""
        if self.model is None or self.scaler is None:
            raise ValueError("请先训练模型")
        
        # 预测状态
        data_subset = self.df.iloc[start_idx:]
        X = data_subset[self.features].values
        X_scaled = self.scaler.transform(X)
        
        states = self.model.predict(X_scaled)
        
        # 生成结果DataFrame
        result_df = data_subset.copy()
        result_df['state'] = states
        result_df['regime'] = result_df['state'].map(self.state_map)
        
        # 交易信号
        signal_map = {"上涨": 1, "下跌": -1, "盘整": 0}
        result_df['signal'] = result_df['regime'].map(signal_map).shift(1).fillna(0)
        
        return result_df
    
    def display_recent_analysis(self, hours_back: int = 48):  # 改为48小时
        """显示最近的交易分析"""
        if self.df is None:
            print("❌ 请先加载数据")
            return
        
        # 获取最近48小时数据
        recent_data = self.df.tail(hours_back * 60)  # 1分钟数据
        
        # 预测状态
        pred_df = self.predict_states(len(self.df) - len(recent_data))
        pred_recent = pred_df.tail(len(recent_data))
        
        # 准备K线数据 - 保持原始pandas datetime index格式
        chart_data = pred_recent[['Open', 'High', 'Low', 'Close']].copy()
        chart_data.columns = ['open', 'high', 'low', 'close']  # 标准化列名
        if 'Volume' in pred_recent.columns:
            chart_data['volume'] = pred_recent['Volume']
        else:
            chart_data['volume'] = 1000  # 默认成交量
        
        # 设置数据
        self.chart.set(chart_data)
        
        # 添加状态变化标记
        self._add_regime_markers(pred_recent)
        
        self.last_processed_index = len(self.df)
        
        print(f"📊 显示最近{hours_back}小时分析，包含 {len(chart_data)} 根K线")
    
    def _add_regime_markers(self, df: pd.DataFrame):
        """添加状态变化标记 - 更突出的状态显示"""
        signal_changes = df['signal'] != df['signal'].shift(1)
        
        # 在信号变化点添加明显标记
        for idx, row in df[signal_changes].iterrows():
            signal = row['signal']
            regime = row['regime']
            
            try:
                if signal == 1:  # 买入信号
                    self.chart.marker(
                        time=idx,  # 直接使用pandas datetime index
                        position='below',
                        shape='arrow_up',
                        color=self.state_colors['上涨'],
                        text=f'{regime}'
                    )
                elif signal == -1:  # 卖出信号
                    self.chart.marker(
                        time=idx,
                        position='above', 
                        shape='arrow_down',
                        color=self.state_colors['下跌'],
                        text=f'{regime}'
                    )
                elif signal == 0 and regime == '盘整':  # 盘整信号
                    self.chart.marker(
                        time=idx,
                        position='below',
                        shape='circle',
                        color=self.state_colors['盘整'],
                        text=f'{regime}'
                    )
            except Exception as e:
                # 静默处理标记错误，不影响主要功能
                print(f"⚠️ 状态标记添加失败 ({regime}): {e}")
                continue
    
    def start_realtime_monitoring(self, update_interval_minutes: int = 1, max_updates: int = 1440):
        """启动实时监控 - 智能K线数据获取策略"""
        print(f"\n🔄 启动实时监控 (每{update_interval_minutes}分钟更新)")
        print(f"💡 智能K线策略：优先获取最新数据，确保时间连续性")
        print(f"💡 将连续监控{max_updates//60}小时")
        
        def fetch_latest_data():
            """获取最新K线数据 - 智能选择策略"""
            return self._fetch_complete_futures_data()
        
        def update_chart_with_new_data(new_data_row, current_time):
            """更新图表with新数据 - 改进时间处理"""
            if self.model is None or self.scaler is None:
                return
            
            try:
                # 确保数据为标量值
                close_price = float(new_data_row['Close'])
                open_price = float(new_data_row['Open'])
                high_price = float(new_data_row['High'])
                low_price = float(new_data_row['Low'])
                
                # 安全处理Volume字段
                try:
                    if 'Volume' in new_data_row.index:
                        volume_val = new_data_row['Volume']
                        volume = float(volume_val) if pd.notna(volume_val) and volume_val > 0 else 1.0
                    else:
                        volume = 1.0
                except:
                    volume = 1.0
                
                # 计算技术指标（基于历史数据）
                historical_prices = [float(x) for x in self.df['Close'].tail(self.ma_slow).values]
                recent_prices = historical_prices + [close_price]
                
                # 计算特征 - 确保所有值都是标量
                if len(recent_prices) >= 2:
                    log_return = float(np.log(recent_prices[-1] / recent_prices[-2]))
                else:
                    log_return = 0.0
                
                if len(recent_prices) >= 6:
                    momentum_5m = float(np.log(recent_prices[-1] / recent_prices[-6]))
                else:
                    momentum_5m = 0.0
                    
                if len(recent_prices) >= 21:
                    momentum_20m = float(np.log(recent_prices[-1] / recent_prices[-21]))
                    ma_fast = float(np.mean(recent_prices[-self.ma_fast:]))
                    ma_slow = float(np.mean(recent_prices[-self.ma_slow:]))
                    price_position = float((recent_prices[-1] - ma_slow) / ma_slow)
                    ma_diff = float((ma_fast - ma_slow) / ma_slow)
                else:
                    momentum_20m = 0.0
                    price_position = 0.0
                    ma_diff = 0.0
                
                # 构建特征向量
                features = np.array([[log_return, momentum_5m, momentum_20m, price_position, ma_diff]], dtype=np.float64)
                
                # 标准化并预测
                features_scaled = self.scaler.transform(features)
                state = self.model.predict(features_scaled)[0]
                regime = self.state_map[state]
                
                # 生成交易信号
                signal_map = {"上涨": 1, "下跌": -1, "盘整": 0}
                signal = signal_map[regime]
                
                # 检查时间连续性 - 关键修复
                try:
                    # 确保时间戳格式正确
                    if hasattr(current_time, 'timestamp'):
                        chart_time = current_time
                    else:
                        chart_time = pd.Timestamp(current_time)
                    
                    # 检查是否比上次更新的时间更新
                    if hasattr(self, '_last_chart_time'):
                        if chart_time <= self._last_chart_time:
                            print(f"⏭️ 跳过过时数据: {chart_time.strftime('%H:%M:%S')} <= {self._last_chart_time.strftime('%H:%M:%S')}")
                            return
                    
                    # 更新图表
                    update_data = pd.Series({
                        'time': chart_time,
                        'open': open_price,
                        'high': high_price,
                        'low': low_price,
                        'close': close_price,
                        'volume': volume
                    })
                    
                    self.chart.update(update_data)
                    self._last_chart_time = chart_time  # 记录最后更新时间
                    print(f"📊 图表已更新: ${close_price:.2f} (Vol: {volume:.0f}) [{chart_time.strftime('%H:%M:%S')}]")
                    
                except Exception as e:
                    print(f"⚠️ 图表更新失败: {e}")
                    # 继续执行，不影响状态识别
                
                # 检查信号变化
                last_signal = getattr(self, '_last_signal', None)
                if signal != last_signal:
                    # 状态变化提示
                    status_emoji = "🟢" if signal == 1 else "🔴" if signal == -1 else "🟡"
                    time_str = current_time.strftime('%H:%M')
                    print(f"{status_emoji} {time_str}: {regime} 信号 | ${close_price:.2f}")
                    
                    # 添加实时标记
                    try:
                        if signal == 1:
                            self.chart.marker(
                                position='below',
                                shape='arrow_up', 
                                color=self.state_colors['上涨'],
                                text=f'{regime}'
                            )
                        elif signal == -1:
                            self.chart.marker(
                                position='above',
                                shape='arrow_down',
                                color=self.state_colors['下跌'],
                                text=f'{regime}'
                            )
                        elif signal == 0:
                            self.chart.marker(
                                position='below',
                                shape='circle',
                                color=self.state_colors['盘整'],
                                text=f'{regime}'
                            )
                    except Exception as e:
                        print(f"⚠️ 标记添加失败: {e}")
                    
                    # 触发回调
                    if self.on_signal_change:
                        data_dict = {
                            'Close': close_price,
                            'Open': open_price,
                            'High': high_price,
                            'Low': low_price,
                            'Volume': volume
                        }
                        self.on_signal_change(signal, regime, data_dict)
                
                self._last_signal = signal
                
            except Exception as e:
                print(f"❌ 更新图表失败: {e}")
        
        def monitoring_loop():
            """
            智能监控循环:
            - 动态计算睡眠时间，对齐到每分钟的第5秒执行，确保获取完整的上一分钟K线。
            - 根本上解决了数据重复和时间戳冲突的问题。
            """
            update_count = 0
            last_processed_minute = None
            
            print("⏰ 智能实时监控已启动 (对齐时钟策略)...")

            while update_count < max_updates:
                try:
                    now = datetime.now(timezone.utc)

                    # 检查是否在交易时间
                    if now.weekday() >= 5:
                        print("📅 周末市场休市，等待下一个交易日...")
                        # 计算到下周一的睡眠时间
                        days_to_monday = (7 - now.weekday()) % 7
                        next_monday = (now + timedelta(days=days_to_monday)).replace(hour=0, minute=0, second=0, microsecond=0)
                        sleep_duration = (next_monday - now).total_seconds()
                        time.sleep(sleep_duration)
                        continue

                    # 1. 计算下一次唤醒时间 (下一个整分钟过5秒)
                    next_minute = (now + timedelta(minutes=1)).replace(second=5, microsecond=0)
                    sleep_duration = (next_minute - now).total_seconds()
                    
                    print(f"🕒 当前时间: {now.strftime('%H:%M:%S')}, 下次唤醒: {next_minute.strftime('%H:%M:%S')}, 休眠 {sleep_duration:.1f} 秒...")
                    time.sleep(sleep_duration)

                    # 2. 唤醒后，获取刚刚结束的那一分钟的数据
                    target_minute = next_minute.replace(second=0) - timedelta(minutes=1)

                    # 防止重复处理同一分钟
                    if last_processed_minute and target_minute <= last_processed_minute:
                        continue

                    # 3. 获取最新数据
                    latest_data = fetch_latest_data()
                    if latest_data is None or latest_data.empty:
                        print("⚠️ 暂时无法获取最新数据，等待下次尝试...")
                        continue
                    
                    # 4. 在获取的数据中精确查找目标分钟的K线
                    target_bar = latest_data[latest_data.index == target_minute]

                    if not target_bar.empty:
                        bar_data = target_bar.iloc[0]
                        
                        # 5. 更新图表和状态
                        update_chart_with_new_data(bar_data, target_minute)
                        last_processed_minute = target_minute
                        
                        # 显示数据信息
                        close_price = float(bar_data['Close'])
                        volume = float(bar_data['Volume']) if pd.notna(bar_data['Volume']) else 0
                        time_str = target_minute.strftime('%H:%M')
                        print(f"📊 {time_str}: ${close_price:.2f} (Vol: {volume:.0f}) [完整K线]")
                    else:
                        print(f"❓ 未找到 {target_minute.strftime('%H:%M')} 的K线数据，可能市场休市或无交易。")

                    update_count += 1

                except KeyboardInterrupt:
                    print("\n🛑 用户停止监控")
                    break
                except Exception as e:
                    print(f"❌ 监控循环发生严重错误: {e}")
                    import traceback
                    traceback.print_exc()
                    time.sleep(30) # 发生错误后短暂休眠
            
            print(f"✅ 实时监控结束，共监控 {update_count} 次")
        
        # 在后台线程运行监控
        monitor_thread = threading.Thread(target=monitoring_loop)
        monitor_thread.daemon = True
        monitor_thread.start()
        
        return monitor_thread
    
    def show_dashboard(self, block: bool = True):
        """显示交易仪表板"""
        print(f"\n📊 启动HMM状态识别仪表板")
        print(f"💡 专注状态识别: 🟢上涨 🔴下跌 🟡盘整")
        print(f"💡 提示: 关闭图表窗口结束程序")
        
        self.chart.show(block=block)
    
    def _fetch_complete_futures_data(self):
        """获取黄金期货完整K线数据 - 专注倒数第二根K线以获取准确成交量"""
        try:
            # 获取最近的数据
            latest = yf.download("GC=F", period="2d", interval="1m", 
                               progress=False, auto_adjust=False)
            
            if latest.empty:
                return None
                
            # 处理多级列名
            if isinstance(latest.columns, pd.MultiIndex):
                latest.columns = [col[0] for col in latest.columns]
            
            # 返回最近几根K线，确保有倒数第二根完整数据
            return latest.tail(10).dropna() if len(latest) >= 10 else latest.dropna()
            
        except Exception as e:
            # print(f"❌ 获取期货数据失败: {e}")  # 静默处理错误，避免刷屏
            return None
    
    def set_signal_callback(self, callback: Callable):
        """设置信号变化回调函数"""
        self.on_signal_change = callback

def main():
    """主程序 - HMM黄金期货状态识别系统"""
    print("=" * 80)
    print("🚀 HMM黄金期货状态识别系统")
    print("📈 基于完整K线数据的实时状态监控")
    print("=" * 80)
    
    # 初始化系统
    system = HMMTradingSystem(
        n_states=3,
        ma_fast=20,
        ma_slow=35
    )
    
    # 设置信号回调
    def on_signal_change(signal, regime, data_dict):
        action = "买入" if signal == 1 else "卖出" if signal == -1 else "观望"
        close_price = data_dict['Close']
        print(f"🎯 交易提示: {action} | {regime} | ${close_price:.2f}")
    
    system.set_signal_callback(on_signal_change)
    
    try:
        # 步骤1: 下载数据
        if not system.download_gold_data(days=20):
            print("❌ 数据下载失败，退出程序")
            return
        
        # 步骤2: 加载和准备数据
        if not system.load_and_prepare_data():
            print("❌ 数据准备失败，退出程序")
            return
        
        # 步骤3: 前向展开分析优化参数
        best_params = system.walk_forward_analysis()
        if not best_params:
            print("❌ 参数优化失败，使用默认参数")
        
        # 步骤4: 训练最优模型
        if not system.train_optimal_model():
            print("❌ 模型训练失败，退出程序")
            return
        
        # 步骤5: 显示最近48小时分析
        system.display_recent_analysis(hours_back=48)
        
        # 步骤6: 启动实时监控
        monitor_thread = system.start_realtime_monitoring(
            update_interval_minutes=1, 
            max_updates=1440  # 24小时监控
        )
        
        # 步骤7: 显示交易仪表板
        print(f"\n💡 建议每 {system.retrain_frequency_hours:.0f} 小时重新运行系统进行模型更新")
        system.show_dashboard(block=True)
        
    except KeyboardInterrupt:
        print("\n\n用户中断程序")
    except Exception as e:
        print(f"\n\n程序执行异常: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n✅ HMM黄金期货状态识别系统结束")

if __name__ == "__main__":
    main() 