#!/usr/bin/env python3
"""
策略一：双SMA交叉 + HMM状态识别策略
=================================

核心逻辑：
- 使用HMM识别市场状态（牛市/熊市）
- 在牛市状态下，当快速SMA > 慢速SMA时买入
- 当慢速SMA向上穿越快速SMA或进入熊市时卖出

作者：量化交易研究团队
日期：2024年
版本：1.0
"""

import numpy as np
import pandas as pd
import yfinance as yf
import matplotlib.pyplot as plt
import warnings
from datetime import datetime, timedelta

# 机器学习和回测相关
from hmmlearn.hmm import GaussianHMM
from backtesting import Backtest, Strategy
from backtesting.lib import crossover
from backtesting.test import SMA
import multiprocessing

# 忽略警告
warnings.filterwarnings('ignore')

def Extract(data, column):
    """从数据中提取指定列的函数，用于backtesting框架"""
    if hasattr(data, column):
        return getattr(data, column)
    else:
        return data[column]

def fix_multiprocessing_warnings():
    """修复multiprocessing警告"""
    try:
        if multiprocessing.get_start_method(allow_none=True) is None:
            multiprocessing.set_start_method('fork', force=True)
    except RuntimeError:
        pass
    warnings.filterwarnings('ignore', message='resource_tracker: There appear to be .* leaked .* objects to clean up at shutdown')

# 应用multiprocessing修复
fix_multiprocessing_warnings()

class HMMDataProcessor:
    """HMM数据处理和模型训练类"""
    
    def __init__(self):
        self.model = None
        self.state_means = None
        
    def download_data(self, symbol="SPY", start_date="2013-01-01", end_date=None):
        """下载股票数据并处理yfinance MultiIndex问题"""
        if end_date is None:
            end_date = datetime.now().strftime("%Y-%m-%d")
            
        print(f"下载{symbol}数据: {start_date} 到 {end_date}")
        
        # 下载数据
        data = yf.download(symbol, start=start_date, end=end_date)
        
        # 修复yfinance MultiIndex问题
        if isinstance(data.columns, pd.MultiIndex):
            data.columns = data.columns.droplevel(1)
            print("✅ 已修复yfinance MultiIndex数据结构")
        
        # 计算对数收益率
        data['Returns'] = np.log(data['Close']).diff()
        data.dropna(inplace=True)
        
        print(f"✅ 数据下载完成，共{len(data)}个交易日")
        return data
    
    def train_hmm_model(self, data, n_states=2):
        """训练HMM模型"""
        print(f"开始训练{n_states}状态HMM模型...")
        
        # 准备训练数据
        returns = data['Returns'].values.reshape(-1, 1)
        
        # 训练HMM模型
        self.model = GaussianHMM(n_components=n_states, 
                                covariance_type="full", 
                                n_iter=1000, 
                                random_state=42)
        self.model.fit(returns)
        
        # 预测状态
        states = self.model.predict(returns)
        
        # 分析状态特征
        state_means = []
        for i in range(n_states):
            state_returns = returns[states == i]
            state_mean = np.mean(state_returns)
            state_means.append(state_mean)
            print(f"状态{i}: 平均收益={state_mean:.6f}")
        
        # 确定牛市和熊市状态（收益率高的为牛市）
        bull_state = np.argmax(state_means)
        bear_state = np.argmin(state_means)
        
        # 重新映射状态：0为牛市，1为熊市
        mapped_states = np.where(states == bull_state, 0, 1)
        
        print(f"✅ HMM模型训练完成")
        print(f"   牛市状态(0): {np.sum(mapped_states == 0)}天 ({np.mean(mapped_states == 0)*100:.1f}%)")
        print(f"   熊市状态(1): {np.sum(mapped_states == 1)}天 ({np.mean(mapped_states == 1)*100:.1f}%)")
        
        return mapped_states
    
    def get_state_probabilities(self, data):
        """获取状态概率"""
        if self.model is None:
            raise ValueError("模型未训练，请先调用train_hmm_model")
        
        returns = data['Returns'].values.reshape(-1, 1)
        state_probs = self.model.predict_proba(returns)
        return state_probs

class DualSMAStrategy(Strategy):
    """双SMA交叉策略 + HMM状态识别"""
    
    # 策略参数
    n1 = 90   # 快速SMA周期
    n2 = 120  # 慢速SMA周期
    
    def init(self):
        """初始化策略指标"""
        close = self.data.Close
        
        # 计算双SMA
        self.sma1 = self.I(SMA, close, self.n1)  # 快速SMA
        self.sma2 = self.I(SMA, close, self.n2)  # 慢速SMA
        
        # 提取HMM状态
        self.state = self.I(Extract, self.data, 'State')
        
    def next(self):
        """策略交易逻辑"""
        current_state = self.state[-1]
        
        if not self.position:
            # 买入条件：快速SMA > 慢速SMA 且 牛市状态
            if (self.sma1[-1] > self.sma2[-1]) and (current_state == 0):
                self.buy()
        else:
            # 卖出条件：慢速SMA向上穿越快速SMA 或 进入熊市
            exit_conditions = [
                crossover(self.sma2, self.sma1),  # 技术信号转弱
                (current_state != 0)              # 市场状态变化
            ]
            
            if any(exit_conditions):
                self.position.close()

class StrategyOneRunner:
    """策略一运行器"""
    
    def __init__(self):
        self.data_processor = HMMDataProcessor()
        self.raw_data = None
        self.processed_data = None
        self.backtest_result = None
        
    def setup_chinese_display(self):
        """设置中文显示"""
        plt.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'SimHei', 'DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False
        print("✅ 中文显示设置完成")
    
    def run_complete_strategy(self, symbol="SPY", start_date="2013-01-01", 
                            test_start="2018-01-01", cash=10000, commission=0.0):
        """运行完整策略"""
        print("=" * 60)
        print("策略一：双SMA交叉 + HMM状态识别策略")
        print("=" * 60)
        
        # 1. 数据下载和处理
        self.raw_data = self.data_processor.download_data(symbol, start_date)
        
        # 2. HMM模型训练
        states = self.data_processor.train_hmm_model(self.raw_data)
        
        # 3. 准备回测数据
        self.processed_data = self.raw_data.copy()
        self.processed_data['State'] = states
        self.processed_data.dropna(inplace=True)
        
        # 4. 运行回测
        test_data = self.processed_data[self.processed_data.index >= test_start]
        
        print(f"\n开始回测 ({test_start} 至今，共{len(test_data)}个交易日)")
        
        bt = Backtest(test_data, DualSMAStrategy, 
                     cash=cash, commission=commission, 
                     exclusive_orders=True, margin=0.3)
        
        self.backtest_result = bt.run()
        
        # 5. 显示结果
        self.display_results()
        
        return self.backtest_result
    
    def display_results(self):
        """显示策略结果"""
        result = self.backtest_result
        
        print("\n" + "=" * 50)
        print("📊 策略一回测结果")
        print("=" * 50)
        print(f"年化收益率: {result['Return (Ann.) [%]']:.2f}%")
        print(f"夏普比率: {result['Sharpe Ratio']:.3f}")
        print(f"最大回撤: {result['Max. Drawdown [%]']:.1f}%")
        print(f"总交易次数: {result['# Trades']}")
        print(f"胜率: {result['Win Rate [%]']:.1f}%")
        print(f"盈亏比: {result['Avg. Trade [%]']:.2f}%")
        print(f"Sortino比率: {result['Sortino Ratio']:.3f}")
        print(f"Calmar比率: {result['Calmar Ratio']:.3f}")
        
    def plot_results(self, figsize=(15, 10)):
        """绘制策略结果"""
        self.setup_chinese_display()
        
        fig, axes = plt.subplots(3, 1, figsize=figsize)
        
        # 子图1：价格和SMA
        ax1 = axes[0]
        test_data = self.processed_data[self.processed_data.index >= "2018-01-01"]
        
        ax1.plot(test_data.index, test_data['Close'], label='SPY价格', alpha=0.7)
        ax1.plot(test_data.index, test_data['Close'].rolling(90).mean(), 
                label='SMA90', alpha=0.8)
        ax1.plot(test_data.index, test_data['Close'].rolling(120).mean(), 
                label='SMA120', alpha=0.8)
        ax1.set_title('策略一：价格与移动平均线', fontsize=14)
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # 子图2：HMM状态
        ax2 = axes[1]
        states = test_data['State']
        ax2.fill_between(test_data.index, 0, 1, where=(states == 0), 
                        alpha=0.3, color='green', label='牛市状态')
        ax2.fill_between(test_data.index, 0, 1, where=(states == 1), 
                        alpha=0.3, color='red', label='熊市状态')
        ax2.set_title('HMM市场状态识别', fontsize=14)
        ax2.set_ylabel('状态')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        # 子图3：策略净值
        ax3 = axes[2]
        equity_curve = self.backtest_result._equity_curve
        ax3.plot(equity_curve.index, equity_curve['Equity'], 
                label='策略净值', linewidth=2, color='blue')
        
        # 计算基准收益
        spy_returns = test_data['Close'].pct_change().fillna(0)
        spy_cumret = (1 + spy_returns).cumprod() * 10000
        ax3.plot(test_data.index, spy_cumret, 
                label='SPY基准', alpha=0.7, color='gray')
        
        ax3.set_title('策略一：净值曲线对比', fontsize=14)
        ax3.set_xlabel('时间')
        ax3.set_ylabel('资产价值 ($)')
        ax3.legend()
        ax3.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.show()
    
    def get_strategy_summary(self):
        """获取策略摘要"""
        if self.backtest_result is None:
            return "策略尚未运行"
        
        result = self.backtest_result
        return {
            'strategy_name': '策略一：双SMA交叉+HMM',
            'annual_return': result['Return (Ann.) [%]'],
            'sharpe_ratio': result['Sharpe Ratio'],
            'max_drawdown': result['Max. Drawdown [%]'],
            'total_trades': result['# Trades'],
            'win_rate': result['Win Rate [%]'],
            'sortino_ratio': result['Sortino Ratio'],
            'calmar_ratio': result['Calmar Ratio']
        }

def main():
    """主函数：运行策略一"""
    
    # 创建策略运行器
    runner = StrategyOneRunner()
    
    # 运行完整策略
    result = runner.run_complete_strategy(
        symbol="SPY",
        start_date="2013-01-01", 
        test_start="2018-01-01",
        cash=10000,
        commission=0.0
    )
    
    # 绘制结果
    runner.plot_results()
    
    # 获取策略摘要
    summary = runner.get_strategy_summary()
    print("\n" + "=" * 50)
    print("📋 策略摘要")
    print("=" * 50)
    for key, value in summary.items():
        print(f"{key}: {value}")
    
    return runner, result

if __name__ == "__main__":
    # 运行策略
    runner, result = main()
    
    # 保存结果（可选）
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    import os; os.makedirs("logs", exist_ok=True); result.to_csv(f"logs/strategy_1_results_{timestamp}.csv")
    print(f"\n✅ 结果已保存至 logs/strategy_1_results_{timestamp}.csv") 