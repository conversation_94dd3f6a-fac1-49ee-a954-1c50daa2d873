#!/usr/bin/env python3
"""
四策略综合对比运行器
================

这个脚本可以一次性运行所有四个HMM交叉策略，并进行全面的性能对比分析。

策略概览：
- 策略一：双SMA交叉 + HMM (简单有效)
- 策略二：四SMA交叉 + HMM (最佳平衡，推荐)
- 策略三：卡尔曼滤波 + SMA + HMM (研究型)
- 策略四：多因子HMM + 动态仓位 (进阶型，夏普比率最优)

作者：量化交易研究团队
日期：2024年
版本：1.0
"""

import sys
import os
import time
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime
import warnings

# 导入各个策略
from strategy_1_dual_sma_hmm import StrategyOneRunner
from strategy_2_quad_sma_hmm import StrategyTwoRunner  
from strategy_3_kalman_sma_hmm import StrategyThreeRunner
from strategy_4_multifactor_hmm_v2 import StrategyFourRunner

warnings.filterwarnings('ignore')

class StrategiesComparator:
    """四策略综合对比器"""
    
    def __init__(self):
        self.runners = {}
        self.results = {}
        self.setup_chinese_display()
        
    def setup_chinese_display(self):
        """设置中文显示"""
        plt.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'SimHei', 'DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False
        print("✅ 中文显示设置完成")
    
    def run_all_strategies(self, symbol="SPY", start_date="2013-01-01", 
                          test_start="2018-01-01", cash=10000, commission=0.0):
        """运行所有四个策略"""
        
        print("🚀 开始运行四策略综合对比")
        print("=" * 80)
        
        strategy_configs = [
            ("策略一：双SMA+HMM", StrategyOneRunner()),
            ("策略二：四SMA+HMM", StrategyTwoRunner()),
            ("策略三：卡尔曼+SMA+HMM", StrategyThreeRunner()),
            ("策略四：多因子HMM", StrategyFourRunner())
        ]
        
        for i, (name, runner) in enumerate(strategy_configs, 1):
            print(f"\n🔄 正在运行 {name}...")
            start_time = time.time()
            
            try:
                result = runner.run_complete_strategy(
                    symbol=symbol,
                    start_date=start_date,
                    test_start=test_start,
                    cash=cash,
                    commission=commission
                )
                
                self.runners[f"strategy_{i}"] = runner
                self.results[f"strategy_{i}"] = result
                
                elapsed_time = time.time() - start_time
                print(f"✅ {name} 完成 (用时: {elapsed_time:.1f}秒)")
                
            except Exception as e:
                print(f"❌ {name} 运行失败: {e}")
                continue
        
        print(f"\n🎉 所有策略运行完成！")
        return self.results
    
    def create_comparison_table(self):
        """创建策略对比表"""
        if not self.results:
            print("❌ 没有策略结果可供对比")
            return None
        
        print("\n📊 策略性能对比表")
        print("=" * 100)
        
        # 收集所有策略的关键指标
        comparison_data = []
        
        for strategy_key, result in self.results.items():
            strategy_names = {
                "strategy_1": "策略一(双SMA+HMM)",
                "strategy_2": "策略二(四SMA+HMM)", 
                "strategy_3": "策略三(卡尔曼+SMA+HMM)",
                "strategy_4": "策略四(多因子HMM)"
            }
            
            strategy_data = {
                '策略名称': strategy_names.get(strategy_key, strategy_key),
                '年化收益率(%)': f"{result['Return (Ann.) [%]']:.2f}",
                '夏普比率': f"{result['Sharpe Ratio']:.3f}",
                '最大回撤(%)': f"{result['Max. Drawdown [%]']:.1f}",
                '总交易次数': result['# Trades'],
                '胜率(%)': f"{result['Win Rate [%]']:.1f}",
                'Sortino比率': f"{result['Sortino Ratio']:.3f}",
                'Calmar比率': f"{result['Calmar Ratio']:.3f}"
            }
            comparison_data.append(strategy_data)
        
        # 创建DataFrame
        comparison_df = pd.DataFrame(comparison_data)
        
        # 显示表格
        print(comparison_df.to_string(index=False))
        
        # 性能排名分析
        print(f"\n🏆 性能排名分析:")
        print("-" * 50)
        
        # 各指标最佳策略
        metrics = {
            '最高年化收益率': ('Return (Ann.) [%]', True),
            '最高夏普比率': ('Sharpe Ratio', True),
            '最小回撤': ('Max. Drawdown [%]', False),
            '最高胜率': ('Win Rate [%]', True),
            '最高Sortino比率': ('Sortino Ratio', True),
            '最高Calmar比率': ('Calmar Ratio', True)
        }
        
        for metric_name, (metric_key, is_higher_better) in metrics.items():
            if is_higher_better:
                best_strategy = max(self.results.items(), key=lambda x: x[1][metric_key])
            else:
                best_strategy = min(self.results.items(), key=lambda x: x[1][metric_key])
            
            strategy_names = {
                "strategy_1": "策略一",
                "strategy_2": "策略二", 
                "strategy_3": "策略三",
                "strategy_4": "策略四"
            }
            
            best_name = strategy_names.get(best_strategy[0], best_strategy[0])
            best_value = best_strategy[1][metric_key]
            print(f"{metric_name}: {best_name} ({best_value:.3f})")
        
        return comparison_df
    
    def plot_comprehensive_comparison(self, figsize=(20, 16)):
        """绘制综合对比图表"""
        if not self.results:
            print("❌ 没有策略结果可供绘制")
            return
        
        print("\n📈 绘制综合对比图表...")
        
        fig, axes = plt.subplots(3, 2, figsize=figsize)
        
        # 策略名称映射
        strategy_names = {
            "strategy_1": "策略一(双SMA+HMM)",
            "strategy_2": "策略二(四SMA+HMM)", 
            "strategy_3": "策略三(卡尔曼+SMA+HMM)",
            "strategy_4": "策略四(多因子HMM)"
        }
        
        colors = ['blue', 'orange', 'green', 'red']
        
        # 子图1：净值曲线对比
        ax1 = axes[0, 0]
        for i, (strategy_key, result) in enumerate(self.results.items()):
            equity_curve = result._equity_curve
            strategy_name = strategy_names.get(strategy_key, strategy_key)
            ax1.plot(equity_curve.index, equity_curve['Equity'], 
                    label=strategy_name, linewidth=2, color=colors[i], alpha=0.8)
        
        ax1.set_title('四策略净值曲线对比', fontsize=14, fontweight='bold')
        ax1.set_ylabel('资产价值 ($)')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # 子图2：年化收益率对比
        ax2 = axes[0, 1]
        strategy_labels = [strategy_names.get(k, k) for k in self.results.keys()]
        annual_returns = [v['Return (Ann.) [%]'] for v in self.results.values()]
        
        bars = ax2.bar(strategy_labels, annual_returns, color=colors[:len(self.results)], alpha=0.7)
        ax2.set_title('年化收益率对比', fontsize=14, fontweight='bold')
        ax2.set_ylabel('年化收益率 (%)')
        ax2.tick_params(axis='x', rotation=45)
        
        # 添加数值标签
        for i, bar in enumerate(bars):
            height = bar.get_height()
            ax2.text(bar.get_x() + bar.get_width()/2., height + 0.5,
                    f'{height:.1f}%', ha='center', va='bottom')
        
        # 子图3：夏普比率对比
        ax3 = axes[1, 0]
        sharpe_ratios = [v['Sharpe Ratio'] for v in self.results.values()]
        
        bars = ax3.bar(strategy_labels, sharpe_ratios, color=colors[:len(self.results)], alpha=0.7)
        ax3.set_title('夏普比率对比', fontsize=14, fontweight='bold')
        ax3.set_ylabel('夏普比率')
        ax3.tick_params(axis='x', rotation=45)
        ax3.axhline(y=1.0, color='red', linestyle='--', alpha=0.5, label='优秀水平(1.0)')
        ax3.legend()
        
        # 添加数值标签
        for i, bar in enumerate(bars):
            height = bar.get_height()
            ax3.text(bar.get_x() + bar.get_width()/2., height + 0.05,
                    f'{height:.3f}', ha='center', va='bottom')
        
        # 子图4：最大回撤对比
        ax4 = axes[1, 1]
        max_drawdowns = [v['Max. Drawdown [%]'] for v in self.results.values()]
        
        bars = ax4.bar(strategy_labels, max_drawdowns, color=colors[:len(self.results)], alpha=0.7)
        ax4.set_title('最大回撤对比 (越低越好)', fontsize=14, fontweight='bold')
        ax4.set_ylabel('最大回撤 (%)')
        ax4.tick_params(axis='x', rotation=45)
        
        # 添加数值标签
        for i, bar in enumerate(bars):
            height = bar.get_height()
            ax4.text(bar.get_x() + bar.get_width()/2., height + 0.5,
                    f'{height:.1f}%', ha='center', va='bottom')
        
        # 子图5：胜率对比
        ax5 = axes[2, 0]
        win_rates = [v['Win Rate [%]'] for v in self.results.values()]
        
        bars = ax5.bar(strategy_labels, win_rates, color=colors[:len(self.results)], alpha=0.7)
        ax5.set_title('胜率对比', fontsize=14, fontweight='bold')
        ax5.set_ylabel('胜率 (%)')
        ax5.tick_params(axis='x', rotation=45)
        ax5.axhline(y=50, color='red', linestyle='--', alpha=0.5, label='基准线(50%)')
        ax5.legend()
        
        # 添加数值标签
        for i, bar in enumerate(bars):
            height = bar.get_height()
            ax5.text(bar.get_x() + bar.get_width()/2., height + 1,
                    f'{height:.1f}%', ha='center', va='bottom')
        
        # 子图6：综合评分雷达图 (简化版)
        ax6 = axes[2, 1]
        
        # 标准化各指标到0-10分
        metrics_data = []
        for result in self.results.values():
            # 标准化公式（简化版）
            annual_return_score = min(10, max(0, result['Return (Ann.) [%]'] / 2))  # 20%年化收益为满分
            sharpe_score = min(10, max(0, result['Sharpe Ratio'] * 5))  # 2.0夏普比率为满分
            drawdown_score = min(10, max(0, 10 - result['Max. Drawdown [%]'] / 2))  # 回撤越小分数越高
            win_rate_score = min(10, max(0, (result['Win Rate [%]'] - 30) / 7))  # 30-100%胜率映射到0-10分
            
            metrics_data.append([annual_return_score, sharpe_score, drawdown_score, win_rate_score])
        
        # 绘制简化的条形图代替雷达图
        x = np.arange(len(strategy_labels))
        width = 0.2
        
        metric_names = ['年化收益', '夏普比率', '回撤控制', '胜率']
        for i, metric_name in enumerate(metric_names):
            scores = [data[i] for data in metrics_data]
            ax6.bar(x + i * width, scores, width, label=metric_name, alpha=0.7)
        
        ax6.set_title('综合评分对比 (0-10分)', fontsize=14, fontweight='bold')
        ax6.set_ylabel('评分')
        ax6.set_xticks(x + width * 1.5)
        ax6.set_xticklabels([name.split('(')[0] for name in strategy_labels])
        ax6.legend()
        ax6.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.show()
    
    def generate_final_recommendations(self):
        """生成最终推荐建议"""
        if not self.results:
            print("❌ 没有策略结果可供分析")
            return
        
        print("\n" + "=" * 80)
        print("🎯 最终策略推荐建议")
        print("=" * 80)
        
        # 找出各方面最佳策略
        best_return = max(self.results.items(), key=lambda x: x[1]['Return (Ann.) [%]'])
        best_sharpe = max(self.results.items(), key=lambda x: x[1]['Sharpe Ratio'])
        best_drawdown = min(self.results.items(), key=lambda x: x[1]['Max. Drawdown [%]'])
        best_winrate = max(self.results.items(), key=lambda x: x[1]['Win Rate [%]'])
        
        strategy_names = {
            "strategy_1": "策略一",
            "strategy_2": "策略二", 
            "strategy_3": "策略三",
            "strategy_4": "策略四"
        }
        
        print(f"🏆 单项冠军:")
        print(f"   最高收益率: {strategy_names[best_return[0]]} ({best_return[1]['Return (Ann.) [%]']:.2f}%)")
        print(f"   最高夏普比率: {strategy_names[best_sharpe[0]]} ({best_sharpe[1]['Sharpe Ratio']:.3f})")
        print(f"   最小回撤: {strategy_names[best_drawdown[0]]} ({best_drawdown[1]['Max. Drawdown [%]']:.1f}%)")
        print(f"   最高胜率: {strategy_names[best_winrate[0]]} ({best_winrate[1]['Win Rate [%]']:.1f}%)")
        
        print(f"\n📋 针对不同用户的推荐:")
        print("-" * 50)
        
        print(f"🔰 量化交易新手:")
        print(f"   推荐: 策略二(四SMA+HMM)")
        print(f"   理由: 最佳性价比，复杂度适中，实际表现稳定")
        print(f"   优势: 多重确认机制，风险控制好，易于理解和实施")
        
        print(f"\n🔧 进阶量化用户:")
        print(f"   推荐: 策略四(多因子HMM)")
        print(f"   理由: 理论框架完善，夏普比率最优，可扩展性强")
        print(f"   优势: 多因子模型，动态仓位管理，适合深度定制")
        
        print(f"\n📚 学术研究用户:")
        print(f"   推荐: 策略三(卡尔曼+SMA+HMM)")
        print(f"   理由: 高级数学工具应用，研究价值高")
        print(f"   注意: 实际表现有限，更适合作为研究起点")
        
        print(f"\n💼 机构投资者:")
        print(f"   推荐: 策略组合使用")
        print(f"   建议: 策略二(主体) + 策略四(增强) + 策略一(备用)")
        print(f"   优势: 分散风险，利用各策略优势，提高整体稳定性")
        
        print(f"\n🎯 核心建议:")
        print(f"   1. 新手首选: 策略二，简单有效")
        print(f"   2. 追求最优: 策略四，夏普比率最高")
        print(f"   3. 学习研究: 策略三，技术含量最高")
        print(f"   4. 保守稳健: 策略一，最基础可靠")
        
        print(f"\n⚠️ 重要提醒:")
        print(f"   - 历史表现不代表未来收益")
        print(f"   - 建议先用模拟交易验证")
        print(f"   - 根据个人风险偏好选择")
        print(f"   - 定期回测和参数调优")
        
        print("=" * 80)
    
    def save_results(self, filename_prefix="strategies_comparison"):
        """保存对比结果"""
        import os
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 确保log目录存在
        log_dir = "log"
        if not os.path.exists(log_dir):
            os.makedirs(log_dir)
            print(f"✅ 创建结果目录: {log_dir}/")
        
        # 保存对比表格
        comparison_df = self.create_comparison_table()
        if comparison_df is not None:
            csv_filename = f"{log_dir}/{filename_prefix}_{timestamp}.csv"
            comparison_df.to_csv(csv_filename, index=False, encoding='utf-8-sig')
            print(f"\n✅ 对比结果已保存至: {csv_filename}")
        
        # 保存各策略详细结果
        for strategy_key, result in self.results.items():
            detail_filename = f"{log_dir}/{strategy_key}_details_{timestamp}.csv"
            result.to_csv(detail_filename, encoding='utf-8-sig')
            print(f"✅ {strategy_key} 详细结果已保存至: {detail_filename}")

def main():
    """主函数：运行四策略综合对比"""
    
    print("🚀 四策略HMM交叉综合对比系统")
    print("=" * 80)
    print("即将运行以下策略：")
    print("策略一：双SMA交叉 + HMM")
    print("策略二：四SMA交叉 + HMM (推荐)")
    print("策略三：卡尔曼滤波 + SMA + HMM (研究型)")
    print("策略四：多因子HMM + 动态仓位 (进阶型)")
    print("=" * 80)
    
    # 创建对比器
    comparator = StrategiesComparator()
    
    # 运行所有策略
    results = comparator.run_all_strategies(
        symbol="SPY",
        start_date="2013-01-01",
        test_start="2018-01-01", 
        cash=10000,
        commission=0.0
    )
    
    if results:
        # 创建对比表格
        comparison_df = comparator.create_comparison_table()
        
        # 绘制综合对比图表
        comparator.plot_comprehensive_comparison()
        
        # 生成最终推荐
        comparator.generate_final_recommendations()
        
        # 保存结果
        comparator.save_results()
        
        print(f"\n🎉 四策略综合对比完成！")
        print(f"📊 共完成 {len(results)} 个策略的回测")
        print(f"📈 图表和详细分析已生成")
        print(f"💾 所有结果已保存到本地文件")
        
    else:
        print("❌ 没有策略成功运行，请检查错误信息")
    
    return comparator

if __name__ == "__main__":
    # 运行综合对比
    comparator = main()
    
    print(f"\n📝 使用建议:")
    print(f"   - 各策略的单独py文件可以独立运行")
    print(f"   - 可以修改参数进行自定义回测")
    print(f"   - 建议根据个人需求选择合适的策略")
    print(f"   - 记得在实盘前进行充分的模拟测试")
    print(f"\n�� 记住：最好的策略是适合你的策略！") 