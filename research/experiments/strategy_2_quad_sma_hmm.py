#!/usr/bin/env python3
"""
策略二：四SMA交叉 + HMM状态识别策略 (最佳策略)
=============================================

核心逻辑：
- 使用HMM识别市场状态（牛市/熊市）
- 四条移动平均线：长期趋势线、更长期趋势线、入场线、出场线
- 买入：大趋势向上 + 牛市状态 + 价格突破入场线
- 卖出：出场线穿越价格 + 熊市状态

这是经过验证的最佳策略，在复杂度和效果间达到最佳平衡。

作者：量化交易研究团队
日期：2024年
版本：1.0
"""

import numpy as np
import pandas as pd
import yfinance as yf
import matplotlib.pyplot as plt
import warnings
from datetime import datetime, timedelta

# 机器学习和回测相关
from hmmlearn.hmm import GaussianHMM
from backtesting import Backtest, Strategy
from backtesting.lib import crossover
from backtesting.test import SMA
import multiprocessing

# 忽略警告
warnings.filterwarnings('ignore')

def Extract(data, column):
    """从数据中提取指定列的函数，用于backtesting框架"""
    if hasattr(data, column):
        return getattr(data, column)
    else:
        return data[column]

def fix_multiprocessing_warnings():
    """修复multiprocessing警告"""
    try:
        if multiprocessing.get_start_method(allow_none=True) is None:
            multiprocessing.set_start_method('fork', force=True)
    except RuntimeError:
        pass
    warnings.filterwarnings('ignore', message='resource_tracker: There appear to be .* leaked .* objects to clean up at shutdown')

# 应用multiprocessing修复
fix_multiprocessing_warnings()

class HMMDataProcessor:
    """HMM数据处理和模型训练类"""
    
    def __init__(self):
        self.model = None
        self.state_means = None
        
    def download_data(self, symbol="SPY", start_date="2013-01-01", end_date=None):
        """下载股票数据并处理yfinance MultiIndex问题"""
        if end_date is None:
            end_date = datetime.now().strftime("%Y-%m-%d")
            
        print(f"下载{symbol}数据: {start_date} 到 {end_date}")
        
        # 下载数据
        data = yf.download(symbol, start=start_date, end=end_date)
        
        # 修复yfinance MultiIndex问题
        if isinstance(data.columns, pd.MultiIndex):
            data.columns = data.columns.droplevel(1)
            print("✅ 已修复yfinance MultiIndex数据结构")
        
        # 计算对数收益率
        data['Returns'] = np.log(data['Close']).diff()
        data.dropna(inplace=True)
        
        print(f"✅ 数据下载完成，共{len(data)}个交易日")
        return data
    
    def train_hmm_model(self, data, n_states=2):
        """训练HMM模型"""
        print(f"开始训练{n_states}状态HMM模型...")
        
        # 准备训练数据
        returns = data['Returns'].values.reshape(-1, 1)
        
        # 训练HMM模型
        self.model = GaussianHMM(n_components=n_states, 
                                covariance_type="full", 
                                n_iter=1000, 
                                random_state=42)
        self.model.fit(returns)
        
        # 预测状态
        states = self.model.predict(returns)
        
        # 分析状态特征
        state_means = []
        for i in range(n_states):
            state_returns = returns[states == i]
            state_mean = np.mean(state_returns)
            state_means.append(state_mean)
            print(f"状态{i}: 平均收益={state_mean:.6f}")
        
        # 确定牛市和熊市状态（收益率高的为牛市）
        bull_state = np.argmax(state_means)
        bear_state = np.argmin(state_means)
        
        # 重新映射状态：0为牛市，1为熊市
        mapped_states = np.where(states == bull_state, 0, 1)
        
        print(f"✅ HMM模型训练完成")
        print(f"   牛市状态(0): {np.sum(mapped_states == 0)}天 ({np.mean(mapped_states == 0)*100:.1f}%)")
        print(f"   熊市状态(1): {np.sum(mapped_states == 1)}天 ({np.mean(mapped_states == 1)*100:.1f}%)")
        
        return mapped_states

class QuadSMAStrategy(Strategy):
    """四SMA交叉策略 + HMM状态识别"""
    
    # 策略参数（经过优化的最佳参数）
    n1 = 70      # 长期趋势线SMA
    n2 = 120     # 更长期趋势线SMA  
    n_enter = 20 # 入场信号线SMA
    n_exit = 25  # 出场信号线SMA
    
    def init(self):
        """初始化策略指标"""
        close = self.data.Close
        
        # 四条移动平均线
        self.sma1 = self.I(SMA, close, self.n1)        # 长期趋势线
        self.sma2 = self.I(SMA, close, self.n2)        # 更长期趋势线
        self.sma_enter = self.I(SMA, close, self.n_enter) # 入场线
        self.sma_exit = self.I(SMA, close, self.n_exit)   # 出场线
        
        # 提取HMM状态
        self.state = self.I(Extract, self.data, 'State')
        
    def next(self):
        """策略交易逻辑"""
        current_state = self.state[-1]
        
        if not self.position:
            # 买入条件：大趋势向上 + 牛市状态 + 价格突破入场线
            long_trend_up = (self.sma1[-1] > self.sma2[-1])  # 大趋势向上
            bull_market = (current_state == 0)               # 牛市状态
            price_breakout = (self.data.Close[-1] > self.sma_enter[-1])  # 价格突破入场线
            
            if long_trend_up and bull_market and price_breakout:
                self.buy()
        else:
            # 卖出条件：出场线向上穿越价格 或 进入熊市状态
            exit_conditions = [
                crossover(self.sma_exit, self.data.Close),  # 技术止损
                (current_state != 0)                        # 状态变化
            ]
            
            if any(exit_conditions):
                self.position.close()

class StrategyTwoRunner:
    """策略二运行器"""
    
    def __init__(self):
        self.data_processor = HMMDataProcessor()
        self.raw_data = None
        self.processed_data = None
        self.backtest_result = None
        
    def setup_chinese_display(self):
        """设置中文显示"""
        plt.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'SimHei', 'DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False
        print("✅ 中文显示设置完成")
    
    def run_complete_strategy(self, symbol="SPY", start_date="2013-01-01", 
                            test_start="2018-01-01", cash=10000, commission=0.0):
        """运行完整策略"""
        print("=" * 70)
        print("策略二：四SMA交叉 + HMM状态识别策略 (最佳策略)")
        print("=" * 70)
        
        # 1. 数据下载和处理
        self.raw_data = self.data_processor.download_data(symbol, start_date)
        
        # 2. HMM模型训练
        states = self.data_processor.train_hmm_model(self.raw_data)
        
        # 3. 准备回测数据
        self.processed_data = self.raw_data.copy()
        self.processed_data['State'] = states
        self.processed_data.dropna(inplace=True)
        
        # 4. 运行回测
        test_data = self.processed_data[self.processed_data.index >= test_start]
        
        print(f"\n开始回测 ({test_start} 至今，共{len(test_data)}个交易日)")
        
        bt = Backtest(test_data, QuadSMAStrategy, 
                     cash=cash, commission=commission, 
                     exclusive_orders=True, margin=0.3)
        
        self.backtest_result = bt.run()
        
        # 5. 显示结果
        self.display_results()
        
        return self.backtest_result
    
    def display_results(self):
        """显示策略结果"""
        result = self.backtest_result
        
        print("\n" + "=" * 50)
        print("📊 策略二回测结果 (最佳策略)")
        print("=" * 50)
        print(f"年化收益率: {result['Return (Ann.) [%]']:.2f}%")
        print(f"夏普比率: {result['Sharpe Ratio']:.3f}")
        print(f"最大回撤: {result['Max. Drawdown [%]']:.1f}%")
        print(f"总交易次数: {result['# Trades']}")
        print(f"胜率: {result['Win Rate [%]']:.1f}%")
        print(f"盈亏比: {result['Avg. Trade [%]']:.2f}%")
        print(f"Sortino比率: {result['Sortino Ratio']:.3f}")
        print(f"Calmar比率: {result['Calmar Ratio']:.3f}")
        
        # 策略特色分析
        print(f"\n🎯 策略二特色:")
        print(f"   多重确认机制: ✅ 四条SMA + HMM状态")
        print(f"   风险控制: ✅ 技术止损 + 状态切换")
        print(f"   交易频率: 适中 ({result['# Trades']}次)")
        print(f"   实用性: ⭐⭐⭐⭐⭐ (最推荐)")
        
    def plot_results(self, figsize=(15, 12)):
        """绘制策略结果"""
        self.setup_chinese_display()
        
        fig, axes = plt.subplots(4, 1, figsize=figsize)
        
        # 子图1：价格和四条SMA
        ax1 = axes[0]
        test_data = self.processed_data[self.processed_data.index >= "2018-01-01"]
        
        ax1.plot(test_data.index, test_data['Close'], label='SPY价格', alpha=0.8, linewidth=2)
        ax1.plot(test_data.index, test_data['Close'].rolling(70).mean(), 
                label='SMA70(长期)', alpha=0.8, color='blue')
        ax1.plot(test_data.index, test_data['Close'].rolling(120).mean(), 
                label='SMA120(更长期)', alpha=0.8, color='red')
        ax1.plot(test_data.index, test_data['Close'].rolling(20).mean(), 
                label='SMA20(入场)', alpha=0.8, color='green')
        ax1.plot(test_data.index, test_data['Close'].rolling(25).mean(), 
                label='SMA25(出场)', alpha=0.8, color='orange')
        ax1.set_title('策略二：价格与四条移动平均线', fontsize=14, fontweight='bold')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # 子图2：HMM状态
        ax2 = axes[1]
        states = test_data['State']
        ax2.fill_between(test_data.index, 0, 1, where=(states == 0), 
                        alpha=0.3, color='green', label='牛市状态')
        ax2.fill_between(test_data.index, 0, 1, where=(states == 1), 
                        alpha=0.3, color='red', label='熊市状态')
        ax2.set_title('HMM市场状态识别', fontsize=14, fontweight='bold')
        ax2.set_ylabel('状态')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        # 子图3：策略净值对比
        ax3 = axes[2]
        equity_curve = self.backtest_result._equity_curve
        ax3.plot(equity_curve.index, equity_curve['Equity'], 
                label='策略二净值', linewidth=3, color='darkblue')
        
        # 计算基准收益
        spy_returns = test_data['Close'].pct_change().fillna(0)
        spy_cumret = (1 + spy_returns).cumprod() * 10000
        ax3.plot(test_data.index, spy_cumret, 
                label='SPY基准', alpha=0.7, color='gray', linewidth=2)
        
        ax3.set_title('策略二：净值曲线对比', fontsize=14, fontweight='bold')
        ax3.set_ylabel('资产价值 ($)')
        ax3.legend()
        ax3.grid(True, alpha=0.3)
        
        # 子图4：回撤分析
        ax4 = axes[3]
        # 策略回撤
        strategy_equity = equity_curve['Equity']
        strategy_drawdown = (strategy_equity / strategy_equity.expanding().max() - 1) * 100
        ax4.fill_between(equity_curve.index, strategy_drawdown, 0, 
                        alpha=0.6, color='red', label='策略二回撤')
        
        # 基准回撤
        spy_drawdown = (spy_cumret / spy_cumret.expanding().max() - 1) * 100
        ax4.fill_between(test_data.index, spy_drawdown, 0, 
                        alpha=0.4, color='gray', label='SPY基准回撤')
        
        ax4.set_title('回撤对比分析', fontsize=14, fontweight='bold')
        ax4.set_xlabel('时间')
        ax4.set_ylabel('回撤 (%)')
        ax4.legend()
        ax4.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.show()
    
    def analyze_trades(self):
        """分析交易详情"""
        if self.backtest_result is None:
            print("策略尚未运行")
            return
        
        trades = self.backtest_result._trades
        
        print("\n📈 交易分析:")
        print("-" * 30)
        print(f"总交易次数: {len(trades)}")
        print(f"盈利交易: {len(trades[trades['PnL'] > 0])}")
        print(f"亏损交易: {len(trades[trades['PnL'] < 0])}")
        print(f"平均持仓天数: {trades['Duration'].mean():.1f}天")
        print(f"最大单笔盈利: {trades['PnL'].max():.2f}%")
        print(f"最大单笔亏损: {trades['PnL'].min():.2f}%")
        
        return trades
    
    def get_strategy_summary(self):
        """获取策略摘要"""
        if self.backtest_result is None:
            return "策略尚未运行"
        
        result = self.backtest_result
        return {
            'strategy_name': '策略二：四SMA交叉+HMM (最佳策略)',
            'annual_return': result['Return (Ann.) [%]'],
            'sharpe_ratio': result['Sharpe Ratio'],
            'max_drawdown': result['Max. Drawdown [%]'],
            'total_trades': result['# Trades'],
            'win_rate': result['Win Rate [%]'],
            'sortino_ratio': result['Sortino Ratio'],
            'calmar_ratio': result['Calmar Ratio'],
            'recommendation': '⭐⭐⭐⭐⭐ 最推荐策略'
        }

def main():
    """主函数：运行策略二"""
    
    # 创建策略运行器
    runner = StrategyTwoRunner()
    
    # 运行完整策略
    result = runner.run_complete_strategy(
        symbol="SPY",
        start_date="2013-01-01", 
        test_start="2018-01-01",
        cash=10000,
        commission=0.0
    )
    
    # 绘制结果
    runner.plot_results()
    
    # 分析交易
    trades = runner.analyze_trades()
    
    # 获取策略摘要
    summary = runner.get_strategy_summary()
    print("\n" + "=" * 60)
    print("📋 策略二摘要 (最佳策略)")
    print("=" * 60)
    for key, value in summary.items():
        print(f"{key}: {value}")
    
    print(f"\n💡 为什么策略二是最佳选择:")
    print(f"   ✅ 多重确认减少假信号")
    print(f"   ✅ 风险控制机制完善") 
    print(f"   ✅ 复杂度适中，易于实施")
    print(f"   ✅ 在各项指标中最均衡")
    print(f"   ✅ 实际交易中最实用")
    
    return runner, result

if __name__ == "__main__":
    # 运行策略
    runner, result = main()
    
    # 保存结果（可选）
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    import os; os.makedirs("logs", exist_ok=True); result.to_csv(f"logs/strategy_2_results_{timestamp}.csv")
    print(f"\n✅ 结果已保存至 logs/strategy_2_results_{timestamp}.csv")
    print(f"📝 建议：策略二是经过验证的最佳选择，推荐实际使用！") 