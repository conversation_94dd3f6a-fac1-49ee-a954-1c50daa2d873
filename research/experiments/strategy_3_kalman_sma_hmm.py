#!/usr/bin/env python3
"""
策略三：卡尔曼滤波 + SMA + HMM状态识别策略
=======================================

核心逻辑：
- 使用HMM识别市场状态（牛市/熊市）
- 使用卡尔曼滤波器平滑价格噪声
- 当卡尔曼滤波价格 > SMA 且在牛市状态时买入
- 当SMA向上穿越卡尔曼滤波价格或进入熊市时卖出

注意：这是理论研究策略，在实际市场中表现不如传统SMA策略。
卡尔曼滤波在量化交易中的应用需要更精细的参数调优。

作者：量化交易研究团队
日期：2024年
版本：1.0
"""

import numpy as np
import pandas as pd
import yfinance as yf
import matplotlib.pyplot as plt
import warnings
from datetime import datetime, timedelta

# 机器学习和回测相关
from hmmlearn.hmm import GaussianHMM
from backtesting import Backtest, Strategy
from backtesting.lib import crossover
from backtesting.test import SMA
import multiprocessing

# 忽略警告
warnings.filterwarnings('ignore')

def Extract(data, column):
    """从数据中提取指定列的函数，用于backtesting框架"""
    if hasattr(data, column):
        return getattr(data, column)
    else:
        return data[column]

def fix_multiprocessing_warnings():
    """修复multiprocessing警告"""
    try:
        if multiprocessing.get_start_method(allow_none=True) is None:
            multiprocessing.set_start_method('fork', force=True)
    except RuntimeError:
        pass
    warnings.filterwarnings('ignore', message='resource_tracker: There appear to be .* leaked .* objects to clean up at shutdown')

# 应用multiprocessing修复
fix_multiprocessing_warnings()

class KalmanFilter:
    """卡尔曼滤波器实现"""
    
    def __init__(self, transition_matrices=None, observation_matrices=None,
                 initial_state_mean=None, n_dim_state=2):
        """
        初始化卡尔曼滤波器
        
        参数:
        - transition_matrices: 状态转移矩阵
        - observation_matrices: 观测矩阵  
        - initial_state_mean: 初始状态均值
        - n_dim_state: 状态维度
        """
        self.n_dim_state = n_dim_state
        
        # 默认参数设置
        if transition_matrices is None:
            self.transition_matrices = np.eye(n_dim_state)
        else:
            self.transition_matrices = transition_matrices
            
        if observation_matrices is None:
            self.observation_matrices = np.eye(1, n_dim_state)
        else:
            self.observation_matrices = observation_matrices
            
        # 初始状态
        if initial_state_mean is None:
            self.initial_state_mean = np.zeros(n_dim_state)
        else:
            self.initial_state_mean = initial_state_mean
            
        # 噪声协方差矩阵
        self.transition_covariance = np.eye(n_dim_state) * 0.01
        self.observation_covariance = np.array([[1.0]])
        self.initial_state_covariance = np.eye(n_dim_state)
        
    def filter(self, observations):
        """执行卡尔曼滤波"""
        n_observations = len(observations)
        
        # 初始化状态估计
        state_estimates = np.zeros((n_observations, self.n_dim_state))
        state_covariances = np.zeros((n_observations, self.n_dim_state, self.n_dim_state))
        
        # 初始状态
        current_state = self.initial_state_mean.copy()
        current_covariance = self.initial_state_covariance.copy()
        
        for t in range(n_observations):
            # 预测步骤
            predicted_state = self.transition_matrices @ current_state
            predicted_covariance = (self.transition_matrices @ current_covariance @ 
                                   self.transition_matrices.T + self.transition_covariance)
            
            # 更新步骤
            innovation = observations[t] - self.observation_matrices @ predicted_state
            innovation_covariance = (self.observation_matrices @ predicted_covariance @ 
                                   self.observation_matrices.T + self.observation_covariance)
            
            # 卡尔曼增益
            kalman_gain = (predicted_covariance @ self.observation_matrices.T @ 
                          np.linalg.inv(innovation_covariance))
            
            # 状态更新
            current_state = predicted_state + kalman_gain @ innovation
            current_covariance = ((np.eye(self.n_dim_state) - 
                                 kalman_gain @ self.observation_matrices) @ predicted_covariance)
            
            # 存储结果
            state_estimates[t] = current_state
            state_covariances[t] = current_covariance
            
        return state_estimates, state_covariances

class HMMDataProcessor:
    """HMM数据处理和模型训练类"""
    
    def __init__(self):
        self.model = None
        self.state_means = None
        
    def download_data(self, symbol="SPY", start_date="2013-01-01", end_date=None):
        """下载股票数据并处理yfinance MultiIndex问题"""
        if end_date is None:
            end_date = datetime.now().strftime("%Y-%m-%d")
            
        print(f"下载{symbol}数据: {start_date} 到 {end_date}")
        
        # 下载数据
        data = yf.download(symbol, start=start_date, end=end_date)
        
        # 修复yfinance MultiIndex问题
        if isinstance(data.columns, pd.MultiIndex):
            data.columns = data.columns.droplevel(1)
            print("✅ 已修复yfinance MultiIndex数据结构")
        
        # 计算对数收益率
        data['Returns'] = np.log(data['Close']).diff()
        data.dropna(inplace=True)
        
        print(f"✅ 数据下载完成，共{len(data)}个交易日")
        return data
    
    def apply_kalman_filter(self, data):
        """应用卡尔曼滤波器到价格数据"""
        print("应用卡尔曼滤波器...")
        
        prices = data['Close'].values.reshape(-1, 1)
        
        # 创建卡尔曼滤波器
        kf = KalmanFilter(n_dim_state=2)
        
        # 执行滤波
        state_estimates, _ = kf.filter(prices)
        
        # 提取滤波后的价格（状态的第一个维度）
        filtered_prices = state_estimates[:, 0]
        
        # 添加到数据中
        data_with_kalman = data.copy()
        data_with_kalman['Kalman'] = filtered_prices
        
        print(f"✅ 卡尔曼滤波完成")
        return data_with_kalman
    
    def train_hmm_model(self, data, n_states=2):
        """训练HMM模型"""
        print(f"开始训练{n_states}状态HMM模型...")
        
        # 准备训练数据
        returns = data['Returns'].values.reshape(-1, 1)
        
        # 训练HMM模型
        self.model = GaussianHMM(n_components=n_states, 
                                covariance_type="full", 
                                n_iter=1000, 
                                random_state=42)
        self.model.fit(returns)
        
        # 预测状态
        states = self.model.predict(returns)
        
        # 分析状态特征
        state_means = []
        for i in range(n_states):
            state_returns = returns[states == i]
            state_mean = np.mean(state_returns)
            state_means.append(state_mean)
            print(f"状态{i}: 平均收益={state_mean:.6f}")
        
        # 确定牛市和熊市状态（收益率高的为牛市）
        bull_state = np.argmax(state_means)
        bear_state = np.argmin(state_means)
        
        # 重新映射状态：0为牛市，1为熊市
        mapped_states = np.where(states == bull_state, 0, 1)
        
        print(f"✅ HMM模型训练完成")
        print(f"   牛市状态(0): {np.sum(mapped_states == 0)}天 ({np.mean(mapped_states == 0)*100:.1f}%)")
        print(f"   熊市状态(1): {np.sum(mapped_states == 1)}天 ({np.mean(mapped_states == 1)*100:.1f}%)")
        
        return mapped_states

class KalmanSMAStrategy(Strategy):
    """卡尔曼滤波 + SMA策略 + HMM状态识别"""
    
    # 策略参数（经过优化的参数）
    n = 12  # SMA周期
    
    def init(self):
        """初始化策略指标"""
        close = self.data.Close
        
        # 计算SMA
        self.sma = self.I(SMA, close, self.n)
        
        # 提取卡尔曼滤波价格和HMM状态
        self.kalman = self.I(Extract, self.data, 'Kalman')
        self.state = self.I(Extract, self.data, 'State')
        
    def next(self):
        """策略交易逻辑"""
        current_state = self.state[-1]
        
        if not self.position:
            # 买入条件：卡尔曼滤波价格 > SMA 且 牛市状态
            if (self.kalman[-1] > self.sma[-1]) and (current_state == 0):
                self.buy()
        else:
            # 卖出条件：SMA向上穿越卡尔曼滤波价格 或 进入熊市状态
            exit_conditions = [
                crossover(self.sma, self.kalman),  # 技术信号转弱
                (current_state != 0)               # 市场状态变化
            ]
            
            if any(exit_conditions):
                self.position.close()

class StrategyThreeRunner:
    """策略三运行器"""
    
    def __init__(self):
        self.data_processor = HMMDataProcessor()
        self.raw_data = None
        self.processed_data = None
        self.backtest_result = None
        
    def setup_chinese_display(self):
        """设置中文显示"""
        plt.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'SimHei', 'DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False
        print("✅ 中文显示设置完成")
    
    def run_complete_strategy(self, symbol="SPY", start_date="2013-01-01", 
                            test_start="2018-01-01", cash=10000, commission=0.0):
        """运行完整策略"""
        print("=" * 70)
        print("策略三：卡尔曼滤波 + SMA + HMM状态识别策略")
        print("=" * 70)
        
        # 1. 数据下载和处理
        self.raw_data = self.data_processor.download_data(symbol, start_date)
        
        # 2. 应用卡尔曼滤波
        kalman_data = self.data_processor.apply_kalman_filter(self.raw_data)
        
        # 3. HMM模型训练
        states = self.data_processor.train_hmm_model(kalman_data)
        
        # 4. 准备回测数据
        self.processed_data = kalman_data.copy()
        self.processed_data['State'] = states
        self.processed_data.dropna(inplace=True)
        
        # 5. 运行回测
        test_data = self.processed_data[self.processed_data.index >= test_start]
        
        print(f"\n开始回测 ({test_start} 至今，共{len(test_data)}个交易日)")
        
        bt = Backtest(test_data, KalmanSMAStrategy, 
                     cash=cash, commission=commission, 
                     exclusive_orders=True, margin=0.3)
        
        self.backtest_result = bt.run()
        
        # 6. 显示结果
        self.display_results()
        
        return self.backtest_result
    
    def display_results(self):
        """显示策略结果"""
        result = self.backtest_result
        
        print("\n" + "=" * 50)
        print("📊 策略三回测结果 (研究型策略)")
        print("=" * 50)
        print(f"年化收益率: {result['Return (Ann.) [%]']:.2f}%")
        print(f"夏普比率: {result['Sharpe Ratio']:.3f}")
        print(f"最大回撤: {result['Max. Drawdown [%]']:.1f}%")
        print(f"总交易次数: {result['# Trades']}")
        print(f"胜率: {result['Win Rate [%]']:.1f}%")
        print(f"盈亏比: {result['Avg. Trade [%]']:.2f}%")
        print(f"Sortino比率: {result['Sortino Ratio']:.3f}")
        print(f"Calmar比率: {result['Calmar Ratio']:.3f}")
        
        # 策略特色分析
        print(f"\n🔬 策略三特色:")
        print(f"   技术创新: ✅ 卡尔曼滤波 + SMA + HMM")
        print(f"   理论价值: ✅ 展示高级数学工具应用")
        print(f"   实际表现: ⚠️ 需要更精细的参数调优")
        print(f"   适用场景: 📚 学术研究和算法探索")
        
        # 卡尔曼滤波效果分析
        test_data = self.processed_data[self.processed_data.index >= "2018-01-01"]
        correlation = test_data['Close'].corr(test_data['Kalman'])
        print(f"\n📈 卡尔曼滤波效果:")
        print(f"   原始价格与滤波价格相关性: {correlation:.3f}")
        print(f"   滤波平滑效果: {'✅ 良好' if correlation > 0.95 else '⚠️ 一般'}")
        
    def plot_results(self, figsize=(15, 14)):
        """绘制策略结果"""
        self.setup_chinese_display()
        
        fig, axes = plt.subplots(5, 1, figsize=figsize)
        
        # 子图1：原始价格 vs 卡尔曼滤波价格
        ax1 = axes[0]
        test_data = self.processed_data[self.processed_data.index >= "2018-01-01"]
        
        ax1.plot(test_data.index, test_data['Close'], label='原始SPY价格', 
                alpha=0.7, linewidth=1, color='gray')
        ax1.plot(test_data.index, test_data['Kalman'], label='卡尔曼滤波价格', 
                alpha=0.9, linewidth=2, color='blue')
        ax1.set_title('策略三：原始价格 vs 卡尔曼滤波价格', fontsize=14, fontweight='bold')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # 子图2：卡尔曼滤波价格和SMA
        ax2 = axes[1]
        ax2.plot(test_data.index, test_data['Kalman'], label='卡尔曼滤波价格', 
                alpha=0.8, linewidth=2, color='blue')
        ax2.plot(test_data.index, test_data['Close'].rolling(12).mean(), 
                label='SMA12', alpha=0.8, linewidth=2, color='red')
        ax2.set_title('卡尔曼滤波价格 vs SMA12', fontsize=14, fontweight='bold')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        # 子图3：HMM状态
        ax3 = axes[2]
        states = test_data['State']
        ax3.fill_between(test_data.index, 0, 1, where=(states == 0), 
                        alpha=0.3, color='green', label='牛市状态')
        ax3.fill_between(test_data.index, 0, 1, where=(states == 1), 
                        alpha=0.3, color='red', label='熊市状态')
        ax3.set_title('HMM市场状态识别', fontsize=14, fontweight='bold')
        ax3.set_ylabel('状态')
        ax3.legend()
        ax3.grid(True, alpha=0.3)
        
        # 子图4：策略净值对比
        ax4 = axes[3]
        equity_curve = self.backtest_result._equity_curve
        ax4.plot(equity_curve.index, equity_curve['Equity'], 
                label='策略三净值', linewidth=3, color='darkgreen')
        
        # 计算基准收益
        spy_returns = test_data['Close'].pct_change().fillna(0)
        spy_cumret = (1 + spy_returns).cumprod() * 10000
        ax4.plot(test_data.index, spy_cumret, 
                label='SPY基准', alpha=0.7, color='gray', linewidth=2)
        
        ax4.set_title('策略三：净值曲线对比', fontsize=14, fontweight='bold')
        ax4.set_ylabel('资产价值 ($)')
        ax4.legend()
        ax4.grid(True, alpha=0.3)
        
        # 子图5：滤波误差分析
        ax5 = axes[4]
        filter_error = test_data['Close'] - test_data['Kalman']
        ax5.plot(test_data.index, filter_error, label='滤波误差', 
                alpha=0.7, color='purple')
        ax5.axhline(y=0, color='black', linestyle='--', alpha=0.5)
        ax5.set_title('卡尔曼滤波误差分析', fontsize=14, fontweight='bold')
        ax5.set_xlabel('时间')
        ax5.set_ylabel('误差 ($)')
        ax5.legend()
        ax5.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.show()
    
    def analyze_kalman_performance(self):
        """分析卡尔曼滤波性能"""
        test_data = self.processed_data[self.processed_data.index >= "2018-01-01"]
        
        print("\n🔬 卡尔曼滤波性能分析:")
        print("-" * 40)
        
        # 计算滤波误差统计
        filter_error = test_data['Close'] - test_data['Kalman']
        
        print(f"平均滤波误差: {filter_error.mean():.4f}")
        print(f"滤波误差标准差: {filter_error.std():.4f}")
        print(f"最大正误差: {filter_error.max():.4f}")
        print(f"最大负误差: {filter_error.min():.4f}")
        
        # 滤波平滑度分析
        original_volatility = test_data['Close'].pct_change().std() * np.sqrt(252)
        filtered_volatility = test_data['Kalman'].pct_change().std() * np.sqrt(252)
        
        print(f"\n📈 平滑效果:")
        print(f"原始价格年化波动率: {original_volatility:.3f}")
        print(f"滤波价格年化波动率: {filtered_volatility:.3f}")
        print(f"噪声减少程度: {(1 - filtered_volatility/original_volatility)*100:.1f}%")
        
        return {
            'mean_error': filter_error.mean(),
            'error_std': filter_error.std(),
            'noise_reduction': (1 - filtered_volatility/original_volatility)*100
        }
    
    def get_strategy_summary(self):
        """获取策略摘要"""
        if self.backtest_result is None:
            return "策略尚未运行"
        
        result = self.backtest_result
        return {
            'strategy_name': '策略三：卡尔曼滤波+SMA+HMM',
            'annual_return': result['Return (Ann.) [%]'],
            'sharpe_ratio': result['Sharpe Ratio'],
            'max_drawdown': result['Max. Drawdown [%]'],
            'total_trades': result['# Trades'],
            'win_rate': result['Win Rate [%]'],
            'sortino_ratio': result['Sortino Ratio'],
            'calmar_ratio': result['Calmar Ratio'],
            'recommendation': '🔬 研究型策略，实际表现有限'
        }

def main():
    """主函数：运行策略三"""
    
    # 创建策略运行器
    runner = StrategyThreeRunner()
    
    # 运行完整策略
    result = runner.run_complete_strategy(
        symbol="SPY",
        start_date="2013-01-01", 
        test_start="2018-01-01",
        cash=10000,
        commission=0.0
    )
    
    # 绘制结果
    runner.plot_results()
    
    # 分析卡尔曼滤波性能
    kalman_analysis = runner.analyze_kalman_performance()
    
    # 获取策略摘要
    summary = runner.get_strategy_summary()
    print("\n" + "=" * 60)
    print("📋 策略三摘要 (研究型策略)")
    print("=" * 60)
    for key, value in summary.items():
        print(f"{key}: {value}")
    
    print(f"\n🤔 策略三的教训:")
    print(f"   💡 卡尔曼滤波在金融数据中的局限性:")
    print(f"      - 假设线性状态空间，但市场是非线性的")
    print(f"      - 需要精确的噪声建模，实际中很难获得")
    print(f"      - 滞后性：滤波会引入时间延迟")
    print(f"   📚 学术价值:")
    print(f"      - 展示了先进数学工具的应用")
    print(f"      - 为扩展卡尔曼滤波等变种提供基础")
    print(f"      - 可以作为特征工程的一部分")
    print(f"   ⚠️ 实际应用建议:")
    print(f"      - 不推荐单独使用")
    print(f"      - 可以与其他信号结合")
    print(f"      - 需要针对不同市场重新校准")
    
    return runner, result

if __name__ == "__main__":
    # 运行策略
    runner, result = main()
    
    # 保存结果（可选）
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    import os; os.makedirs("logs", exist_ok=True); result.to_csv(f"logs/strategy_3_results_{timestamp}.csv")
    print(f"\n✅ 结果已保存至 logs/strategy_3_results_{timestamp}.csv")
    print(f"📝 总结：策略三展示了卡尔曼滤波在量化交易中的挑战，教育价值大于实用价值。") 