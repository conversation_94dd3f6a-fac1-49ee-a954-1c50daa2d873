#!/usr/bin/env python3
"""
策略测试脚本
==========

用于快速测试所有四个策略是否能正常运行的脚本。
使用较小的数据集进行快速验证。

作者：量化交易研究团队
日期：2024年
"""

import pandas as pd
import numpy as np
import warnings
import sys
import traceback
import os
from datetime import datetime

# 忽略警告
warnings.filterwarnings('ignore')

def test_strategy(strategy_name, strategy_module, quick_test=True):
    """测试单个策略"""
    print(f"\n{'='*60}")
    print(f"🔍 测试 {strategy_name}")
    print(f"{'='*60}")
    
    try:
        # 导入策略模块
        if strategy_module == "strategy_1_dual_sma_hmm":
            from strategy_1_dual_sma_hmm import StrategyOneRunner
            runner = StrategyOneRunner()
        elif strategy_module == "strategy_2_quad_sma_hmm":
            from strategy_2_quad_sma_hmm import StrategyTwoRunner
            runner = StrategyTwoRunner()
        elif strategy_module == "strategy_3_kalman_sma_hmm":
            from strategy_3_kalman_sma_hmm import StrategyThreeRunner
            runner = StrategyThreeRunner()
        elif strategy_module == "strategy_4_multifactor_hmm_v2":
            from strategy_4_multifactor_hmm_v2 import StrategyFourRunner
            runner = StrategyFourRunner()
        elif strategy_module == "strategy_5_adaptive_enhanced_hmm":
            from strategy_5_adaptive_enhanced_hmm import StrategyFiveRunner
            runner = StrategyFiveRunner()
        else:
            raise ValueError(f"未知策略模块: {strategy_module}")
        
        print(f"✅ 策略模块导入成功")
        
        # 设置测试参数
        if quick_test:
            # 快速测试：使用较短的时间范围
            start_date = "2022-01-01"
            test_start = "2023-01-01"
            print(f"📅 快速测试模式：{start_date} - 现在 (回测从 {test_start} 开始)")
        else:
            # 完整测试
            start_date = "2020-01-01"
            test_start = "2022-01-01"
            print(f"📅 完整测试模式：{start_date} - 现在 (回测从 {test_start} 开始)")
        
        # 运行策略
        result = runner.run_complete_strategy(
            symbol="SPY",
            start_date=start_date,
            test_start=test_start,
            cash=10000,
            commission=0.0
        )
        
        # 检查结果
        if result is not None and hasattr(result, '__getitem__'):
            annual_return = result.get('Return (Ann.) [%]', 0)
            sharpe_ratio = result.get('Sharpe Ratio', 0)
            max_drawdown = result.get('Max. Drawdown [%]', 0)
            total_trades = result.get('# Trades', 0)
            
            print(f"\n📊 {strategy_name} 测试结果:")
            print(f"   年化收益率: {annual_return:.2f}%")
            print(f"   夏普比率: {sharpe_ratio:.3f}")
            print(f"   最大回撤: {max_drawdown:.1f}%")
            print(f"   总交易次数: {total_trades}")
            
            # 简单的健全性检查
            if annual_return < -50 or annual_return > 100:
                print(f"⚠️  警告：年化收益率异常 ({annual_return:.2f}%)")
            if sharpe_ratio < -2 or sharpe_ratio > 5:
                print(f"⚠️  警告：夏普比率异常 ({sharpe_ratio:.3f})")
            if max_drawdown > 50:
                print(f"⚠️  警告：最大回撤过大 ({max_drawdown:.1f}%)")
            if total_trades == 0:
                print(f"⚠️  警告：没有产生任何交易")
            elif total_trades > 1000:
                print(f"⚠️  警告：交易次数过多 ({total_trades})")
            
            print(f"✅ {strategy_name} 测试通过")
            return True, result
        else:
            print(f"❌ {strategy_name} 返回结果无效")
            return False, None
            
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        return False, None
    except Exception as e:
        print(f"❌ 运行错误: {e}")
        print("错误详情:")
        traceback.print_exc()
        return False, None

def test_all_strategies(quick_test=True):
    """测试所有策略"""
    print("🚀 开始测试所有HMM量化交易策略")
    print(f"测试模式: {'快速测试' if quick_test else '完整测试'}")
    
    strategies = [
        ("策略一：双SMA+HMM", "strategy_1_dual_sma_hmm"),
        ("策略二：四SMA+HMM", "strategy_2_quad_sma_hmm"),
        ("策略三：卡尔曼+SMA+HMM", "strategy_3_kalman_sma_hmm"),
        ("策略四：多因子HMM", "strategy_4_multifactor_hmm_v2"),
        ("策略五：自适应强化HMM", "strategy_5_adaptive_enhanced_hmm")
    ]
    
    results = {}
    passed = 0
    failed = 0
    
    for strategy_name, strategy_module in strategies:
        success, result = test_strategy(strategy_name, strategy_module, quick_test)
        results[strategy_name] = (success, result)
        
        if success:
            passed += 1
        else:
            failed += 1
    
    # 总结
    print(f"\n" + "="*80)
    print(f"📈 策略测试总结")
    print(f"="*80)
    print(f"✅ 通过: {passed} 个策略")
    print(f"❌ 失败: {failed} 个策略")
    print(f"📊 总计: {len(strategies)} 个策略")
    
    if passed == len(strategies):
        print(f"\n🎉 所有策略测试通过！代码质量良好。")
    elif passed > 0:
        print(f"\n⚠️  部分策略测试通过，请检查失败的策略。")
    else:
        print(f"\n❌ 所有策略测试失败，请检查代码和依赖。")
    
    # 成功策略的简要对比
    if passed > 1:
        print(f"\n📊 成功策略性能对比:")
        print("-" * 60)
        for strategy_name, (success, result) in results.items():
            if success and result is not None:
                annual_return = result.get('Return (Ann.) [%]', 0)
                sharpe_ratio = result.get('Sharpe Ratio', 0)
                print(f"{strategy_name:25}: 收益率 {annual_return:6.2f}% | 夏普比率 {sharpe_ratio:6.3f}")
    
    print(f"\n💡 建议:")
    if failed == 0:
        print("   - 所有策略正常工作，可以进行生产环境部署")
        print("   - 建议进行更长时间段的回测验证")
        print("   - 可以开始参数优化和实盘测试")
    else:
        print("   - 修复失败的策略后再进行下一步")
        print("   - 检查依赖包是否正确安装")
        print("   - 验证数据下载是否正常")
    
    return results

def main():
    """主函数"""
    print("HMM量化交易策略测试工具")
    print("="*40)
    
    # 检查命令行参数
    quick_test = True
    if len(sys.argv) > 1 and sys.argv[1] == "--full":
        quick_test = False
        print("使用完整测试模式")
    else:
        print("使用快速测试模式（如需完整测试，请添加 --full 参数）")
    
    # 运行测试
    results = test_all_strategies(quick_test)
    
    # 保存测试结果
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    # 确保logs目录存在
    os.makedirs("logs", exist_ok=True)
    
    # 创建测试报告
    report_file = f"logs/test_report_{timestamp}.txt"
    with open(report_file, "w", encoding="utf-8") as f:
        f.write("HMM量化交易策略测试报告\n")
        f.write("="*50 + "\n")
        f.write(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"测试模式: {'完整测试' if not quick_test else '快速测试'}\n\n")
        
        for strategy_name, (success, result) in results.items():
            f.write(f"\n{strategy_name}:\n")
            f.write(f"  状态: {'✅ 通过' if success else '❌ 失败'}\n")
            if success and result is not None:
                f.write(f"  年化收益率: {result.get('Return (Ann.) [%]', 0):.2f}%\n")
                f.write(f"  夏普比率: {result.get('Sharpe Ratio', 0):.3f}\n")
                f.write(f"  最大回撤: {result.get('Max. Drawdown [%]', 0):.1f}%\n")
                f.write(f"  总交易次数: {result.get('# Trades', 0)}\n")
    
    # 保存成功策略的详细结果为CSV
    successful_results = []
    for strategy_name, (success, result) in results.items():
        if success and result is not None:
            # 保存每个成功策略的完整回测结果
            try:
                strategy_csv_file = f"logs/test_{strategy_name.replace('：', '_').replace(' ', '_')}_{timestamp}.csv"
                result.to_csv(strategy_csv_file)
                print(f"✅ {strategy_name} 详细结果已保存至: {strategy_csv_file}")
                
                # 收集汇总信息
                successful_results.append({
                    '策略名称': strategy_name,
                    '年化收益率(%)': result.get('Return (Ann.) [%]', 0),
                    '夏普比率': result.get('Sharpe Ratio', 0),
                    '最大回撤(%)': result.get('Max. Drawdown [%]', 0),
                    '总交易次数': result.get('# Trades', 0),
                    '胜率(%)': result.get('Win Rate [%]', 0),
                    'Sortino比率': result.get('Sortino Ratio', 0),
                    'Calmar比率': result.get('Calmar Ratio', 0)
                })
            except Exception as e:
                print(f"⚠️ 保存{strategy_name}详细结果时出错: {e}")
    
    # 保存策略对比汇总表
    if successful_results:
        summary_df = pd.DataFrame(successful_results)
        summary_file = f"logs/test_summary_{timestamp}.csv"
        summary_df.to_csv(summary_file, index=False, encoding='utf-8-sig')
        print(f"📊 策略对比汇总表已保存至: {summary_file}")
    
    print(f"\n📄 详细测试报告已保存至: {report_file}")
    
    return results

if __name__ == "__main__":
    results = main() 