#!/usr/bin/env python3
"""
Mac上Python3绘图模块Matplotlib中文方框乱码问题解决测试
基于BruceD_的简单方法 - 使用Arial Unicode MS字体
"""

import matplotlib.pyplot as plt
import numpy as np
from datetime import datetime

def find_chinese_fonts():
    """查找Mac中支持中文的字体"""
    print("正在查找Mac中支持中文的字体...")
    
    from matplotlib.font_manager import FontManager
    fm = FontManager()
    mat_fonts = set(f.name for f in fm.ttflist)
    
    # 常见的Mac中文字体
    chinese_fonts = [
        'Arial Unicode MS',  # 推荐字体
        'Heiti TC',          # 黑体TC
        'Arial Black',       # Arial Black
        'PingFang SC',       # 苹方SC  
        'Songti SC',         # 宋体SC
        'Kaiti SC',          # 楷体SC
    ]
    
    print("Mac系统中可用的中文字体:")
    available_fonts = []
    for font in chinese_fonts:
        if font in mat_fonts:
            print(f"  ✅ {font}")
            available_fonts.append(font)
        else:
            print(f"  ❌ {font}")
    
    return available_fonts

def setup_chinese_font():
    """设置matplotlib支持中文显示"""
    print("\n设置matplotlib支持中文显示...")
    
    # 支持中文 - 使用Arial Unicode MS
    plt.rcParams['font.sans-serif'] = ['Arial Unicode MS']  # 用来正常显示中文标签
    plt.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号
    
    print("✅ 字体设置完成: Arial Unicode MS")

def test_chinese_display():
    """测试中文显示效果"""
    print("\n开始测试中文显示效果...")
    
    # 创建测试数据
    x = np.linspace(0, 10, 100)
    y1 = np.sin(x)
    y2 = np.cos(x)
    
    # 创建图形
    fig, axes = plt.subplots(2, 2, figsize=(12, 8))
    fig.suptitle('Mac上Matplotlib中文显示测试', fontsize=16, fontweight='bold')
    
    # 测试1: 基本线图
    ax1 = axes[0, 0]
    ax1.plot(x, y1, label='正弦波', color='blue')
    ax1.plot(x, y2, label='余弦波', color='red')
    ax1.set_title('三角函数图像')
    ax1.set_xlabel('横坐标 (弧度)')
    ax1.set_ylabel('纵坐标 (数值)')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 测试2: 柱状图
    ax2 = axes[0, 1]
    categories = ['苹果', '香蕉', '橙子', '葡萄', '草莓']
    values = [23, 45, 56, 78, 32]
    bars = ax2.bar(categories, values, color=['red', 'yellow', 'orange', 'purple', 'pink'])
    ax2.set_title('水果销量统计')
    ax2.set_xlabel('水果种类')
    ax2.set_ylabel('销量 (千克)')
    
    # 在柱子上添加数值标签
    for bar, value in zip(bars, values):
        height = bar.get_height()
        ax2.text(bar.get_x() + bar.get_width()/2., height + 1,
                f'{value}千克', ha='center', va='bottom')
    
    # 测试3: 饼图
    ax3 = axes[1, 0]
    labels = ['北京', '上海', '广州', '深圳', '杭州']
    sizes = [30, 25, 20, 15, 10]
    colors = ['gold', 'lightcoral', 'lightskyblue', 'lightgreen', 'plum']
    
    wedges, texts, autotexts = ax3.pie(sizes, labels=labels, colors=colors, 
                                      autopct='%1.1f%%', startangle=90)
    ax3.set_title('城市人口分布')
    
    # 测试4: 散点图和文本
    ax4 = axes[1, 1]
    np.random.seed(42)
    x_scatter = np.random.randn(50)
    y_scatter = np.random.randn(50)
    colors_scatter = np.random.rand(50)
    
    scatter = ax4.scatter(x_scatter, y_scatter, c=colors_scatter, alpha=0.6, cmap='viridis')
    ax4.set_title('随机数据散点图')
    ax4.set_xlabel('X轴数据')
    ax4.set_ylabel('Y轴数据')
    
    # 添加中文注释
    ax4.text(0.05, 0.95, '这是中文注释\n测试字体显示效果', 
            transform=ax4.transAxes, fontsize=10,
            verticalalignment='top',
            bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.5))
    
    # 调整布局
    plt.tight_layout()
    
    # 保存图片
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f'mac_chinese_test_{timestamp}.png'
    plt.savefig(filename, dpi=150, bbox_inches='tight')
    print(f"✅ 测试图片已保存: {filename}")
    
    # 显示图形
    try:
        plt.show()
        print("✅ 图形显示成功")
    except Exception as e:
        print(f"⚠️  图形显示失败: {e}")
    
    plt.close()

def run_comprehensive_test():
    """运行综合测试"""
    print("=" * 60)
    print("Mac上Matplotlib中文字体显示测试")
    print("使用简单方法: Arial Unicode MS")
    print("=" * 60)
    
    # 1. 查找可用字体
    available_fonts = find_chinese_fonts()
    
    if 'Arial Unicode MS' not in available_fonts:
        print("\n❌ 警告: Arial Unicode MS字体不可用")
        print("尝试使用其他可用的中文字体...")
        if available_fonts:
            alternative_font = available_fonts[0]
            print(f"使用替代字体: {alternative_font}")
            plt.rcParams['font.sans-serif'] = [alternative_font]
            plt.rcParams['axes.unicode_minus'] = False
        else:
            print("❌ 未找到任何可用的中文字体")
            return False
    else:
        # 2. 设置中文字体
        setup_chinese_font()
    
    # 3. 测试中文显示
    test_chinese_display()
    
    print("\n" + "=" * 60)
    print("测试完成!")
    print("如果图片中的中文显示正常，说明问题已解决")
    print("该方法的优点:")
    print("- 不需要修改配置文件")
    print("- 不需要重启程序")
    print("- 只需要几行代码")
    print("- 使用系统自带字体")
    print("=" * 60)
    
    return True

if __name__ == "__main__":
    success = run_comprehensive_test()
    if success:
        print("\n🎉 Mac上matplotlib中文显示问题解决成功!")
    else:
        print("\n❌ 测试失败，请检查系统字体安装情况")