"""
HMM算法交易教学演示
使用隐马尔可夫模型进行市场状态检测和交易策略

主要功能：
1. 使用yfinance获取实时股票数据
2. 训练HMM模型识别市场状态（牛市/熊市）
3. 基于状态预测执行交易策略
4. 可视化分析和回测结果比较
"""

import warnings
warnings.filterwarnings('ignore')

import numpy as np
import pandas as pd
import yfinance as yf
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timedelta
from hmmlearn.hmm import GaussianHMM
from sklearn.preprocessing import StandardScaler
import pickle
import sys
from typing import Tuple, Dict, List

plt.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False


class HMMMarketRegimeDetector:
    """HMM市场状态检测器"""
    
    def __init__(self, n_components: int = 2, covariance_type: str = "full", n_iter: int = 1000):
        self.n_components = n_components
        self.model = GaussianHMM(
            n_components=n_components,
            covariance_type=covariance_type,
            n_iter=n_iter,
            random_state=42
        )
        self.scaler = StandardScaler()
        self.is_fitted = False
        
    def prepare_features(self, prices: pd.Series, window: int = 10) -> np.ndarray:
        """准备HMM模型的特征数据"""
        df = pd.DataFrame({'price': prices})
        
        # 计算收益率
        df['returns'] = df['price'].pct_change()
        
        # 计算波动率（滚动标准差）
        df['volatility'] = df['returns'].rolling(window=window).std()
        
        # 计算价格动量
        df['momentum'] = df['price'] / df['price'].shift(window) - 1
        
        # 计算相对强弱指标RSI
        delta = df['returns']
        gain = (delta.where(delta > 0, 0)).rolling(window=window).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=window).mean()
        rs = gain / loss
        df['rsi'] = 100 - (100 / (1 + rs))
        
        # 选择特征
        features = ['returns', 'volatility', 'momentum', 'rsi']
        feature_data = df[features].dropna()
        
        return feature_data.values
        
    def fit(self, prices: pd.Series) -> 'HMMMarketRegimeDetector':
        """训练HMM模型"""
        print("正在准备特征数据...")
        features = self.prepare_features(prices)
        
        print("正在标准化特征...")
        features_scaled = self.scaler.fit_transform(features)
        
        print(f"正在训练HMM模型（{self.n_components}个状态）...")
        self.model.fit(features_scaled)
        self.is_fitted = True
        
        print(f"模型训练完成，对数似然: {self.model.score(features_scaled):.2f}")
        return self
        
    def predict_states(self, prices: pd.Series) -> np.ndarray:
        """预测市场状态"""
        if not self.is_fitted:
            raise ValueError("模型尚未训练，请先调用fit方法")
            
        features = self.prepare_features(prices)
        features_scaled = self.scaler.transform(features)
        return self.model.predict(features_scaled)
        
    def predict_proba(self, prices: pd.Series) -> np.ndarray:
        """预测状态概率"""
        if not self.is_fitted:
            raise ValueError("模型尚未训练，请先调用fit方法")
            
        features = self.prepare_features(prices)
        features_scaled = self.scaler.transform(features)
        return self.model.predict_proba(features_scaled)


class HMMTradingStrategy:
    """基于HMM的交易策略"""
    
    def __init__(self, detector: HMMMarketRegimeDetector, initial_capital: float = 100000):
        self.detector = detector
        self.initial_capital = initial_capital
        self.positions = []  # 持仓记录
        self.cash = initial_capital
        self.portfolio_value = []
        self.trades = []
        
    def identify_regime_characteristics(self, prices: pd.Series, states: np.ndarray) -> Dict:
        """分析各个状态的特征"""
        returns = prices.pct_change().dropna()
        
        # 确保长度匹配
        min_len = min(len(returns), len(states))
        returns = returns.iloc[:min_len]
        states = states[:min_len]
        
        regime_stats = {}
        for state in range(self.detector.n_components):
            mask = states == state
            state_returns = returns[mask]
            
            regime_stats[state] = {
                'mean_return': state_returns.mean(),
                'volatility': state_returns.std(),
                'sharpe_ratio': state_returns.mean() / state_returns.std() if state_returns.std() > 0 else 0,
                'frequency': mask.sum() / len(states),
                'periods': mask.sum()
            }
            
        return regime_stats
        
    def backtest(self, prices: pd.Series, states: np.ndarray) -> Dict:
        """执行回测"""
        returns = prices.pct_change().dropna()
        
        # 确保长度匹配
        min_len = min(len(returns), len(states))
        prices = prices.iloc[:min_len+1]  # +1因为returns比prices少1个
        returns = returns.iloc[:min_len]
        states = states[:min_len]
        
        # 分析状态特征以确定哪个是"好"状态
        regime_stats = self.identify_regime_characteristics(prices[:-1], states)
        
        # 选择夏普比率最高的状态作为交易状态
        best_state = max(regime_stats.keys(), key=lambda x: regime_stats[x]['sharpe_ratio'])
        
        print(f"\n状态分析:")
        print(f"best_state = {best_state}")  # 添加调试信息
        for state, stats in regime_stats.items():
            status = "交易状态" if state == best_state else "观望状态"
            print(f"状态 {state} ({status}): 平均收益={stats['mean_return']:.4f}, "
                  f"波动率={stats['volatility']:.4f}, 夏普比率={stats['sharpe_ratio']:.4f}")
        
        print(f"交易逻辑: 当状态={best_state}时买入，当状态!={best_state}时卖出")  # 添加调试信息
        
        # 执行交易策略
        position = 0  # 0: 空仓, 1: 满仓
        portfolio_values = [self.initial_capital]
        buy_and_hold_values = [self.initial_capital]
        
        print(f"\n=== 交易信号详细记录 ===")
        print(f"交易状态(best_state): {best_state}")
        print("日期\t\t前状态→当前状态\t持仓\t信号\t价格\t状态转换说明")
        print("-" * 80)
        
        prev_state = None
        trade_count = 0
        
        for i in range(len(states)):
            current_price = prices.iloc[i+1]  # +1因为要用明天的价格
            current_return = returns.iloc[i]
            current_state = states[i]
            current_date = prices.index[i+1].strftime('%Y-%m-%d')
            
            # 状态转换信息
            if prev_state is None:
                state_change = f"初始→{current_state}"
            elif prev_state != current_state:
                state_change = f"{prev_state}→{current_state} ★"  # ★标记状态转换
            else:
                state_change = f"{prev_state}→{current_state}"
            
            # 持仓状态
            position_str = "持仓" if position == 1 else "空仓"
            
            # HMM策略：只在"好"状态持仓
            trade_signal = "无"
            explanation = ""
            
            if current_state == best_state and position == 0:
                # 买入信号
                position = 1
                trade_signal = "买入"
                trade_count += 1
                self.trades.append(('BUY', prices.index[i+1], current_price))
                explanation = f"进入交易状态{best_state}，执行买入"
                
            elif current_state != best_state and position == 1:
                # 卖出信号
                position = 0
                trade_signal = "卖出"
                trade_count += 1
                self.trades.append(('SELL', prices.index[i+1], current_price))
                explanation = f"离开交易状态{best_state}，执行卖出"
                
            elif current_state == best_state and position == 1:
                explanation = f"保持在交易状态{best_state}，继续持仓"
                
            elif current_state != best_state and position == 0:
                explanation = f"保持在观望状态{current_state}，继续空仓"
            
            # 只打印有交易信号或状态转换的记录
            if trade_signal != "无" or "★" in state_change or i < 5 or i == len(states)-1:
                print(f"{current_date}\t{state_change}\t\t{position_str}\t{trade_signal}\t{current_price:.0f}\t{explanation}")
            
            prev_state = current_state
            
            # 计算组合价值
            if position == 1:
                # 持仓状态，跟随市场收益
                portfolio_values.append(portfolio_values[-1] * (1 + current_return))
            else:
                # 空仓状态，保持现金
                portfolio_values.append(portfolio_values[-1])
            
            # Buy & Hold 基准
            buy_and_hold_values.append(buy_and_hold_values[-1] * (1 + current_return))
        
        print(f"\n=== 状态转换和交易统计 ===")
        print(f"总交易次数: {trade_count}")
        
        # 统计状态转换次数
        state_transitions = 0
        for i in range(1, len(states)):
            if states[i] != states[i-1]:
                state_transitions += 1
        print(f"状态转换次数: {state_transitions}")
        print(f"交易/转换比率: {trade_count/state_transitions:.2f}" if state_transitions > 0 else "交易/转换比率: N/A")
        
        # 计算绩效指标
        hmm_returns = pd.Series(portfolio_values).pct_change().dropna()
        bh_returns = pd.Series(buy_and_hold_values).pct_change().dropna()
        
        results = {
            'hmm_portfolio': portfolio_values,
            'buy_hold_portfolio': buy_and_hold_values,
            'hmm_total_return': (portfolio_values[-1] / portfolio_values[0] - 1) * 100,
            'bh_total_return': (buy_and_hold_values[-1] / buy_and_hold_values[0] - 1) * 100,
            'hmm_sharpe': hmm_returns.mean() / hmm_returns.std() * np.sqrt(252) if hmm_returns.std() > 0 else 0,
            'bh_sharpe': bh_returns.mean() / bh_returns.std() * np.sqrt(252) if bh_returns.std() > 0 else 0,
            'hmm_max_drawdown': self._calculate_max_drawdown(portfolio_values),
            'bh_max_drawdown': self._calculate_max_drawdown(buy_and_hold_values),
            'regime_stats': regime_stats,
            'best_regime': best_state,
            'trades': self.trades
        }
        
        return results
    
    def _calculate_max_drawdown(self, portfolio_values: List[float]) -> float:
        """计算最大回撤"""
        peak = portfolio_values[0]
        max_dd = 0
        
        for value in portfolio_values:
            if value > peak:
                peak = value
            drawdown = (peak - value) / peak
            if drawdown > max_dd:
                max_dd = drawdown
                
        return max_dd * 100


class HMMTradingVisualizer:
    """HMM交易策略可视化"""
    
    @staticmethod
    def plot_regime_detection(prices: pd.Series, states: np.ndarray, 
                            regime_stats: Dict, title: str = "HMM市场状态检测"):
        """绘制状态检测结果"""
        
        # 确保长度匹配
        min_len = min(len(prices) - 1, len(states))  # -1因为要错开一位
        plot_prices = prices.iloc[1:min_len+1]  # 从第二个开始
        plot_states = states[:min_len]
        
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(15, 10))
        
        # 子图1：价格和状态
        colors = ['red', 'green', 'blue', 'orange']
        for state in range(len(regime_stats)):
            mask = plot_states == state
            state_info = regime_stats[state]
            ax1.scatter(plot_prices.index[mask], plot_prices.values[mask], 
                       c=colors[state], alpha=0.6, s=20,
                       label=f'状态{state} (收益: {state_info["mean_return"]:.4f})')
        
        ax1.plot(plot_prices.index, plot_prices.values, 'k-', alpha=0.3, linewidth=1)
        ax1.set_title(f'{title} - 价格与市场状态')
        ax1.set_ylabel('价格')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # 子图2：状态序列
        ax2.plot(plot_prices.index, plot_states, 'b-', linewidth=2)
        ax2.fill_between(plot_prices.index, plot_states, alpha=0.3)
        ax2.set_title('市场状态时间序列')
        ax2.set_xlabel('日期')
        ax2.set_ylabel('状态')
        ax2.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.show()
    
    @staticmethod
    def plot_backtest_results(prices: pd.Series, results: Dict, 
                            title: str = "HMM交易策略回测结果"):
        """绘制回测结果"""
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        
        # 子图1：组合价值对比
        dates = prices.index[1:len(results['hmm_portfolio'])+1]
        axes[0,0].plot(dates, results['hmm_portfolio'], 'b-', 
                      label=f"HMM策略 ({results['hmm_total_return']:.2f}%)", linewidth=2)
        axes[0,0].plot(dates, results['buy_hold_portfolio'], 'r--', 
                      label=f"买入持有 ({results['bh_total_return']:.2f}%)", linewidth=2)
        axes[0,0].set_title('组合价值对比')
        axes[0,0].set_ylabel('组合价值')
        axes[0,0].legend()
        axes[0,0].grid(True, alpha=0.3)
        
        # 子图2：交易信号
        axes[0,1].plot(prices.index, prices.values, 'k-', alpha=0.7, linewidth=1)
        
        for trade_type, trade_date, trade_price in results['trades']:
            color = 'green' if trade_type == 'BUY' else 'red'
            marker = '^' if trade_type == 'BUY' else 'v'
            axes[0,1].scatter(trade_date, trade_price, c=color, marker=marker, 
                            s=100, alpha=0.8, edgecolors='black')
        
        axes[0,1].set_title('交易信号')
        axes[0,1].set_ylabel('价格')
        axes[0,1].grid(True, alpha=0.3)
        
        # 子图3：收益率分布对比
        hmm_returns = pd.Series(results['hmm_portfolio']).pct_change().dropna()
        bh_returns = pd.Series(results['buy_hold_portfolio']).pct_change().dropna()
        
        axes[1,0].hist(hmm_returns, bins=50, alpha=0.7, label='HMM策略', density=True)
        axes[1,0].hist(bh_returns, bins=50, alpha=0.7, label='买入持有', density=True)
        axes[1,0].set_title('收益率分布对比')
        axes[1,0].set_xlabel('日收益率')
        axes[1,0].set_ylabel('概率密度')
        axes[1,0].legend()
        axes[1,0].grid(True, alpha=0.3)
        
        # 子图4：绩效指标对比
        metrics = ['总收益率(%)', '夏普比率', '最大回撤(%)']
        hmm_values = [results['hmm_total_return'], results['hmm_sharpe'], results['hmm_max_drawdown']]
        bh_values = [results['bh_total_return'], results['bh_sharpe'], results['bh_max_drawdown']]
        
        x = np.arange(len(metrics))
        width = 0.35
        
        axes[1,1].bar(x - width/2, hmm_values, width, label='HMM策略', alpha=0.8)
        axes[1,1].bar(x + width/2, bh_values, width, label='买入持有', alpha=0.8)
        
        axes[1,1].set_title('绩效指标对比')
        axes[1,1].set_ylabel('值')
        axes[1,1].set_xticks(x)
        axes[1,1].set_xticklabels(metrics)
        axes[1,1].legend()
        axes[1,1].grid(True, alpha=0.3)
        
        # 在每个柱子上显示数值
        for i, (hmm_val, bh_val) in enumerate(zip(hmm_values, bh_values)):
            axes[1,1].text(i - width/2, hmm_val + 0.5, f'{hmm_val:.2f}', 
                          ha='center', va='bottom')
            axes[1,1].text(i + width/2, bh_val + 0.5, f'{bh_val:.2f}', 
                          ha='center', va='bottom')
        
        plt.suptitle(title, fontsize=16)
        plt.tight_layout()
        plt.show()


def get_stock_data(symbol: str, start_date: str, end_date: str) -> pd.Series:
    """使用yfinance获取股票数据"""
    print(f"正在获取 {symbol} 从 {start_date} 到 {end_date} 的数据...")
    
    stock = yf.Ticker(symbol)
    data = stock.history(start=start_date, end=end_date, auto_adjust=True)
    
    if data.empty:
        raise ValueError(f"无法获取 {symbol} 的数据")
    
    print(f"成功获取 {len(data)} 天的数据")
    return data['Close']


def main():
    """主演示函数"""
    print("="*60)
    print("HMM算法交易教学演示")
    print("="*60)
    
    # 从命令行参数获取标的代码，默认为上证综指
    if len(sys.argv) > 1:
        symbol = sys.argv[1]
    else:
        symbol = "GC=F"  # 上证综指
    
    # 根据不同标的设置合适的参数
    train_start = "2015-01-01"
    train_end = "2023-12-31"
    test_start = "2024-01-01"
    test_end = "2025-06-25"
    
    print(f"分析标的: {symbol}")
    print(f"训练期间: {train_start} 到 {train_end}")
    print(f"测试期间: {test_start} 到 {test_end}")
    
    try:
        # 1. 获取训练数据
        print("\n步骤1: 获取训练数据")
        train_prices = get_stock_data(symbol, train_start, train_end)
        
        # 2. 训练HMM模型
        print("\n步骤2: 训练HMM模型")
        detector = HMMMarketRegimeDetector(n_components=2, n_iter=1000)
        detector.fit(train_prices)
        
        # 3. 获取测试数据并预测状态
        print("\n步骤3: 获取测试数据并预测状态")
        test_prices = get_stock_data(symbol, test_start, test_end)
        test_states = detector.predict_states(test_prices)
        
        # 4. 执行交易策略回测
        print("\n步骤4: 执行交易策略回测")
        strategy = HMMTradingStrategy(detector, initial_capital=100000)
        results = strategy.backtest(test_prices, test_states)
        
        # 5. 显示结果
        print("\n" + "="*60)
        print("回测结果汇总")
        print("="*60)
        print(f"测试期间: {test_start} 到 {test_end}")
        print(f"HMM策略总收益: {results['hmm_total_return']:.2f}%")
        print(f"买入持有收益: {results['bh_total_return']:.2f}%")
        print(f"HMM策略夏普比率: {results['hmm_sharpe']:.3f}")
        print(f"买入持有夏普比率: {results['bh_sharpe']:.3f}")
        print(f"HMM策略最大回撤: {results['hmm_max_drawdown']:.2f}%")
        print(f"买入持有最大回撤: {results['bh_max_drawdown']:.2f}%")
        print(f"交易次数: {len(results['trades'])}")
        
        # 6. 可视化结果
        print("\n步骤6: 生成可视化图表")
        visualizer = HMMTradingVisualizer()
        
        # 绘制状态检测结果
        visualizer.plot_regime_detection(
            test_prices, test_states, results['regime_stats'],
            f"{symbol} HMM市场状态检测 ({test_start} - {test_end})"
        )
        
        # 绘制回测结果
        visualizer.plot_backtest_results(
            test_prices, results,
            f"{symbol} HMM交易策略回测 ({test_start} - {test_end})"
        )
        
        # 7. 结论和教学要点
        print("\n" + "="*60)
        print("教学要点和结论")
        print("="*60)
        print("1. HMM模型能够识别市场的不同状态（如牛市/熊市）")
        print("2. 通过在有利状态持仓，不利状态空仓，可以改善风险调整后收益")
        print("3. HMM策略的有效性取决于:")
        print("   - 市场状态的持续性和可预测性")
        print("   - 特征选择的合理性")
        print("   - 模型参数的适当调整")
        print("4. 实际应用时需要考虑:")
        print("   - 交易成本和滑点")
        print("   - 模型的稳定性和泛化能力")
        print("   - 风险管理和资金管理")
        
        if results['hmm_total_return'] > results['bh_total_return']:
            print(f"\n在此测试中，HMM策略表现 {'优于' if results['hmm_total_return'] > results['bh_total_return'] else '不如'} 买入持有策略")
        else:
            print(f"\n在此测试中，HMM策略表现不如买入持有策略")
            print("这可能因为:")
            print("- 测试期间市场趋势相对单一")
            print("- 特征选择需要优化")
            print("- 交易频率过高导致交易成本侵蚀收益")
            
    except Exception as e:
        print(f"程序执行出错: {e}")
        print("请检查网络连接和数据获取是否正常")


if __name__ == "__main__":
    main()