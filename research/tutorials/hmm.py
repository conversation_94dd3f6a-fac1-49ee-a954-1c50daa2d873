"""
使用隐马尔可夫模型 (HMM) 对股票市场环境进行分析。

该脚本执行以下步骤：
1.  从 yfinance 获取指定股票的历史数据。
2.  进行特征工程，计算回报率、动量、波动率和交易量变化。
3.  使用高斯隐马尔可夫模型 (GaussianHMM) 训练数据，以识别不同的市场环境（状态）。
4.  可视化不同市场环境下的股价表现。
5.  分析每个市场环境的特征。
6.  根据当前识别出的市场环境提供简单的交易策略建议。
"""

import warnings
from datetime import datetime, timedelta
from typing import Dict, List, Tuple

import matplotlib.pyplot as plt
import numpy as np
import pandas as pd
import yfinance as yf
from hmmlearn.hmm import GaussianHMM
from sklearn.preprocessing import StandardScaler

# 忽略 hmmlearn 中关于未来版本变更的警告
warnings.filterwarnings(
    "ignore",
    category=FutureWarning,
    module="hmmlearn.base",
)

# --- 1. 常量与配置 ---
TICKER: str = "SPY"  # 股票代码 (以微软为例)
N_COMPONENTS: int = 3  # HMM 模型的隐藏状态数量
N_ITER: int = 1000  # HMM 模型的迭代次数
RANDOM_STATE: int = 42  # 随机种子，确保结果可复现
DAYS_AGO: int = 1000  # 获取历史数据的天数
FEATURES: List[str] = [
    "Return",
    "Momentum_10d",
    "Volatility_10d",
    "Volume_Change",
]


def get_stock_data(ticker: str, days_ago: int) -> pd.DataFrame:
    """
    从 yfinance 获取股票数据。

    Args:
        ticker (str): 股票代码。
        days_ago (int): 获取过去多少天的数据。

    Returns:
        pd.DataFrame: 包含 OHLCV 数据的 DataFrame。
    """
    print(f"正在获取 {ticker} 过去 {days_ago} 天的数据...")
    end_date = datetime.now()
    start_date = end_date - timedelta(days=days_ago)
    data = yf.download(ticker, start=start_date, end=end_date, progress=False)
    print("数据获取完成。")
    return data


def create_features(data: pd.DataFrame) -> pd.DataFrame:
    """
    基于原始股价数据创建特征。

    Args:
        data (pd.DataFrame): 包含收盘价和交易量的 DataFrame。

    Returns:
        pd.DataFrame: 包含新计算特征的 DataFrame。
    """
    df = data.copy()

    # 计算每日回报率
    df["Return"] = df["Close"].pct_change()
    # 计算动量（10日回报率）
    df["Momentum_10d"] = df["Close"].pct_change(10)
    # 计算波动率（10日回报率的标准差）
    df["Volatility_10d"] = df["Return"].rolling(window=10).std()
    # 计算交易量变化率
    df["Volume_Change"] = df["Volume"].pct_change()

    # 删除因计算产生的含有 NaN 的行
    df = df.dropna()
    return df


def train_hmm_model(
    X: np.ndarray, n_components: int, n_iter: int, random_state: int
) -> GaussianHMM:
    """
    训练高斯隐马尔可夫模型。

    Args:
        X (np.ndarray): 用于训练的特征数据。
        n_components (int): 隐藏状态的数量。
        n_iter (int): 训练的迭代次数。
        random_state (int): 随机种子。

    Returns:
        GaussianHMM: 训练好的 HMM 模型。
    """
    print("正在训练 HMM 模型...")
    model = GaussianHMM(
        n_components=n_components,
        covariance_type="full",
        n_iter=n_iter,
        random_state=random_state,
    )
    model.fit(X)
    print("模型训练完成。")
    return model


def plot_market_regimes(
    data: pd.DataFrame, hidden_states: np.ndarray, ticker: str
) -> None:
    """
    可视化不同市场环境下的股票表现。

    Args:
        data (pd.DataFrame): 包含收盘价和市场状态的 DataFrame。
        hidden_states (np.ndarray): 模型预测的隐藏状态序列。
        ticker (str): 股票代码。
    """
    plt.figure(figsize=(15, 8))
    for i in range(N_COMPONENTS):
        idx = hidden_states == i
        plt.plot(
            data.index[idx],
            data["Close"][idx],
            "o",  # 使用点来标记
            markersize=4,
            label=f"Regime {i}",
        )
    plt.title(f"{ticker} Stock Price in Different Market Regimes")
    plt.xlabel("Date")
    plt.ylabel("Close Price")
    plt.legend()
    plt.grid(True)
    plt.show()


def analyze_regimes(data: pd.DataFrame, features: List[str], n_components: int) -> pd.DataFrame:
    """
    分析并打印每个市场环境的特征。

    Args:
        data (pd.DataFrame): 包含特征和市场状态的 DataFrame。
        features (List[str]): 用于分析的特征列表。
        n_components (int): 隐藏状态的数量。
        
    Returns:
        pd.DataFrame: 包含各状态特征分析结果的 DataFrame。
    """
    analysis_df = pd.DataFrame()
    for i in range(n_components):
        regime_data = data[data["Market_Regime"] == i]
        regime_features = regime_data[features].mean()
        avg_return = regime_data["Return"].mean()

        analysis_df[f"Regime_{i}"] = pd.Series(
            {
                **dict(zip(features, regime_features)),
                "Avg_Daily_Return": avg_return,
                "Count": len(regime_data),
            }
        )
    return analysis_df


def get_trading_advice(current_regime: int) -> str:
    """
    根据当前市场环境生成简单的交易建议。

    Args:
        current_regime (int): 当前最新的市场状态。

    Returns:
        str: 交易建议。
    """
    if current_regime == 0:
        return "建议增加动量因子暴露，减少波动率因子暴露。"
    elif current_regime == 1:
        return "建议减少动量因子暴露，增加价值因子暴露。"
    else:
        return "建议增加波动率因子暴露，减少动量因子暴露。"


def main():
    """主执行函数"""
    # 1. 数据获取
    raw_data = get_stock_data(TICKER, DAYS_AGO)
    if raw_data.empty:
        print("未能获取到数据，程序退出。")
        return

    # 2. 特征工程
    feature_data = create_features(raw_data)

    # 3. 数据准备和标准化
    X = feature_data[FEATURES].values
    scaler = StandardScaler()
    X_scaled = scaler.fit_transform(X)

    # 4. 训练 HMM 模型
    hmm_model = train_hmm_model(X_scaled, N_COMPONENTS, N_ITER, RANDOM_STATE)

    # 5. 预测隐藏状态
    hidden_states = hmm_model.predict(X_scaled)
    feature_data["Market_Regime"] = hidden_states

    # 6. 分析每个状态的特征
    regime_analysis = analyze_regimes(feature_data, FEATURES, N_COMPONENTS)
    print("\n不同市场环境下的特征分析:")
    print(regime_analysis.round(4))

    # 7. 根据当前市场环境优化因子暴露
    current_regime = hidden_states[-1]
    print(f"\n当前市场环境: Regime {current_regime}")
    advice = get_trading_advice(current_regime)
    print(advice)
    
    # 8. 可视化结果
    plot_market_regimes(feature_data, hidden_states, TICKER)


if __name__ == "__main__":
    main()