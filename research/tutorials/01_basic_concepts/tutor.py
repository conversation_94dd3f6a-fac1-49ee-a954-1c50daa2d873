import numpy as np
from hmmlearn import hmm
import warnings

# 忽略未来版本可能产生的警告，保持输出干净
warnings.filterwarnings("ignore", category=FutureWarning)

# --- 阶段一：模型定义与数据生成 (与之前相同) ---
n_states = 2
n_observations = 3
start_prob = np.array([0.7, 0.3])
trans_mat = np.array([[0.8, 0.2], [0.4, 0.6]])
emission_prob = np.array([[0.6, 0.3, 0.1], [0.1, 0.4, 0.5]])

true_model = hmm.CategoricalHMM(n_components=n_states, n_features=n_observations, init_params="")
true_model.startprob_ = start_prob
true_model.transmat_ = trans_mat
true_model.emissionprob_ = emission_prob

n_samples = 5000
X, Z = true_model.sample(n_samples=n_samples)

print("--- 使用了更大的数据集(n=5000) ---\n")

# --- 阶段二：模型训练 (专业版：多次初始化，选择最优) ---

print("--- 开始进行专业版模型训练 ---")
# 我们将进行多次训练，以避免陷入局部最优解
n_restarts = 20  # 设置尝试训练的次数
best_score = -np.inf
best_model = None

for i in range(n_restarts):
    # 在每次循环中，创建一个全新的、随机初始化的模型
    # 注意：我们不再固定random_state，让每一次的起点都不同
    model = hmm.CategoricalHMM(n_components=n_states, n_features=n_observations,
                               n_iter=500, tol=0.001,
                               params='ste', init_params='ste')
    
    # 训练模型
    model.fit(X)
    
    # 计算当前模型对数据的对数似然分数 (log likelihood)
    # 分数越高，代表模型对数据的解释能力越强
    current_score = model.score(X)
    
    # 如果当前模型比我们之前见过的所有模型都好，就保存它
    if current_score > best_score:
        best_score = current_score
        best_model = model
        print(f"发现更好的模型，第 {i+1} 次尝试，对数似然分数: {best_score:.2f}")

print("\n--- 训练完成！选出最优模型 ---")

# 使用我们找到的最佳模型
model = best_model

# --- 阶段三：结果展示与评估 ---

print("\n--- 最优模型的HMM参数 ---")
np.set_printoptions(precision=3)
print("学习到的初始概率 (π'):", model.startprob_)
print("学习到的转移矩阵 (A'):\n", model.transmat_)
print("学习到的发射矩阵 (B'):\n", model.emissionprob_)

print("\n--- 与真实参数对比 ---")
print("真实的转移矩阵 (A):\n", trans_mat)
print("真实发射矩阵 (B):\n", emission_prob)

# 模型预测
predicted_Z = model.predict(X)

# 结果评估
sunny_state_learned = np.argmax(model.emissionprob_[:, 0])
if sunny_state_learned != 0:
    is_sunny_learned = (predicted_Z == sunny_state_learned)
    is_rainy_learned = (predicted_Z != sunny_state_learned)
    corrected_predicted_Z = np.copy(predicted_Z)
    corrected_predicted_Z[is_sunny_learned] = 0
    corrected_predicted_Z[is_rainy_learned] = 1
else:
    corrected_predicted_Z = predicted_Z

accuracy = np.mean(corrected_predicted_Z == Z) * 100

print(f"\n--- 模型性能评估 ---")
print(f"使用最优模型，经过校正后，模型预测的准确率为: {accuracy:.2f}%")