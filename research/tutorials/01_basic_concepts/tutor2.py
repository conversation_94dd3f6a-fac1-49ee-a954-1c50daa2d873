import numpy as np
from hmmlearn import hmm
import warnings

warnings.filterwarnings("ignore", category=FutureWarning)

# --- 阶段一：模型定义 (高区分度版) ---
print("--- 任务场景: 高区分度的世界 ---")
n_states = 2
n_observations = 3
start_prob = np.array([0.7, 0.3])

# 真实转移矩阵保持不变
trans_mat = np.array([[0.8, 0.2], [0.4, 0.6]])

# #######################################################
# # 关键变更：创建一个没有模糊性的发射矩阵 B            #
# #######################################################
# 晴天时: P(散步)=0.8, P(购物)=0.15, P(宅家)=0.05
# 雨天时: P(散步)=0.05, P(购物)=0.15, P(宅家)=0.8
emission_prob = np.array([
    [0.8, 0.15, 0.05],  # 晴天: 散步概率极高
    [0.05, 0.15, 0.8]   # 雨天: 宅家概率极高
])
# 注意：现在“购物”的概率在两种天气下相同，成为一个无用信息，
# 模型必须依赖“散步”和“宅家”这两个强信号。

true_model = hmm.CategoricalHMM(n_components=n_states, n_features=n_observations, init_params="")
true_model.startprob_ = start_prob
true_model.transmat_ = trans_mat
true_model.emissionprob_ = emission_prob

n_samples = 5000
X, Z = true_model.sample(n_samples=n_samples)


# --- 阶段二：模型训练 (专业版) ---
n_restarts = 10  # 减少次数，因为问题更简单
best_score = -np.inf
best_model = None

for i in range(n_restarts):
    model = hmm.CategoricalHMM(n_components=n_states, n_features=n_observations,
                               n_iter=500, tol=0.001,
                               params='ste', init_params='ste')
    model.fit(X)
    current_score = model.score(X)
    if current_score > best_score:
        best_score = current_score
        best_model = model

model = best_model

# --- 阶段三：结果评估 ---
print("\n--- 最优模型的HMM参数 ---")
np.set_printoptions(precision=3)
print("学习到的发射矩阵 (B'):\n", model.emissionprob_)
print("\n--- 与真实发射矩阵 (B) 对比 ---")
print("真实发射矩阵 (B):\n", emission_prob)

predicted_Z = model.predict(X)
sunny_state_learned = np.argmax(model.emissionprob_[:, 0])
if sunny_state_learned != 0:
    is_sunny_learned = (predicted_Z == sunny_state_learned)
    is_rainy_learned = (predicted_Z != sunny_state_learned)
    corrected_predicted_Z = np.copy(predicted_Z)
    corrected_predicted_Z[is_sunny_learned] = 0
    corrected_predicted_Z[is_rainy_learned] = 1
else:
    corrected_predicted_Z = predicted_Z

accuracy = np.mean(corrected_predicted_Z == Z) * 100

print(f"\n--- 模型性能评估 ---")
print(f"在高区分度任务下，模型预测的准确率为: {accuracy:.2f}%")