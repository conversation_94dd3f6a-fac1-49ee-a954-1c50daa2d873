#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HMM趋势状态检测 - 前向滚动训练版本
基于趋势特征的HMM模型，采用前向滚动训练避免前视偏差
"""

import warnings
warnings.filterwarnings('ignore')

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from datetime import datetime, timedelta
import yfinance as yf
from hmmlearn.hmm import GaussianHMM
from sklearn.cluster import AgglomerativeClustering
from sklearn.mixture import GaussianMixture
import plotly.graph_objects as go
from plotly.subplots import make_subplots

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

# ----------------------------- #
# 1. 数据获取
# ----------------------------- #
TICKER = "^GSPC"  # S&P 500指数
START_DATE = "2000-01-01"
END_DATE = datetime.now().strftime('%Y-%m-%d')

print(f"正在获取 {TICKER} 数据 ({START_DATE} 到 {END_DATE})...")
prices = yf.download(TICKER, start=START_DATE, end=END_DATE)[['Close']]
prices.columns = [TICKER]
prices.columns.name = TICKER
print(f"数据获取完成，共 {len(prices)} 个交易日")

# ----------------------------- #
# 2. 趋势特征工程
# ----------------------------- #
def prepare_trend_features(prices_df: pd.DataFrame, short_ma: int = 7, long_ma: int = 30):
    """
    基于趋势的特征组合
    """
    inst = prices_df.columns.name
    
    # 1. 短期和长期移动平均
    prices_df[f'{inst}_ma_short'] = prices_df[inst].rolling(short_ma).mean()
    prices_df[f'{inst}_ma_long'] = prices_df[inst].rolling(long_ma).mean()
    
    # 2. 价格相对于移动平均的位置（趋势强度）
    prices_df[f'{inst}_price_ma_ratio'] = prices_df[inst] / prices_df[f'{inst}_ma_long']
    
    # 3. 移动平均的斜率（趋势方向）
    prices_df[f'{inst}_ma_slope'] = (
        prices_df[f'{inst}_ma_short'] / prices_df[f'{inst}_ma_short'].shift(5) - 1
    )
    
    # 4. 短期与长期移动平均的比值（趋势确认）
    prices_df[f'{inst}_ma_ratio'] = prices_df[f'{inst}_ma_short'] / prices_df[f'{inst}_ma_long']
    
    # 5. 价格动量
    prices_df[f'{inst}_momentum'] = prices_df[inst] / prices_df[inst].shift(10) - 1
    
    # 6. 波动率特征（年化波动率）
    prices_df[f'{inst}_volatility'] = prices_df[inst].pct_change().rolling(20).std() * np.sqrt(252)
    
    prices_df.dropna(inplace=True)
    
    # 组合特征
    feature_cols = [
        f'{inst}_price_ma_ratio',
        f'{inst}_ma_slope', 
        f'{inst}_ma_ratio',
        f'{inst}_momentum',
        f'{inst}_volatility'  # 新增波动率特征
    ]
    X = prices_df[feature_cols].values
    return prices_df, X

prices, prices_array = prepare_trend_features(prices, short_ma=7, long_ma=30)

# ----------------------------- #
# 3. Regime-Detection 封装
# ----------------------------- #
class RegimeDetection:
    def get_regimes_hmm(self, X, params):
        model = self.initialise_model(GaussianHMM(), params).fit(X)
        return model

    def get_regimes_clustering(self, params):
        return self.initialise_model(AgglomerativeClustering(), params)

    def get_regimes_gmm(self, X, params):
        return self.initialise_model(GaussianMixture(), params).fit(X)

    @staticmethod
    def initialise_model(model, params):
        for k, v in params.items():
            setattr(model, k, v)
        return model

# ----------------------------- #
# 4. 可视化工具
# ----------------------------- #
def plot_hidden_states(states, price_data):
    """
    可视化隐藏状态和价格数据
    """
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(15, 10), sharex=True)
    
    # 确保数据长度一致
    min_len = min(len(states), len(price_data))
    states = states[:min_len]
    price_slice = price_data.iloc[:min_len]
    
    # 绘制价格数据
    ax1.plot(price_slice.index, price_slice.iloc[:, 0], 'k-', alpha=0.8, linewidth=1.5, label='价格走势')
    ax1.set_ylabel('价格', fontsize=12)
    ax1.set_title('S&P 500期货价格与HMM检测的趋势状态', fontsize=14, fontweight='bold')
    ax1.grid(True, alpha=0.3)
    
    # 为不同状态着色背景
    # 状态0=下跌趋势（红色），状态1=上涨趋势（绿色），状态2=震荡市场（橙色）
    colors = {0: 'red', 1: 'green', 2: 'orange'}
    labels = {0: '下跌趋势', 1: '上涨趋势', 2: '震荡市场'}
    
    # 创建状态区域填充
    for state in [0, 1, 2]:
        mask = (states == state)
        if np.any(mask):
            # 找到连续的状态段
            state_changes = np.diff(np.concatenate(([False], mask, [False])).astype(int))
            starts = np.where(state_changes == 1)[0]
            ends = np.where(state_changes == -1)[0]
            
            for start, end in zip(starts, ends):
                if start < len(price_slice) and end <= len(price_slice):
                    ax1.axvspan(price_slice.index[start], price_slice.index[end-1], 
                               alpha=0.2, color=colors[state], 
                               label=f'状态 {state} ({labels[state]})' if start == starts[0] else "")
    
    # 移除重复的图例项
    handles, legend_labels = ax1.get_legend_handles_labels()
    by_label = dict(zip(legend_labels, handles))
    ax1.legend(by_label.values(), by_label.keys(), loc='upper left')
    
    # 绘制状态序列
    colors_line = ['red' if s == 0 else 'green' if s == 1 else 'orange' for s in states[:len(price_slice)]]
    ax2.scatter(price_slice.index, states[:len(price_slice)], c=colors_line, s=10, alpha=0.7)
    ax2.set_ylabel('趋势状态', fontsize=12)
    ax2.set_xlabel('日期', fontsize=12)
    ax2.set_yticks([0, 1, 2])
    ax2.set_yticklabels(['状态0\n(下跌趋势)', '状态1\n(上涨趋势)', '状态2\n(震荡市场)'])
    ax2.grid(True, alpha=0.3)
    
    # 添加状态统计信息
    state_counts = np.bincount(states)
    state_info = []
    for i, count in enumerate(state_counts):
        if count > 0:
            percentage = count / len(states) * 100
            state_name = labels[i]
            state_info.append(f'状态{i}({state_name}): {count}天 ({percentage:.1f}%)')
    
    ax2.text(0.02, 0.98, '\n'.join(state_info), transform=ax2.transAxes, 
             verticalalignment='top', bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
    
    plt.tight_layout()
    plt.show()

# ----------------------------- #
# 5. In-Sample 演示 (可选)
# ----------------------------- #
regime_detection = RegimeDetection()

# Agglomerative
params_clu = dict(n_clusters=2, linkage='complete', affinity='manhattan')
clu = regime_detection.get_regimes_clustering(params_clu)
clu_states = clu.fit_predict(prices_array)
print("Agglomerative – State counts:", np.bincount(clu_states))

# GMM
params_gmm = dict(n_components=2, covariance_type='full', max_iter=100_000,
                  n_init=30, init_params='kmeans', random_state=100)
gmm_model = regime_detection.get_regimes_gmm(prices_array, params_gmm)
gmm_states = gmm_model.predict(prices_array)
print("GMM – State counts:", np.bincount(gmm_states))

# HMM参数设置 - 增加到3个状态以识别震荡市
# 使用diag协方差类型避免数值稳定性问题
params_hmm = dict(n_components=3, covariance_type='diag', random_state=100)
hmm_model  = regime_detection.get_regimes_hmm(prices_array, params_hmm)
hmm_states = hmm_model.predict(prices_array)
print("HMM – State counts:", np.bincount(hmm_states))

# ----------------------------- #
# 6. 滚动训练 / 前向验证
# ----------------------------- #
def feed_forward_training(model_fn, params, X, split_idx, retrain_step):
    """
    Rolling retrain & predict for out-of-sample regime sequence.
    """
    init_train = X[:split_idx]
    test_len   = len(X) - split_idx
    model = model_fn(init_train, params)

    preds = []
    for i in range(test_len):
        split_idx += 1
        preds.append(model.predict(X[:split_idx])[-1])
        if i % retrain_step == 0:
            model = model_fn(X[:split_idx], params)
    return np.array(preds)

SPLIT_DATE = "2010-01-01"
split_ix   = np.where(prices.index >= SPLIT_DATE)[0][0]

print(f"\n=== 前向滚动训练设置 ===")
print(f"初始训练期: {prices.index[0].date()} 至 {prices.index[split_ix-1].date()}")
print(f"滚动训练期: {prices.index[split_ix].date()} 至 {prices.index[-1].date()}")
print(f"初始训练样本数: {split_ix}")
print(f"滚动预测样本数: {len(prices) - split_ix}")

# GMM
print("\n正在进行GMM前向滚动训练...")
states_pred_gmm = feed_forward_training(
    regime_detection.get_regimes_gmm, params_gmm, prices_array, split_ix, 20)

# HMM
print("正在进行HMM前向滚动训练...")
states_pred_hmm = feed_forward_training(
    regime_detection.get_regimes_hmm, params_hmm, prices_array, split_ix, 20)

# ----------------------------- #
# 7. 策略回测 (HMM)
# ----------------------------- #
oos_prices = prices.iloc[split_ix:].copy()
oos_prices['State']   = states_pred_hmm
oos_prices['LogRet']  = np.log(oos_prices[TICKER] / oos_prices[TICKER].shift(1))
oos_prices['State']   = oos_prices['State'].shift(1)   # avoid look-ahead
oos_prices.dropna(inplace=True)

# 策略仓位：上涨趋势做多(1)，其他状态空仓(0)
# 这避免了在不确定市场中的无效交易和做空风险
oos_prices['Position'] = np.where(oos_prices['State'] == 1, 1, 0)
oos_prices['DailyPnL'] = oos_prices['Position'] * oos_prices['LogRet']
oos_prices['CumStrat'] = oos_prices['DailyPnL'].cumsum()
oos_prices['CumBnH']   = oos_prices['LogRet'].cumsum()

# ----------------------------- #
# 8. 详细性能分析
# ----------------------------- #
def calculate_performance_metrics(returns):
    """
    计算策略性能指标
    """
    total_return = returns.iloc[-1]
    annual_return = total_return / (len(returns) / 252)  # 假设252个交易日/年
    volatility = returns.diff().std() * np.sqrt(252)
    sharpe_ratio = annual_return / volatility if volatility > 0 else 0
    
    # 最大回撤
    cumulative = returns
    running_max = cumulative.expanding().max()
    drawdown = cumulative - running_max
    max_drawdown = drawdown.min()
    
    return {
        'Total Return': total_return,
        'Annualized Return': annual_return,
        'Volatility': volatility,
        'Sharpe Ratio': sharpe_ratio,
        'Max Drawdown': max_drawdown
    }

# 计算性能指标
bnh_metrics = calculate_performance_metrics(oos_prices['CumBnH'])
hmm_metrics = calculate_performance_metrics(oos_prices['CumStrat'])

# ----------------------------- #
# 9. 状态可视化
# ----------------------------- #
print("\n=== 趋势状态分析 ===")
print(f"HMM趋势状态分布:")
state_counts = np.bincount(states_pred_hmm)
state_names = ["下跌趋势", "上涨趋势", "震荡市场"]
for i, count in enumerate(state_counts):
    if i < len(state_names):
        state_name = state_names[i]
        print(f"  状态 {i} ({state_name}): {count} 天 ({count/len(states_pred_hmm)*100:.1f}%)")

# 使用matplotlib生成传统图表
print("\n=== 生成HMM状态可视化图表 ===")

import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from matplotlib.patches import Rectangle

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

# 创建状态时间序列图
fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(15, 10))

# 上图：价格走势和状态背景
ax1.plot(oos_prices.index, oos_prices[TICKER], 'k-', linewidth=1, alpha=0.8)

# 添加状态背景色
state_colors = {0: 'red', 1: 'green', 2: 'yellow'}
state_names = {0: '下跌趋势', 1: '上涨趋势', 2: '震荡市场'}

current_state = states_pred_hmm[0]
start_idx = 0

for i in range(1, len(states_pred_hmm)):
    if states_pred_hmm[i] != current_state:
        # 绘制当前状态区间
        ax1.axvspan(oos_prices.index[start_idx], oos_prices.index[i-1], 
                   alpha=0.3, color=state_colors[current_state])
        current_state = states_pred_hmm[i]
        start_idx = i

# 绘制最后一个状态区间
ax1.axvspan(oos_prices.index[start_idx], oos_prices.index[-1], 
           alpha=0.3, color=state_colors[current_state])

ax1.set_title('HMM状态检测：价格走势与市场状态', fontsize=14, fontweight='bold')
ax1.set_ylabel('价格', fontsize=12)
ax1.grid(True, alpha=0.3)

# 添加图例
legend_elements = [Rectangle((0,0),1,1, facecolor=state_colors[i], alpha=0.3, label=state_names[i]) 
                  for i in range(3)]
ax1.legend(handles=legend_elements, loc='upper left')

# 下图：状态序列
# 确保数据长度匹配
states_aligned = states_pred_hmm[:len(oos_prices)]
ax2.plot(oos_prices.index, states_aligned, 'o-', markersize=2, linewidth=0.5)
ax2.set_title('HMM状态时间序列', fontsize=14, fontweight='bold')
ax2.set_ylabel('状态', fontsize=12)
ax2.set_xlabel('日期', fontsize=12)
ax2.set_yticks([0, 1, 2])
ax2.set_yticklabels(['下跌', '上涨', '震荡'])
ax2.grid(True, alpha=0.3)

# 格式化x轴日期
for ax in [ax1, ax2]:
    ax.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m'))
    ax.xaxis.set_major_locator(mdates.YearLocator())
    plt.setp(ax.xaxis.get_majorticklabels(), rotation=45)

plt.tight_layout()
plt.savefig('hmm2_states_analysis.png', dpi=300, bbox_inches='tight')
plt.close()

# 显示状态统计信息
print("\n最近30个交易日的状态变化:")
recent_dates = oos_prices.index[-30:]
recent_states = states_aligned[-30:]

for i, (date, state) in enumerate(zip(recent_dates, recent_states)):
    state_name = state_names[state]
    if i % 5 == 0:  # 每5天换行
        print()
    print(f"{date.strftime('%m-%d')}:{state_name[:2]}", end="  ")
print("\n")

# 显示状态转换统计
print("\n状态转换分析:")
transitions = 0
for i in range(1, len(states_aligned)):
    if states_aligned[i] != states_aligned[i-1]:
        transitions += 1
print(f"总状态转换次数: {transitions}")
print(f"平均持续天数: {len(states_aligned)/transitions:.1f}天")
print("📊 状态分析图表已保存到: hmm2_states_analysis.png")

# ----------------------------- #
# 10. 策略性能对比可视化
# ----------------------------- #
fig = make_subplots(
    rows=2, cols=1,
    subplot_titles=('累积收益对比', '趋势状态分布'),
    vertical_spacing=0.1
)

# 累积收益图
fig.add_trace(
    go.Scatter(x=oos_prices.index, y=oos_prices['CumBnH'],
               mode='lines', name='买入持有策略', line=dict(color='navy')),
    row=1, col=1
)
fig.add_trace(
    go.Scatter(x=oos_prices.index, y=oos_prices['CumStrat'],
               mode='lines', name='HMM趋势策略', line=dict(color='olive')),
    row=1, col=1
)

# 状态分布图
# 状态0=下跌趋势（红色），状态1=上涨趋势（绿色），状态2=震荡市场（橙色）
colors_state = {0: 'red', 1: 'green', 2: 'orange'}
labels_state = {0: '下跌趋势', 1: '上涨趋势', 2: '震荡市场'}

for st in np.unique(states_pred_hmm):
    mask = states_pred_hmm == st
    # 确保mask长度与oos_prices匹配
    mask_aligned = mask[:len(oos_prices)]
    fig.add_trace(
        go.Scatter(x=oos_prices.index[mask_aligned], y=oos_prices[TICKER].iloc[mask_aligned],
                   mode='markers', name=f'状态 {st} ({labels_state[st]})',
                   marker=dict(size=2, color=colors_state[st])),
        row=2, col=1
    )

fig.update_layout(height=800, width=1000,
                  title="HMM趋势状态检测与策略回测结果",
                  margin=dict(l=20, r=20, t=60, b=20))
# 使用matplotlib生成策略收益对比图
print("\n=== 生成策略收益对比图表 ===")

# 创建收益对比图
fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(15, 10))

# 上图：累计收益对比
ax1.plot(oos_prices.index, oos_prices['CumBnH'] * 100, 'b-', linewidth=2, label='买入持有策略')
ax1.plot(oos_prices.index, oos_prices['CumStrat'] * 100, 'r-', linewidth=2, label='HMM趋势策略')
ax1.set_title('策略收益对比：累计收益率', fontsize=14, fontweight='bold')
ax1.set_ylabel('累计收益率 (%)', fontsize=12)
ax1.legend()
ax1.grid(True, alpha=0.3)

# 下图：回撤对比
bnh_drawdown = (oos_prices['CumBnH'] / oos_prices['CumBnH'].cummax() - 1) * 100
strat_drawdown = (oos_prices['CumStrat'] / oos_prices['CumStrat'].cummax() - 1) * 100

ax2.fill_between(oos_prices.index, bnh_drawdown, 0, alpha=0.3, color='blue', label='买入持有回撤')
ax2.fill_between(oos_prices.index, strat_drawdown, 0, alpha=0.3, color='red', label='HMM策略回撤')
ax2.set_title('策略回撤对比', fontsize=14, fontweight='bold')
ax2.set_ylabel('回撤 (%)', fontsize=12)
ax2.set_xlabel('日期', fontsize=12)
ax2.legend()
ax2.grid(True, alpha=0.3)

# 格式化x轴日期
for ax in [ax1, ax2]:
    ax.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m'))
    ax.xaxis.set_major_locator(mdates.YearLocator())
    plt.setp(ax.xaxis.get_majorticklabels(), rotation=45)

plt.tight_layout()
plt.savefig('hmm2_strategy_comparison.png', dpi=300, bbox_inches='tight')
plt.close()

# 显示关键时间点的收益对比
print("\n=== 策略收益曲线关键节点 ===")
key_dates = [oos_prices.index[0], 
             oos_prices.index[len(oos_prices)//4],
             oos_prices.index[len(oos_prices)//2], 
             oos_prices.index[3*len(oos_prices)//4],
             oos_prices.index[-1]]

print("日期\t\t买入持有\tHMM策略")
print("-" * 40)

for date in key_dates:
    idx = oos_prices.index.get_loc(date)
    bh_return = oos_prices['CumBnH'].iloc[idx] * 100
    hmm_return = oos_prices['CumStrat'].iloc[idx] * 100
    print(f"{date.strftime('%Y-%m-%d')}\t{bh_return:6.1f}%\t{hmm_return:6.1f}%")

print("\n📊 策略对比图表已保存到: hmm2_strategy_comparison.png")

# ----------------------------- #
# 11. 详细结果报告
# ----------------------------- #
print("\n" + "="*60)
print(f"分析期间: {oos_prices.index[0].date()} 至 {oos_prices.index[-1].date()}")
print(f"总交易天数: {len(oos_prices)} 天")

print("\n--- 策略性能对比 ---")
print(f"{'指标':<20} {'买入持有':<15} {'HMM趋势策略':<15} {'改善':<10}")
print("-" * 65)

for metric in ['Total Return', 'Annualized Return', 'Volatility', 'Sharpe Ratio', 'Max Drawdown']:
    bnh_val = bnh_metrics[metric]
    hmm_val = hmm_metrics[metric]
    
    if metric in ['Total Return', 'Annualized Return']:
        improvement = f"+{(hmm_val - bnh_val)*100:.1f}%"
        bnh_str = f"{bnh_val*100:.1f}%"
        hmm_str = f"{hmm_val*100:.1f}%"
    elif metric == 'Max Drawdown':
        improvement = f"{(hmm_val - bnh_val)*100:.1f}%"  # 负数表示改善
        bnh_str = f"{bnh_val*100:.1f}%"
        hmm_str = f"{hmm_val*100:.1f}%"
    else:
        improvement = f"{hmm_val - bnh_val:.2f}"
        bnh_str = f"{bnh_val:.2f}"
        hmm_str = f"{hmm_val:.2f}"
    
    print(f"{metric:<20} {bnh_str:<15} {hmm_str:<15} {improvement:<10}")

print("\n--- 交易统计 ---")
position_changes = oos_prices['Position'].diff().fillna(0)
trade_count = (position_changes != 0).sum()
long_days = (oos_prices['Position'] == 1).sum()
short_days = (oos_prices['Position'] == -1).sum()

print(f"总交易次数: {trade_count}")
print(f"做多天数: {long_days} ({long_days/len(oos_prices)*100:.1f}%)")
print(f"空仓天数: {short_days} ({short_days/len(oos_prices)*100:.1f}%)")

# 状态分布统计
state_counts = np.bincount(oos_prices['State'])
state_names = ['下跌趋势', '上涨趋势', '震荡市场']
print("\n市场状态分布:")
for i, count in enumerate(state_counts):
    if count > 0:
        print(f"{state_names[i]}: {count} ({count/len(oos_prices)*100:.1f}%)")

print("\n--- 关键发现 ---")
if hmm_metrics['Total Return'] > bnh_metrics['Total Return']:
    outperformance = (hmm_metrics['Total Return'] - bnh_metrics['Total Return']) * 100
    print(f"✓ HMM趋势策略跑赢买入持有策略 {outperformance:.1f} 个百分点")
else:
    underperformance = (bnh_metrics['Total Return'] - hmm_metrics['Total Return']) * 100
    print(f"✗ HMM趋势策略跑输买入持有策略 {underperformance:.1f} 个百分点")

if hmm_metrics['Sharpe Ratio'] > bnh_metrics['Sharpe Ratio']:
    print(f"✓ HMM趋势策略具有更好的风险调整收益 (夏普比率: {hmm_metrics['Sharpe Ratio']:.2f} vs {bnh_metrics['Sharpe Ratio']:.2f})")

if hmm_metrics['Max Drawdown'] > bnh_metrics['Max Drawdown']:  # 更小的负数表示更好
    print(f"✓ HMM趋势策略有效控制了最大回撤 ({hmm_metrics['Max Drawdown']*100:.1f}% vs {bnh_metrics['Max Drawdown']*100:.1f}%)")

# ----------------------------- #
# 12. 总结与说明
# ----------------------------- #
print("\n=== 分析完成 ===")
print("\n=== 使用说明 ===")
print("1. 本代码实现了基于趋势特征的HMM市场状态检测")
print("2. 采用前向滚动训练避免前视偏差")
print("3. 使用2010年前数据作为初始训练期")
print("4. 比较了HMM趋势策略与买入持有策略的表现")
print("\n=== 注意事项 ===")
print("- 本策略仅用于学术研究，不构成投资建议")
print("- 实际交易需考虑交易成本、滑点等因素")
print("- 历史表现不代表未来收益")
print("- 前向滚动训练更贴近真实交易环境")

print("\n" + "="*60)