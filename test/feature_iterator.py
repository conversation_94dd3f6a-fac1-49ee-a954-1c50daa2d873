#!/usr/bin/env python3
"""
特征迭代测试器 - 从最有效版本出发，系统性测试每个特征的价值
"""

import pandas as pd
import numpy as np
from hmmlearn.hmm import GaussianHMM
from sklearn.preprocessing import StandardScaler
import warnings
import os
from datetime import datetime

warnings.filterwarnings("ignore")


class FeatureIterativeHMM:
    """
    特征迭代HMM策略测试器
    基于原版最优策略，系统性测试特征添加的效果
    """
    
    def __init__(self):
        # 基础配置
        self.n_states = 4
        self.retraining_period = 63
        self.initial_train_size = 252
        self.transaction_cost = 0.001
        
        # 基础仓位配置（来自原版最优策略）
        self.position_map = {0: 1.0, 1: 0.75, 2: 0.25, 3: 0.0}
        
        # 基础特征集（原版最优的3个特征）
        self.base_features = [
            'feat_mom',    # 价格距离200日均线
            'feat_vol',    # ATR标准化波动率
            'feat_ret'     # 对数收益率
        ]
        
        # 候选特征池
        self.candidate_features = [
            'SMA_5', 'SMA_10', 'SMA_20', 'SMA_50', 'SMA_100',
            'EMA_12', 'EMA_26', 'EMA_50',
            'RSI_14',
            'MACD', 'MACD_Signal', 'MACD_Histogram',
            'BB_Width', 'BB_Position',
            'Stochastic_K', 'Stochastic_D',
            'Williams_R',
            'ROC_10', 'ROC_20',
            'Momentum_10',
            'Price_to_SMA20', 'Price_to_SMA50',
            'Volume_Ratio',
            'Volatility_10', 'Volatility_20',
            'Support_Distance', 'Resistance_Distance',
            'Trend_Strength',
            'Bull_Signal', 'Bear_Signal'
        ]
        
        # 存储最佳特征组合
        self.best_features = self.base_features.copy()
        self.best_performance = None
        
        # 测试结果记录
        self.test_results = []

    def prepare_base_features(self, data):
        """准备基础特征"""
        df = data.copy()
        df["Date"] = pd.to_datetime(df["Date"])
        df.set_index("Date", inplace=True)
        
        # 基础特征计算（与原版完全一致）
        df["SMA_200"] = df["Close"].rolling(window=200).mean()
        df['feat_mom'] = df["Close"] / df["SMA_200"] - 1
        df['feat_vol'] = df["ATR_14"] / df["Close"]
        df['feat_ret'] = df["Log_Returns"]
        
        return df

    def create_feature_vector(self, df, feature_list):
        """创建特征向量"""
        # 基础特征始终包含
        feature_data = {}
        
        # 添加基础特征
        for feat in self.base_features:
            if feat in df.columns:
                feature_data[feat] = df[feat]
        
        # 添加候选特征
        for feature in feature_list:
            if feature not in self.base_features and feature in df.columns:
                # 对某些特征进行标准化处理
                if feature.startswith('SMA_') or feature.startswith('EMA_'):
                    # 转换为相对价格距离
                    feature_data[f'{feature}_dist'] = df["Close"] / df[feature] - 1
                elif feature in ['Volume_Ratio', 'Price_to_SMA20', 'Price_to_SMA50']:
                    # 这些已经是比率，直接使用
                    feature_data[feature] = df[feature]
                elif feature in ['RSI_14', 'Stochastic_K', 'Stochastic_D']:
                    # 振荡器类指标，标准化到[-1,1]
                    feature_data[feature] = (df[feature] - 50) / 50
                elif feature in ['Bull_Signal', 'Bear_Signal']:
                    # 二元信号，直接使用
                    feature_data[feature] = df[feature]
                else:
                    # 其他特征直接使用
                    feature_data[feature] = df[feature]
        
        features_df = pd.DataFrame(feature_data, index=df.index)
        return features_df.dropna()

    def train_hmm_model(self, features_df):
        """训练HMM模型（与原版一致）"""
        scaler = StandardScaler()
        X = scaler.fit_transform(features_df)
        
        model = GaussianHMM(
            n_components=self.n_states,
            covariance_type="full",
            n_iter=1000,
            random_state=42,
        )
        model.fit(X)
        
        return model, scaler

    def map_states(self, model):
        """状态映射（与原版一致）"""
        # 基于收益特征（第3个特征，索引2）的均值排序
        return_means = model.means_[:, 2]
        sorted_indices = np.argsort(return_means)[::-1]
        
        return {original: mapped for mapped, original in enumerate(sorted_indices)}

    def run_backtest(self, data, feature_list, verbose=False):
        """运行回测"""
        try:
            # 准备特征
            df = self.prepare_base_features(data)
            features = self.create_feature_vector(df, feature_list)
            
            if len(features) < self.initial_train_size + 100:
                return None
            
            # Walk-forward回测
            all_dates = features.index
            total_days = len(all_dates)
            
            predicted_states = pd.Series(np.nan, index=all_dates)
            state_confidences = pd.Series(np.nan, index=all_dates)
            
            start_idx = self.initial_train_size
            
            while start_idx < total_days:
                predict_end_idx = min(start_idx + self.retraining_period, total_days)
                
                # 训练数据
                train_features = features.iloc[:start_idx]
                predict_features = features.iloc[start_idx:predict_end_idx]
                
                if len(predict_features) == 0:
                    break
                
                try:
                    # 训练模型
                    model, scaler = self.train_hmm_model(train_features)
                    
                    # 状态映射
                    state_mapping = self.map_states(model)
                    
                    # 预测
                    X_predict = scaler.transform(predict_features)
                    states = model.predict(X_predict)
                    posteriors = model.predict_proba(X_predict)
                    
                    # 应用映射
                    mapped_states = [state_mapping[s] for s in states]
                    confidences = [posteriors[i, s] for i, s in enumerate(states)]
                    
                    predicted_states.iloc[start_idx:predict_end_idx] = mapped_states
                    state_confidences.iloc[start_idx:predict_end_idx] = confidences
                    
                except:
                    pass
                
                start_idx += self.retraining_period
            
            # 生成交易信号 - 只保留必要的列
            result_data = {
                'Returns': df['Returns'],
                'Log_Returns': df['Log_Returns'],
                'Close': df['Close'],
                'Predicted_State': predicted_states,
                'Confidence': state_confidences
            }
            
            df_result = pd.DataFrame(result_data, index=df.index)
            df_result = df_result.dropna(subset=["Predicted_State"])
            
            if len(df_result) == 0:
                return None
            
            # 仓位计算
            df_result["Position"] = df_result["Predicted_State"].map(self.position_map)
            df_result["Position"] *= df_result["Confidence"]  # 置信度调节
            df_result["Position"] = df_result["Position"].shift(1).fillna(0)
            
            # 策略收益
            df_result["Strategy_Return"] = df_result["Position"] * df_result["Returns"]
            position_changes = df_result["Position"].diff().abs()
            costs = position_changes * self.transaction_cost
            df_result["Net_Strategy_Return"] = df_result["Strategy_Return"] - costs
            
            return df_result
            
        except Exception as e:
            if verbose:
                print(f"   ❌ 回测失败: {e}")
            return None

    def calculate_performance(self, results_df, test_start_date):
        """计算性能指标"""
        if results_df is None or len(results_df) == 0:
            return None
        
        # 测试集数据
        test_results = results_df[results_df.index >= test_start_date]
        if len(test_results) < 50:  # 至少50个交易日
            return None
        
        returns = test_results["Net_Strategy_Return"]
        
        # 基础指标
        trading_days = 252
        mean_return = returns.mean()
        std_return = returns.std()
        
        if std_return == 0:
            return None
        
        # 核心指标
        sharpe_ratio = (mean_return / std_return) * np.sqrt(trading_days)
        annual_return = (1 + mean_return) ** trading_days - 1
        
        # 最大回撤
        cumulative = (1 + returns).cumprod()
        peak = cumulative.expanding().max()
        drawdown = (cumulative - peak) / peak
        max_drawdown = drawdown.min()
        
        # 胜率
        win_rate = (returns > 0).sum() / len(returns)
        
        return {
            'sharpe_ratio': sharpe_ratio,
            'annual_return': annual_return,
            'max_drawdown': max_drawdown,
            'win_rate': win_rate,
            'volatility': std_return * np.sqrt(trading_days),
            'total_days': len(test_results)
        }

    def test_feature_addition(self, new_feature, data, test_start_date, verbose=True):
        """测试添加新特征的效果"""
        if verbose:
            print(f"🧪 测试特征: {new_feature}")
        
        # 当前最佳特征 + 新特征
        test_features = [f for f in self.best_features if f not in self.base_features] + [new_feature]
        
        # 运行回测
        results = self.run_backtest(data, test_features, verbose=verbose)
        performance = self.calculate_performance(results, test_start_date)
        
        if performance is None:
            if verbose:
                print(f"   ❌ {new_feature}: 回测失败或数据不足")
            return None
        
        if verbose:
            print(f"   📊 {new_feature}: 夏普{performance['sharpe_ratio']:.4f}, "
                  f"收益{performance['annual_return']:.2%}, "
                  f"回撤{performance['max_drawdown']:.2%}")
        
        return performance

    def run_feature_iteration(self, data, test_start_date):
        """运行特征迭代测试"""
        print("🚀 开始特征迭代测试")
        print("="*60)
        
        # 先测试基础版本性能
        print("📊 基础版本性能:")
        base_results = self.run_backtest(data, [])
        base_performance = self.calculate_performance(base_results, test_start_date)
        
        if base_performance is None:
            print("❌ 基础版本测试失败")
            return
        
        print(f"   基础版本: 夏普{base_performance['sharpe_ratio']:.4f}, "
              f"收益{base_performance['annual_return']:.2%}, "
              f"回撤{base_performance['max_drawdown']:.2%}")
        
        self.best_performance = base_performance
        iteration = 0
        
        # 迭代测试每个候选特征
        remaining_features = self.candidate_features.copy()
        
        while remaining_features and iteration < 20:  # 增加到20轮迭代
            iteration += 1
            print(f"\n🔄 第{iteration}轮特征迭代:")
            print("-" * 40)
            
            best_new_feature = None
            best_new_performance = None
            
            # 测试每个候选特征
            for feature in remaining_features[:]:
                performance = self.test_feature_addition(feature, data, test_start_date, verbose=False)
                
                if performance is not None:
                    # 记录结果
                    self.test_results.append({
                        'iteration': iteration,
                        'feature': feature,
                        'sharpe_ratio': performance['sharpe_ratio'],
                        'annual_return': performance['annual_return'],
                        'max_drawdown': performance['max_drawdown'],
                        'win_rate': performance['win_rate']
                    })
                    
                    # 判断是否是改进（主要看夏普比率）
                    if performance['sharpe_ratio'] > self.best_performance['sharpe_ratio']:
                        if best_new_performance is None or performance['sharpe_ratio'] > best_new_performance['sharpe_ratio']:
                            best_new_feature = feature
                            best_new_performance = performance
            
            # 如果找到改进的特征
            if best_new_feature:
                print(f"✅ 发现改进特征: {best_new_feature}")
                print(f"   性能提升: 夏普 {self.best_performance['sharpe_ratio']:.4f} → {best_new_performance['sharpe_ratio']:.4f}")
                print(f"            收益 {self.best_performance['annual_return']:.2%} → {best_new_performance['annual_return']:.2%}")
                print(f"            回撤 {self.best_performance['max_drawdown']:.2%} → {best_new_performance['max_drawdown']:.2%}")
                
                # 更新最佳组合
                self.best_features.append(best_new_feature)
                self.best_performance = best_new_performance
                remaining_features.remove(best_new_feature)
            else:
                print("❌ 本轮未发现有效改进特征")
                # 不要break，继续尝试其他特征
                # 只有当连续3轮都没有改进或者测试完所有特征时才停止
                if iteration >= 3:
                    consecutive_no_improvement = 0
                    for i in range(3):
                        if len(self.test_results) > i:
                            recent_result = self.test_results[-(i+1)]
                            if recent_result['sharpe_ratio'] <= self.best_performance['sharpe_ratio']:
                                consecutive_no_improvement += 1
                    
                    if consecutive_no_improvement >= 3:
                        print("🔚 连续3轮无改进，停止迭代")
                        break
        
        print(f"\n🏆 特征迭代完成!")
        print("="*60)
        print(f"最终特征组合: {self.best_features}")
        print(f"最终性能: 夏普{self.best_performance['sharpe_ratio']:.4f}, "
              f"收益{self.best_performance['annual_return']:.2%}, "
              f"回撤{self.best_performance['max_drawdown']:.2%}")

    def save_results(self):
        """保存测试结果"""
        if self.test_results:
            results_df = pd.DataFrame(self.test_results)
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"feature_iteration_results_{timestamp}.csv"
            results_df.to_csv(filename, index=False)
            print(f"\n📊 测试结果已保存: {filename}")
            
            # 显示top 10 特征
            print("\n🔝 Top 10 特征排名:")
            top_features = results_df.nlargest(10, 'sharpe_ratio')[['feature', 'sharpe_ratio', 'annual_return', 'max_drawdown']]
            for idx, row in top_features.iterrows():
                print(f"   {row['feature']:<20}: 夏普{row['sharpe_ratio']:.4f}, 收益{row['annual_return']:.2%}, 回撤{row['max_drawdown']:.2%}")

    def generate_final_strategy(self, data, test_start_date):
        """生成最终优化策略"""
        if not self.best_features:
            print("❌ 没有找到有效特征组合")
            return
        
        print(f"\n🎯 生成最终策略 (特征数: {len(self.best_features)})")
        
        # 运行最终回测
        final_features = [f for f in self.best_features if f not in self.base_features]
        results = self.run_backtest(data, final_features, verbose=True)
        
        if results is None:
            print("❌ 最终策略生成失败")
            return
        
        # 生成测试集提交文件
        test_results = results[results.index >= test_start_date]
        
        if len(test_results) == 0:
            print("❌ 没有测试集数据")
            return
        
        # 创建提交文件
        submission_df = test_results.copy().reset_index()
        submission_df = submission_df[["Date", "Position", "Confidence", "Predicted_State"]]
        submission_df["Date"] = submission_df["Date"].dt.strftime("%Y-%m-%d")
        
        # 创建Signal列
        submission_df["Position_prev"] = submission_df["Position"].shift(1).fillna(0)
        submission_df["Signal"] = 0
        submission_df.loc[submission_df["Position"] > submission_df["Position_prev"], "Signal"] = 1
        submission_df.loc[submission_df["Position"] < submission_df["Position_prev"], "Signal"] = -1
        
        final_submission = submission_df[["Date", "Signal", "Confidence", "Predicted_State"]]
        final_submission["Predicted_State"] = final_submission["Predicted_State"].astype(int)
        
        # 保存文件
        os.makedirs("./submission", exist_ok=True)
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"./submission/submission_iterative_{timestamp}.csv"
        final_submission.to_csv(filename, index=False)
        
        print(f"✅ 最终提交文件已保存: {filename}")
        print(f"📊 提交文件形状: {final_submission.shape}")
        print("前5行预览:")
        print(final_submission.head())


def main():
    """主函数"""
    print("🧪 HMM特征迭代测试器")
    print("基于原版最优策略，系统性测试特征价值")
    print("="*60)
    
    try:
        # 加载数据
        train_df = pd.read_csv("./hmm_data/SPY/prepared/train.csv")
        test_df = pd.read_csv("./hmm_data/SPY/prepared/test.csv")
        combined_df = pd.concat([train_df, test_df], ignore_index=True)
        
        test_start_date = pd.to_datetime(test_df["Date"].min())
        
        # 初始化迭代器
        iterator = FeatureIterativeHMM()
        
        # 运行特征迭代
        iterator.run_feature_iteration(combined_df, test_start_date)
        
        # 保存结果
        iterator.save_results()
        
        # 生成最终策略
        iterator.generate_final_strategy(combined_df, test_start_date)
        
    except Exception as e:
        print(f"❌ 特征迭代失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
