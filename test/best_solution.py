import pandas as pd
import numpy as np
from hmmlearn.hmm import GaussianHMM
from sklearn.preprocessing import StandardScaler
import warnings
import os

warnings.filterwarnings("ignore", category=DeprecationWarning)
warnings.filterwarnings("ignore", category=FutureWarning)
warnings.filterwarnings("ignore")


class HMMTradingStrategy:
    """
    Implements a Hidden Markov Model (HMM) based trading strategy with walk-forward optimization.

    The strategy involves:
    1. Engineering features for momentum and volatility.
    2. Periodically retraining an HMM on historical data.
    3. Mapping HMM states to market regimes (Bull, Bear, Transition) consistently.
    4. Generating trading positions based on the predicted regime and model confidence.
    5. Backtesting the strategy with transaction costs.
    """

    def __init__(
        self,
        n_states=4,
        retraining_period=63,
        initial_train_size=252,
        transaction_cost=0.001,
    ):
        self.n_states = n_states
        self.retraining_period = retraining_period
        self.initial_train_size = initial_train_size
        self.transaction_cost = transaction_cost
        self.model = None
        self.scalers = {}
        self.state_mapping = {}

        # Define position sizing based on mapped states
        # Mapped State 0: Bull (highest return) -> 100%
        # Mapped State 1: Transition 1 -> 75%
        # Mapped State 2: Transition 2 -> 25%
        # Mapped State 3: Bear (lowest return) -> 0%
        self.position_map = {0: 1.0, 1: 0.75, 2: 0.25, 3: 0.0}

    def engineer_features(self, data):
        """
        Creates features for the HMM model.
        Focuses on momentum and volatility indicators.
        """
        df = data.copy()

        # Ensure 'Date' is a datetime object
        df["Date"] = pd.to_datetime(df["Date"])
        df.set_index("Date", inplace=True)

        # Calculate SMAs if not present (e.g., SMA_200)
        if "SMA_200" not in df.columns:
            df["SMA_200"] = df["Close"].rolling(window=200).mean()

        # Feature 1: Momentum - Price distance from long-term MA
        df["feat_mom"] = df["Close"] / df["SMA_200"] - 1

        # Feature 2: Volatility - ATR normalized by price
        df["feat_vol"] = df["ATR_14"] / df["Close"]

        # Feature 3: Returns - Log returns are already in the data
        # Using Log_Returns as a core feature
        df["feat_ret"] = df["Log_Returns"]

        # Select features and handle NaNs
        feature_cols = ["feat_mom", "feat_vol", "feat_ret"]
        df.dropna(subset=feature_cols, inplace=True)

        self.features = df[feature_cols]
        self.full_data = df

        return df

    def _map_states(self):
        """
        Maps HMM states to meaningful market regimes (Bull, Bear, etc.)
        based on the mean of log returns for each state.
        This ensures consistency across retraining periods.
        """
        # The 'feat_ret' is the 3rd feature (index 2)
        return_means = self.model.means_[:, 2]

        # Sort states by mean return: highest return = Bull, lowest = Bear
        sorted_indices = np.argsort(return_means)[::-1]

        # Create the mapping: {original_state: mapped_state}
        self.state_mapping = {
            original: mapped for mapped, original in enumerate(sorted_indices)
        }

    def train_and_predict_walk_forward(self):
        """
        Implements the walk-forward training and prediction loop.
        """
        all_dates = self.features.index
        total_days = len(all_dates)

        predicted_states = pd.Series(np.nan, index=all_dates)
        state_confidences = pd.Series(np.nan, index=all_dates)

        # Start prediction after the initial training period
        start_idx = self.initial_train_size

        while start_idx < total_days:
            train_end_idx = start_idx
            predict_start_idx = start_idx
            predict_end_idx = min(start_idx + self.retraining_period, total_days)

            # Prepare training and prediction data
            train_features = self.features.iloc[:train_end_idx]
            predict_features = self.features.iloc[predict_start_idx:predict_end_idx]

            if len(predict_features) == 0:
                break

            # Scale features
            scaler = StandardScaler()
            train_features_scaled = scaler.fit_transform(train_features)
            predict_features_scaled = scaler.transform(predict_features)

            # Train HMM
            self.model = GaussianHMM(
                n_components=self.n_states,
                covariance_type="full",
                n_iter=1000,
                random_state=42,
            )
            try:
                self.model.fit(train_features_scaled)
            except ValueError:
                # If model fails to converge, use previous model or skip
                print(
                    f"HMM failed to converge for period ending {all_dates[train_end_idx].date()}. Skipping prediction for next period."
                )
                start_idx += self.retraining_period
                continue

            # Map states for consistency
            self._map_states()

            # Predict states and probabilities for the next period
            states = self.model.predict(predict_features_scaled)
            posteriors = self.model.predict_proba(predict_features_scaled)

            # Apply mapping and store results
            mapped_states = [self.state_mapping[s] for s in states]
            confidences = [posteriors[i, s] for i, s in enumerate(states)]

            predicted_states.iloc[predict_start_idx:predict_end_idx] = mapped_states
            state_confidences.iloc[predict_start_idx:predict_end_idx] = confidences

            # Move to the next window
            start_idx += self.retraining_period

        self.full_data["Predicted_State"] = predicted_states
        self.full_data["Confidence"] = state_confidences
        self.full_data.dropna(subset=["Predicted_State"], inplace=True)

    def generate_positions(self):
        """
        Generates trading positions based on predicted states and confidence.
        """
        self.full_data["Position"] = self.full_data["Predicted_State"].map(
            self.position_map
        )

        # Modulate position by confidence
        self.full_data["Position"] *= self.full_data["Confidence"]

        # Position is for the *next* day, so shift it
        self.full_data["Position"] = self.full_data["Position"].shift(1).fillna(0)

    def run_backtest(self):
        """
        Calculates strategy returns and performance metrics.
        """
        # Calculate strategy returns before costs
        self.full_data["Strategy_Return"] = (
            self.full_data["Position"] * self.full_data["Returns"]
        )

        # Calculate transaction costs
        position_changes = self.full_data["Position"].diff().abs()
        costs = position_changes * self.transaction_cost
        self.full_data["Net_Strategy_Return"] = (
            self.full_data["Strategy_Return"] - costs
        )

        return self.full_data


def calculate_metrics(returns, risk_free_rate=0.0):
    """
    Calculates key performance metrics for a series of returns.
    """
    if len(returns) < 2:
        return {
            "Sharpe Ratio": 0,
            "Annual Return": 0,
            "Volatility": 0,
            "Maximum Drawdown": 0,
            "Sortino Ratio": 0,
            "Calmar Ratio": 0,
            "Win Rate": 0,
        }

    # Annualization factor
    trading_days = 252

    # Returns statistics
    mean_daily_return = returns.mean()
    std_daily_return = returns.std()

    # Sharpe Ratio
    sharpe_ratio = (
        (mean_daily_return / std_daily_return) * np.sqrt(trading_days)
        if std_daily_return != 0
        else 0
    )

    # Annual Return
    annual_return = (1 + mean_daily_return) ** trading_days - 1

    # Volatility
    volatility = std_daily_return * np.sqrt(trading_days)

    # Maximum Drawdown
    cumulative_returns = (1 + returns).cumprod()
    peak = cumulative_returns.expanding(min_periods=1).max()
    drawdown = (cumulative_returns - peak) / peak
    max_drawdown = drawdown.min()

    # Sortino Ratio
    downside_returns = returns[returns < 0]
    downside_std = downside_returns.std()
    sortino_ratio = (
        (mean_daily_return / downside_std) * np.sqrt(trading_days)
        if downside_std != 0
        else 0
    )

    # Calmar Ratio
    calmar_ratio = annual_return / abs(max_drawdown) if max_drawdown != 0 else 0

    # Win Rate
    win_rate = (returns > 0).sum() / len(returns) if len(returns) > 0 else 0

    metrics = {
        "Sharpe Ratio": sharpe_ratio,
        "Annual Return": annual_return,
        "Volatility": volatility,
        "Maximum Drawdown": max_drawdown,
        "Sortino Ratio": sortino_ratio,
        "Calmar Ratio": calmar_ratio,
        "Win Rate": win_rate,
    }
    return metrics


def main():
    # Load data
    train_df = pd.read_csv("./hmm_data/SPY/prepared/train.csv")
    test_df = pd.read_csv("./hmm_data/SPY/prepared/test.csv")
    sample_submission = pd.read_csv("./hmm_data/SPY/prepared/sample_submission.csv")

    # Combine data for walk-forward analysis
    combined_df = pd.concat([train_df, test_df], ignore_index=True)

    # Initialize and run the strategy
    strategy = HMMTradingStrategy(
        n_states=4, retraining_period=63, initial_train_size=252
    )

    # 1. Feature Engineering
    processed_data = strategy.engineer_features(combined_df)

    # 2. Walk-forward training and prediction
    strategy.train_and_predict_walk_forward()

    # 3. Generate positions
    strategy.generate_positions()

    # 4. Run backtest
    results = strategy.run_backtest()

    # Split results back into train and test periods for evaluation
    test_start_date = pd.to_datetime(test_df["Date"].min())
    test_results = results[results.index >= test_start_date]

    # Calculate and print performance metrics on the test set
    test_metrics = calculate_metrics(test_results["Net_Strategy_Return"])

    print("--- HMM Trading Strategy Performance (Out-of-Sample Test Set) ---")
    print(f"Sharpe Ratio: {test_metrics['Sharpe Ratio']:.4f}")
    print(f"Annual Return: {test_metrics['Annual Return']:.2%}")
    print(f"Volatility: {test_metrics['Volatility']:.2%}")
    print(f"Maximum Drawdown: {test_metrics['Maximum Drawdown']:.2%}")
    print(f"Sortino Ratio: {test_metrics['Sortino Ratio']:.4f}")
    print(f"Calmar Ratio: {test_metrics['Calmar Ratio']:.4f}")
    print(f"Win Rate: {test_metrics['Win Rate']:.2%}")
    print("-" * 60)

    # Generate submission file
    submission_df = test_results.copy().reset_index()
    submission_df = submission_df[["Date", "Position", "Confidence", "Predicted_State"]]
    submission_df["Date"] = submission_df["Date"].dt.strftime("%Y-%m-%d")

    # Create the 'Signal' column based on position changes
    submission_df["Position_prev"] = submission_df["Position"].shift(1).fillna(0)
    submission_df["Signal"] = 0
    submission_df.loc[
        submission_df["Position"] > submission_df["Position_prev"], "Signal"
    ] = 1
    submission_df.loc[
        submission_df["Position"] < submission_df["Position_prev"], "Signal"
    ] = -1

    # Align with sample submission format
    final_submission = submission_df[
        ["Date", "Signal", "Confidence", "Predicted_State"]
    ]
    final_submission.columns = ["Date", "Signal", "Confidence", "Predicted_State"]
    final_submission["Predicted_State"] = final_submission["Predicted_State"].astype(
        int
    )

    # Ensure submission dir exists
    os.makedirs("./submission", exist_ok=True)
    final_submission.to_csv("./submission/submission.csv", index=False)

    print("Submission file created successfully: ./submission/submission.csv")
    print(f"Submission file shape: {final_submission.shape}")
    print("Submission file head:")
    print(final_submission.head())


if __name__ == "__main__":
    main()
