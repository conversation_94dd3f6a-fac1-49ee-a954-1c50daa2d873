#!/usr/bin/env python3
"""
SPY HMM V5策略 - 适配版本
========================

将strategy/spy/spy_hmm_v5_optimal.py改造为与best_solution_final.py相同的数据和评估逻辑，
以便进行公平对比。

主要改造：
1. 使用相同的数据源（./hmm_data/SPY/prepared/）
2. 使用相同的特征工程逻辑
3. 使用相同的评估指标计算
4. 保持V5的核心算法逻辑不变

作者：Leon
版本：V5 Adapted for Fair Comparison
日期：2025年9月2日
"""

import pandas as pd
import numpy as np
from hmmlearn.hmm import GaussianHMM
from sklearn.preprocessing import StandardScaler
import warnings
import os

warnings.filterwarnings("ignore", category=DeprecationWarning)
warnings.filterwarnings("ignore", category=FutureWarning)
warnings.filterwarnings("ignore")


class AdaptedV5HMMStrategy:
    """
    V5 HMM策略适配版本
    
    保持V5的核心算法逻辑：
    1. 季度重训机制
    2. 历史状态表现分析
    3. 动态状态映射
    4. 严格无前视偏差
    
    但使用与best_solution_final.py相同的：
    1. 数据源和格式
    2. 特征工程方法
    3. 评估指标计算
    """

    def __init__(
        self,
        n_states=4,
        retraining_period=63,  # V5特色：季度重训
        initial_train_size=252,
        transaction_cost=0.001,
    ):
        self.n_states = n_states
        self.retraining_period = retraining_period
        self.initial_train_size = initial_train_size
        self.transaction_cost = transaction_cost
        self.model = None
        self.scalers = {}
        self.state_mapping = {}
        self.position_map = {}

    def engineer_v5_features(self, data):
        """
        V5特征工程 - 适配版本
        使用与best_solution_final.py相同的数据格式，但保持V5的特征选择逻辑
        """
        df = data.copy()

        # 确保Date是datetime类型
        df["Date"] = pd.to_datetime(df["Date"])
        df.set_index("Date", inplace=True)

        print("🔧 V5适配版特征工程...")

        # V5核心特征集（11个特征）
        # 1. 基础收益和波动率特征
        df["feat_log_return"] = df["Log_Returns"]
        df["feat_volatility"] = df["Volatility_20"]  # 使用数据中的20日波动率

        # 2. VIX替代 - 使用ATR标准化
        df["feat_vix_proxy"] = df["ATR_14"] / df["Close"]

        # 3. 利率变化替代 - 使用ROC
        df["feat_rate_change"] = df["ROC_10"]

        # 4. 移动平均距离特征（V5特色）
        df["feat_dist_ma20"] = df["Close"] / df["SMA_20"] - 1
        df["feat_dist_ma50"] = df["Close"] / df["SMA_50"] - 1  
        df["feat_dist_ma200"] = df["Close"] / df["SMA_200"] - 1

        # 5. 动量特征
        df["feat_momentum_5d"] = df["ROC_10"] / 2  # 近似5日动量
        df["feat_momentum_20d"] = df["ROC_20"]

        # 6. 市场广度替代 - 使用相对强弱
        df["feat_market_breadth"] = df["RSI_14"] / 50 - 1

        # 7. 信用风险替代 - 使用波动率状态
        df["feat_credit_risk"] = df["Volatility_20"] / df["Volatility_10"] - 1

        # V5特征列表
        feature_columns = [
            "feat_log_return",      # 核心收益特征
            "feat_volatility",      # 核心波动率特征
            "feat_vix_proxy",       # VIX替代
            "feat_rate_change",     # 利率变化替代
            "feat_dist_ma20",       # 短期趋势
            "feat_dist_ma50",       # 中期趋势
            "feat_dist_ma200",      # 长期趋势
            "feat_momentum_5d",     # 短期动量
            "feat_momentum_20d",    # 长期动量
            "feat_market_breadth",  # 市场广度替代
            "feat_credit_risk"      # 信用风险替代
        ]

        # 清理数据
        df_clean = df.dropna(subset=feature_columns)
        
        self.features = df_clean[feature_columns]
        self.full_data = df_clean

        print(f"   ✅ V5适配特征工程完成: {len(feature_columns)}个特征, {len(df_clean)}条记录")
        print(f"   📊 V5特征列表: {feature_columns}")
        
        return self.full_data

    def calculate_historical_state_performance_v5(self, historical_data, model, scaler, feature_cols):
        """
        V5核心算法：计算历史状态表现
        这是V5的核心创新 - 基于历史表现动态映射状态
        """
        try:
            features = historical_data[feature_cols].dropna()
            if len(features) < 252:
                return {}
            
            X = scaler.transform(features)
            states = model.predict(X)
            
            # V5特色：综合评分系统
            state_performance = {}
            for state_id in range(model.n_components):
                state_mask = (states == state_id)
                if state_mask.sum() < 20:
                    continue
                
                state_returns = features.loc[features.index[state_mask], 'feat_log_return']
                state_vols = features.loc[features.index[state_mask], 'feat_volatility']
                state_vix = features.loc[features.index[state_mask], 'feat_vix_proxy']
                
                # V5评分算法
                avg_return = float(state_returns.mean())
                avg_vol = float(state_vols.mean()) 
                avg_vix = float(state_vix.mean())
                
                # V5风险调整收益评分
                return_score = avg_return * 1000
                risk_penalty = -avg_vol * 5
                vix_penalty = -max(0, avg_vix - 0.02) * 100  # 调整VIX替代的阈值
                
                total_score = return_score + risk_penalty + vix_penalty
                
                state_performance[state_id] = {
                    'score': total_score,
                    'return': avg_return,
                    'vol': avg_vol,
                    'vix': avg_vix,
                    'frequency': float(state_mask.sum() / len(states))
                }
            
            return state_performance
        
        except Exception as e:
            print(f"V5历史表现计算失败: {e}")
            return {}

    def map_states_to_regimes_v5(self, state_performance):
        """
        V5核心算法：动态状态映射
        基于历史表现动态分配仓位，这是V5的核心创新
        """
        if not state_performance:
            return {}, {}
        
        # V5算法：按得分排序
        sorted_states = sorted(state_performance.items(), key=lambda x: x[1]['score'])
        
        state_map = {}
        position_map = {}
        
        n_states = len(sorted_states)
        
        # V5动态仓位分配逻辑
        if n_states == 2:
            state_map[sorted_states[0][0]] = "Bear Market"
            state_map[sorted_states[1][0]] = "Bull Market"
            position_map = {"Bear Market": 0.0, "Bull Market": 1.0}
        elif n_states == 3:
            state_map[sorted_states[0][0]] = "Bear Market"
            state_map[sorted_states[1][0]] = "Transition"
            state_map[sorted_states[2][0]] = "Bull Market"
            position_map = {"Bear Market": 0.0, "Transition": 0.8, "Bull Market": 1.0}  # V5特色：0.8仓位
        else:
            # V5多状态处理
            for idx, (state_id, perf) in enumerate(sorted_states):
                if idx == 0:
                    state_map[state_id] = "Bear Market"
                    position_map["Bear Market"] = 0.0
                elif idx == n_states - 1:
                    state_map[state_id] = "Bull Market"
                    position_map["Bull Market"] = 1.0
                else:
                    label = f"Transition_{idx}"
                    state_map[state_id] = label
                    # V5渐进式仓位分配
                    position_map[label] = 0.5 + 0.4 * (idx / (n_states - 1))
        
        # 打印V5映射结果
        print("   📊 V5动态状态映射:")
        for state_id, regime in state_map.items():
            perf = state_performance[state_id]
            position = position_map[regime]
            print(f"     状态{state_id} -> {regime} (仓位{position:.1%})")
            print(f"       收益:{perf['return']:.6f}, 波动:{perf['vol']:.4f}, "
                  f"风险:{perf['vix']:.4f}, 得分:{perf['score']:.2f}")
        
        return state_map, position_map

    def train_hmm_model_v5(self, features_df, n_states_range=[2, 3, 4]):
        """V5 HMM模型训练"""
        scaler = StandardScaler()
        X = scaler.fit_transform(features_df)

        best_model = None
        best_bic = np.inf

        for n_states in n_states_range:
            try:
                model = GaussianHMM(
                    n_components=n_states,
                    covariance_type="full",
                    n_iter=1000,
                    random_state=42
                )
                model.fit(X)
                bic = model.bic(X)

                if bic < best_bic:
                    best_bic = bic
                    best_model = model
            except:
                continue

        return best_model, scaler

    def train_and_predict_walk_forward_v5(self):
        """
        V5核心算法：Walk-forward训练预测
        保持V5的季度重训机制和严格无前视偏差
        """
        all_dates = self.features.index
        total_days = len(all_dates)

        predicted_states = pd.Series(np.nan, index=all_dates)
        state_confidences = pd.Series(np.nan, index=all_dates)
        regimes = pd.Series("Unknown", index=all_dates)
        positions = pd.Series(0.0, index=all_dates)

        start_idx = self.initial_train_size
        current_model = None
        current_scaler = None
        current_state_map = {}
        current_position_map = {}
        last_retrain_date = None

        print(f"🚀 开始V5适配版walk-forward回测...")
        print(f"   初始训练大小: {self.initial_train_size}天")
        print(f"   重训周期: {self.retraining_period}天")

        retrain_count = 0
        while start_idx < total_days:
            current_date = all_dates[start_idx]

            # V5核心：季度重训检查
            should_retrain = (
                current_model is None or
                last_retrain_date is None or
                (current_date - last_retrain_date).days >= self.retraining_period
            )

            if should_retrain:
                retrain_count += 1
                if retrain_count <= 5 or retrain_count % 5 == 0:
                    print(f"   📚 V5重训 #{retrain_count} 于 {current_date.date()}")

                try:
                    # 获取历史训练数据（严格无前视偏差）
                    train_end_idx = start_idx
                    train_features = self.features.iloc[:train_end_idx]

                    if len(train_features) < self.initial_train_size:
                        start_idx += 1
                        continue

                    # V5训练HMM模型
                    model, scaler = self.train_hmm_model_v5(train_features)
                    if model is None:
                        start_idx += 1
                        continue

                    # V5核心：计算历史状态表现
                    historical_data = self.full_data.iloc[:train_end_idx]
                    state_perf = self.calculate_historical_state_performance_v5(
                        historical_data, model, scaler, self.features.columns.tolist()
                    )

                    if not state_perf:
                        start_idx += 1
                        continue

                    # V5核心：动态状态映射
                    state_map, position_map = self.map_states_to_regimes_v5(state_perf)

                    if not state_map:
                        start_idx += 1
                        continue

                    # 更新当前模型
                    current_model = model
                    current_scaler = scaler
                    current_state_map = state_map
                    current_position_map = position_map
                    last_retrain_date = current_date

                except Exception as e:
                    if retrain_count <= 3:
                        print(f"     V5模型训练失败: {e}")
                    start_idx += 1
                    continue

            # 使用当前模型进行预测
            if current_model is not None and current_scaler is not None:
                try:
                    # 获取当日特征
                    current_features = self.features.iloc[[start_idx]]
                    if current_features.isnull().any().any():
                        start_idx += 1
                        continue

                    # 预测状态
                    X_current = current_scaler.transform(current_features)
                    predicted_state = current_model.predict(X_current)[0]
                    state_proba = current_model.predict_proba(X_current)[0]
                    confidence = state_proba[predicted_state]

                    # V5映射到交易信号
                    regime = current_state_map.get(predicted_state, "Unknown")
                    position = current_position_map.get(regime, 0.5)

                    # 存储结果
                    predicted_states.iloc[start_idx] = predicted_state
                    state_confidences.iloc[start_idx] = confidence
                    regimes.iloc[start_idx] = regime
                    positions.iloc[start_idx] = position

                except Exception as e:
                    pass

            start_idx += 1

        # 存储到full_data
        self.full_data["Predicted_State"] = predicted_states
        self.full_data["Confidence"] = state_confidences
        self.full_data["Regime"] = regimes
        self.full_data["Position_Raw"] = positions

        # 清理数据
        self.full_data.dropna(subset=["Predicted_State"], inplace=True)

        print(f"   ✅ V5完成{retrain_count}次重训, 生成{len(self.full_data)}个交易信号")

    def generate_positions_v5(self):
        """
        V5仓位生成 - 不使用置信度调节（这是V5与final版的区别）
        V5的创新在于动态状态映射，而非置信度调节
        """
        # V5特色：直接使用动态映射的仓位，不进行置信度调节
        self.full_data["Position"] = self.full_data["Position_Raw"]

        # 仓位滞后一天（避免前视偏差）
        self.full_data["Position"] = self.full_data["Position"].shift(1).fillna(0)

    def run_backtest_v5(self):
        """V5回测执行"""
        # 策略收益
        self.full_data["Strategy_Return"] = (
            self.full_data["Position"] * self.full_data["Returns"]
        )

        # 交易成本
        position_changes = self.full_data["Position"].diff().abs()
        costs = position_changes * self.transaction_cost
        self.full_data["Net_Strategy_Return"] = (
            self.full_data["Strategy_Return"] - costs
        )

        return self.full_data


def calculate_metrics_v5(returns, risk_free_rate=0.0):
    """V5适配版指标计算 - 与best_solution_final.py保持一致"""
    if len(returns) < 2:
        return {
            "Sharpe Ratio": 0, "Annual Return": 0, "Volatility": 0,
            "Maximum Drawdown": 0, "Sortino Ratio": 0, "Calmar Ratio": 0,
            "Win Rate": 0, "Information Ratio": 0
        }

    trading_days = 252

    # 基础指标
    mean_daily_return = returns.mean()
    std_daily_return = returns.std()

    sharpe_ratio = (mean_daily_return / std_daily_return) * np.sqrt(trading_days) if std_daily_return != 0 else 0
    annual_return = (1 + mean_daily_return) ** trading_days - 1
    volatility = std_daily_return * np.sqrt(trading_days)

    # 最大回撤
    cumulative_returns = (1 + returns).cumprod()
    peak = cumulative_returns.expanding(min_periods=1).max()
    drawdown = (cumulative_returns - peak) / peak
    max_drawdown = drawdown.min()

    # Sortino比率
    downside_returns = returns[returns < 0]
    downside_std = downside_returns.std()
    sortino_ratio = (mean_daily_return / downside_std) * np.sqrt(trading_days) if downside_std != 0 else 0

    # Calmar比率
    calmar_ratio = annual_return / abs(max_drawdown) if max_drawdown != 0 else 0

    # 胜率
    win_rate = (returns > 0).sum() / len(returns) if len(returns) > 0 else 0

    # 信息比率
    excess_returns = returns
    tracking_error = excess_returns.std()
    information_ratio = (excess_returns.mean() / tracking_error) * np.sqrt(trading_days) if tracking_error != 0 else 0

    return {
        "Sharpe Ratio": sharpe_ratio,
        "Annual Return": annual_return,
        "Volatility": volatility,
        "Maximum Drawdown": max_drawdown,
        "Sortino Ratio": sortino_ratio,
        "Calmar Ratio": calmar_ratio,
        "Win Rate": win_rate,
        "Information Ratio": information_ratio,
    }


def main():
    """主函数 - V5适配版"""
    print("🏆 SPY HMM V5策略 - 适配版本")
    print("基于V5核心算法，使用统一数据和评估框架")
    print("="*60)

    try:
        # 加载数据（与best_solution_final.py相同）
        train_df = pd.read_csv("./hmm_data/SPY/prepared/train.csv")
        test_df = pd.read_csv("./hmm_data/SPY/prepared/test.csv")
        combined_df = pd.concat([train_df, test_df], ignore_index=True)

        # 初始化V5适配策略
        strategy = AdaptedV5HMMStrategy(
            n_states=4,
            retraining_period=63,  # V5特色：季度重训
            initial_train_size=252
        )

        # V5适配特征工程
        print("\n🔧 V5适配特征工程...")
        processed_data = strategy.engineer_v5_features(combined_df)

        # V5核心算法：walk-forward训练预测
        print("\n🧠 V5核心算法训练...")
        strategy.train_and_predict_walk_forward_v5()

        # 生成V5仓位
        print("\n📈 生成V5仓位...")
        strategy.generate_positions_v5()

        # 运行V5回测
        print("\n💰 运行V5回测...")
        results = strategy.run_backtest_v5()

        # 计算测试集表现
        test_start_date = pd.to_datetime(test_df["Date"].min())
        test_results = results[results.index >= test_start_date]

        if len(test_results) == 0:
            print("❌ 没有测试集结果")
            return

        # 计算V5指标
        test_metrics = calculate_metrics_v5(test_results["Net_Strategy_Return"])

        print("\n" + "="*80)
        print("🏆 V5适配版HMM策略表现 (测试集)")
        print("="*80)
        print(f"夏普比率:      {test_metrics['Sharpe Ratio']:.4f}")
        print(f"年化收益:      {test_metrics['Annual Return']:.2%}")
        print(f"年化波动:      {test_metrics['Volatility']:.2%}")
        print(f"最大回撤:      {test_metrics['Maximum Drawdown']:.2%}")
        print(f"Sortino比率:   {test_metrics['Sortino Ratio']:.4f}")
        print(f"Calmar比率:    {test_metrics['Calmar Ratio']:.4f}")
        print(f"信息比率:      {test_metrics['Information Ratio']:.4f}")
        print(f"胜率:          {test_metrics['Win Rate']:.2%}")
        print("="*80)

        # V5核心特色说明
        print(f"\n📊 V5核心特色:")
        print(f"   ✅ 季度重训机制 - 平衡效率与适应性")
        print(f"   ✅ 历史状态表现分析 - 动态评分系统")
        print(f"   ✅ 动态状态映射 - 基于历史表现自适应仓位")
        print(f"   ✅ 严格无前视偏差 - 时间序列完整性")

        # 策略统计
        print(f"\n📈 V5策略统计:")
        if 'Regime' in test_results.columns:
            regime_dist = test_results['Regime'].value_counts().sort_index()
            for regime, count in regime_dist.items():
                pct = count / len(test_results) * 100
                print(f"   {regime}: {count}天 ({pct:.1f}%)")

        avg_position = test_results['Position'].mean()
        print(f"\n💰 平均仓位: {avg_position:.1%}")

        if 'Confidence' in test_results.columns:
            avg_confidence = test_results['Confidence'].mean()
            print(f"📊 平均置信度: {avg_confidence:.3f}")

        # 保存V5结果
        os.makedirs("./submission", exist_ok=True)

        submission_df = test_results.copy().reset_index()
        submission_df = submission_df[["Date", "Position", "Predicted_State"]]
        submission_df["Date"] = submission_df["Date"].dt.strftime("%Y-%m-%d")

        # 添加Confidence列（如果存在）
        if 'Confidence' in test_results.columns:
            submission_df["Confidence"] = test_results["Confidence"].values
        else:
            submission_df["Confidence"] = 0.5  # 默认置信度

        # 创建Signal列
        submission_df["Position_prev"] = submission_df["Position"].shift(1).fillna(0)
        submission_df["Signal"] = 0
        submission_df.loc[submission_df["Position"] > submission_df["Position_prev"], "Signal"] = 1
        submission_df.loc[submission_df["Position"] < submission_df["Position_prev"], "Signal"] = -1

        final_submission = submission_df[["Date", "Signal", "Confidence", "Predicted_State"]]
        final_submission["Predicted_State"] = final_submission["Predicted_State"].astype(int)

        final_submission.to_csv("./submission/submission_v5_adapted.csv", index=False)

        print(f"\n✅ V5适配版提交文件已创建: ./submission/submission_v5_adapted.csv")
        print(f"📊 提交文件形状: {final_submission.shape}")
        print("前5行预览:")
        print(final_submission.head())

        print(f"\n🎯 V5适配版总结:")
        print(f"   V5的核心创新是动态状态映射和季度重训机制")
        print(f"   通过历史状态表现分析，实现自适应仓位分配")
        print(f"   相比固定映射，V5能更好地适应市场环境变化")

        # 返回指标用于对比
        return test_metrics

    except Exception as e:
        print(f"❌ V5适配版策略失败: {e}")
        import traceback
        traceback.print_exc()
        return None


if __name__ == "__main__":
    main()
