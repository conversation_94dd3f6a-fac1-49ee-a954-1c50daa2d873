#!/usr/bin/env python3
"""
策略对比脚本
============

公平对比V5适配版和最终优化版策略：
1. 使用相同的数据源
2. 使用相同的评估指标
3. 使用相同的测试集
4. 客观分析两种策略的优劣

作者：Leon
日期：2025年9月2日
"""

import pandas as pd
import numpy as np
import sys
import os
import time
from datetime import datetime

# 导入两个策略
try:
    from spy_hmm_v5_adapted import AdaptedV5HMMStrategy, calculate_metrics_v5
    from best_solution_final import FinalOptimizedHMMStrategy, calculate_metrics_final
except ImportError as e:
    print(f"❌ 导入策略失败: {e}")
    print("请确保spy_hmm_v5_adapted.py和best_solution_final.py在当前目录")
    sys.exit(1)


def run_strategy_comparison():
    """运行策略对比"""
    print("🏆 SPY HMM策略公平对比")
    print("V5适配版 vs 最终优化版")
    print("="*80)
    print("对比条件:")
    print("  ✅ 相同数据源: ./hmm_data/SPY/prepared/")
    print("  ✅ 相同评估指标: 夏普比率、年化收益、最大回撤等")
    print("  ✅ 相同测试集: 样本外数据")
    print("  ✅ 相同交易成本: 0.1%")
    print("="*80)

    try:
        # 加载数据
        print("\n📊 加载数据...")
        train_df = pd.read_csv("./hmm_data/SPY/prepared/train.csv")
        test_df = pd.read_csv("./hmm_data/SPY/prepared/test.csv")
        combined_df = pd.concat([train_df, test_df], ignore_index=True)
        
        print(f"   训练集: {len(train_df)}条记录")
        print(f"   测试集: {len(test_df)}条记录")
        print(f"   总计: {len(combined_df)}条记录")

        # 测试集时间范围
        test_start_date = pd.to_datetime(test_df["Date"].min())
        test_end_date = pd.to_datetime(test_df["Date"].max())
        print(f"   测试期间: {test_start_date.date()} 到 {test_end_date.date()}")

        results = {}

        # ==================== 运行V5适配版 ====================
        print(f"\n{'='*50}")
        print("🔵 运行V5适配版策略")
        print(f"{'='*50}")
        
        start_time = time.time()
        
        try:
            # 初始化V5策略
            v5_strategy = AdaptedV5HMMStrategy(
                n_states=4,
                retraining_period=63,  # V5特色：季度重训
                initial_train_size=252,
                transaction_cost=0.001
            )

            # V5特征工程
            print("🔧 V5特征工程...")
            v5_data = v5_strategy.engineer_v5_features(combined_df.copy())

            # V5训练预测
            print("🧠 V5训练预测...")
            v5_strategy.train_and_predict_walk_forward_v5()

            # V5仓位生成
            print("📈 V5仓位生成...")
            v5_strategy.generate_positions_v5()

            # V5回测
            print("💰 V5回测...")
            v5_results = v5_strategy.run_backtest_v5()

            # V5测试集结果
            v5_test_results = v5_results[v5_results.index >= test_start_date]
            
            if len(v5_test_results) == 0:
                print("❌ V5没有测试集结果")
                v5_metrics = None
            else:
                v5_metrics = calculate_metrics_v5(v5_test_results["Net_Strategy_Return"])
                print(f"✅ V5完成，测试集{len(v5_test_results)}天")
            
            v5_time = time.time() - start_time
            results['V5'] = {
                'metrics': v5_metrics,
                'test_results': v5_test_results if len(v5_test_results) > 0 else None,
                'time': v5_time,
                'success': v5_metrics is not None
            }

        except Exception as e:
            print(f"❌ V5策略失败: {e}")
            results['V5'] = {'success': False, 'error': str(e)}

        # ==================== 运行最终优化版 ====================
        print(f"\n{'='*50}")
        print("🟢 运行最终优化版策略")
        print(f"{'='*50}")
        
        start_time = time.time()
        
        try:
            # 初始化最终版策略
            final_strategy = FinalOptimizedHMMStrategy(
                n_states=4,
                retraining_period=63,
                initial_train_size=252,
                transaction_cost=0.001
            )

            # 最终版特征工程
            print("🔧 最终版特征工程...")
            final_data = final_strategy.engineer_optimized_features(combined_df.copy())

            # 最终版训练预测
            print("🧠 最终版训练预测...")
            final_strategy.train_and_predict_walk_forward_final()

            # 最终版仓位生成
            print("📈 最终版仓位生成...")
            final_strategy.generate_positions_final()

            # 最终版回测
            print("💰 最终版回测...")
            final_results = final_strategy.run_backtest_final()

            # 最终版测试集结果
            final_test_results = final_results[final_results.index >= test_start_date]
            
            if len(final_test_results) == 0:
                print("❌ 最终版没有测试集结果")
                final_metrics = None
            else:
                final_metrics = calculate_metrics_final(final_test_results["Net_Strategy_Return"])
                print(f"✅ 最终版完成，测试集{len(final_test_results)}天")
            
            final_time = time.time() - start_time
            results['Final'] = {
                'metrics': final_metrics,
                'test_results': final_test_results if len(final_test_results) > 0 else None,
                'time': final_time,
                'success': final_metrics is not None
            }

        except Exception as e:
            print(f"❌ 最终版策略失败: {e}")
            results['Final'] = {'success': False, 'error': str(e)}

        # ==================== 对比分析 ====================
        print(f"\n{'='*80}")
        print("📊 策略对比分析")
        print(f"{'='*80}")

        if results['V5']['success'] and results['Final']['success']:
            v5_metrics = results['V5']['metrics']
            final_metrics = results['Final']['metrics']

            # 性能对比表
            print(f"{'指标':<20} | {'V5适配版':<15} | {'最终优化版':<15} | {'差异':<15} | {'优势':<10}")
            print("-" * 85)
            
            metrics_comparison = [
                ('夏普比率', 'Sharpe Ratio', '.4f'),
                ('年化收益', 'Annual Return', '.2%'),
                ('年化波动', 'Volatility', '.2%'),
                ('最大回撤', 'Maximum Drawdown', '.2%'),
                ('Sortino比率', 'Sortino Ratio', '.4f'),
                ('Calmar比率', 'Calmar Ratio', '.4f'),
                ('信息比率', 'Information Ratio', '.4f'),
                ('胜率', 'Win Rate', '.2%')
            ]

            winner_count = {'V5': 0, 'Final': 0}
            
            for metric_name, metric_key, fmt in metrics_comparison:
                v5_val = v5_metrics[metric_key]
                final_val = final_metrics[metric_key]
                diff = final_val - v5_val
                
                # 判断优势（回撤是负数，越小越好）
                if metric_key == 'Maximum Drawdown':
                    winner = 'V5' if v5_val > final_val else 'Final'  # 回撤越小越好
                else:
                    winner = 'V5' if v5_val > final_val else 'Final'  # 其他指标越大越好
                
                winner_count[winner] += 1
                
                if fmt == '.4f':
                    print(f"{metric_name:<20} | {v5_val:>15.4f} | {final_val:>15.4f} | {diff:>+15.4f} | {winner:<10}")
                elif fmt == '.2%':
                    print(f"{metric_name:<20} | {v5_val:>15.2%} | {final_val:>15.2%} | {diff:>+15.2%} | {winner:<10}")
                else:
                    print(f"{metric_name:<20} | {v5_val:>15} | {final_val:>15} | {diff:>+15} | {winner:<10}")

            print("-" * 85)
            print(f"{'总体优势统计':<20} | {'V5: ' + str(winner_count['V5']) + '项':<15} | {'Final: ' + str(winner_count['Final']) + '项':<15} | {'':<15} | {max(winner_count, key=winner_count.get):<10}")

            # 详细分析
            print(f"\n🔍 详细分析:")
            
            # 风险调整收益分析
            sharpe_diff = final_metrics['Sharpe Ratio'] - v5_metrics['Sharpe Ratio']
            if abs(sharpe_diff) > 0.1:
                better_strategy = "最终优化版" if sharpe_diff > 0 else "V5适配版"
                print(f"   📈 风险调整收益: {better_strategy}显著优势 (夏普比率差异{sharpe_diff:+.4f})")
            else:
                print(f"   📈 风险调整收益: 两策略表现接近 (夏普比率差异{sharpe_diff:+.4f})")

            # 回撤控制分析
            dd_diff = final_metrics['Maximum Drawdown'] - v5_metrics['Maximum Drawdown']
            if abs(dd_diff) > 0.02:  # 2%差异
                better_strategy = "最终优化版" if dd_diff > 0 else "V5适配版"  # 回撤越小越好
                print(f"   🛡️  风险控制: {better_strategy}显著优势 (回撤差异{dd_diff:+.2%})")
            else:
                print(f"   🛡️  风险控制: 两策略表现接近 (回撤差异{dd_diff:+.2%})")

            # 执行效率分析
            v5_time = results['V5']['time']
            final_time = results['Final']['time']
            print(f"   ⏱️  执行效率: V5用时{v5_time:.1f}秒, 最终版用时{final_time:.1f}秒")

            # 策略特色分析
            print(f"\n🎯 策略特色对比:")
            print(f"   V5适配版特色:")
            print(f"     • 季度重训机制 - 平衡效率与适应性")
            print(f"     • 动态状态映射 - 基于历史表现自适应")
            print(f"     • 11个技术特征 - 全面市场信息")
            print(f"   最终优化版特色:")
            print(f"     • 精选6个特征 - 系统性迭代优化")
            print(f"     • 置信度调节 - 创新仓位管理")
            print(f"     • 支撑阻力特征 - 技术分析增强")

            # 最终结论
            print(f"\n🏆 最终结论:")
            overall_winner = max(winner_count, key=winner_count.get)
            if overall_winner == 'Final':
                print(f"   最终优化版在{winner_count['Final']}项指标中胜出，整体表现更优")
                if final_metrics['Sharpe Ratio'] > v5_metrics['Sharpe Ratio']:
                    print(f"   特别是在风险调整收益方面具有明显优势")
            else:
                print(f"   V5适配版在{winner_count['V5']}项指标中胜出，整体表现更优")
                if v5_metrics['Sharpe Ratio'] > final_metrics['Sharpe Ratio']:
                    print(f"   特别是在风险调整收益方面具有明显优势")

        else:
            print("❌ 无法进行对比，部分策略执行失败")
            if not results['V5']['success']:
                print(f"   V5失败原因: {results['V5'].get('error', '未知')}")
            if not results['Final']['success']:
                print(f"   最终版失败原因: {results['Final'].get('error', '未知')}")

        # 保存对比报告
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        report_filename = f"strategy_comparison_report_{timestamp}.txt"
        
        with open(report_filename, 'w', encoding='utf-8') as f:
            f.write("SPY HMM策略对比报告\n")
            f.write("="*50 + "\n")
            f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            
            if results['V5']['success'] and results['Final']['success']:
                f.write("性能对比:\n")
                for metric_name, metric_key, fmt in metrics_comparison:
                    v5_val = results['V5']['metrics'][metric_key]
                    final_val = results['Final']['metrics'][metric_key]
                    f.write(f"{metric_name}: V5={v5_val:{fmt}}, Final={final_val:{fmt}}\n")
            
            f.write(f"\n执行状态:\n")
            f.write(f"V5适配版: {'成功' if results['V5']['success'] else '失败'}\n")
            f.write(f"最终优化版: {'成功' if results['Final']['success'] else '失败'}\n")

        print(f"\n📄 对比报告已保存: {report_filename}")
        print(f"✅ 策略对比完成!")

        return results

    except Exception as e:
        print(f"❌ 对比过程失败: {e}")
        import traceback
        traceback.print_exc()
        return None


if __name__ == "__main__":
    run_strategy_comparison()
