#!/usr/bin/env python3
"""
SPY股票数据获取脚本 - 支持多时间周期技术指标
"""

import yfinance as yf
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import os
import warnings
warnings.filterwarnings('ignore')

def calculate_technical_indicators(df):
    """计算完整的技术指标套件"""
    
    # 基础价格指标
    df['Returns'] = df['Close'].pct_change()
    df['Log_Returns'] = np.log(df['Close'] / df['Close'].shift(1))
    
    # Simple Moving Averages
    df['SMA_5'] = df['Close'].rolling(window=5).mean()
    df['SMA_10'] = df['Close'].rolling(window=10).mean()
    df['SMA_20'] = df['Close'].rolling(window=20).mean()
    df['SMA_50'] = df['Close'].rolling(window=50).mean()
    df['SMA_100'] = df['Close'].rolling(window=100).mean()
    df['SMA_200'] = df['Close'].rolling(window=200).mean()
    
    # Exponential Moving Averages
    df['EMA_12'] = df['Close'].ewm(span=12).mean()
    df['EMA_26'] = df['Close'].ewm(span=26).mean()
    df['EMA_50'] = df['Close'].ewm(span=50).mean()
    
    # RSI
    delta = df['Close'].diff()
    gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
    rs = gain / loss
    df['RSI_14'] = 100 - (100 / (1 + rs))
    
    # MACD
    df['MACD'] = df['EMA_12'] - df['EMA_26']
    df['MACD_Signal'] = df['MACD'].ewm(span=9).mean()
    df['MACD_Histogram'] = df['MACD'] - df['MACD_Signal']
    
    # Bollinger Bands
    bb_period = 20
    bb_std = 2
    df['BB_Middle'] = df['Close'].rolling(window=bb_period).mean()
    bb_std_dev = df['Close'].rolling(window=bb_period).std()
    df['BB_Upper'] = df['BB_Middle'] + (bb_std_dev * bb_std)
    df['BB_Lower'] = df['BB_Middle'] - (bb_std_dev * bb_std)
    df['BB_Width'] = (df['BB_Upper'] - df['BB_Lower']) / df['BB_Middle']
    df['BB_Position'] = (df['Close'] - df['BB_Lower']) / (df['BB_Upper'] - df['BB_Lower'])
    
    # Average True Range (ATR)
    df['H-L'] = df['High'] - df['Low']
    df['H-C'] = np.abs(df['High'] - df['Close'].shift(1))
    df['L-C'] = np.abs(df['Low'] - df['Close'].shift(1))
    df['TR'] = df[['H-L', 'H-C', 'L-C']].max(axis=1)
    df['ATR_14'] = df['TR'].rolling(window=14).mean()
    df.drop(['H-L', 'H-C', 'L-C', 'TR'], axis=1, inplace=True)
    
    # Stochastic Oscillator
    low_14 = df['Low'].rolling(window=14).min()
    high_14 = df['High'].rolling(window=14).max()
    df['Stochastic_K'] = 100 * ((df['Close'] - low_14) / (high_14 - low_14))
    df['Stochastic_D'] = df['Stochastic_K'].rolling(window=3).mean()
    
    # Williams %R
    df['Williams_R'] = -100 * ((high_14 - df['Close']) / (high_14 - low_14))
    
    # Rate of Change
    df['ROC_10'] = ((df['Close'] - df['Close'].shift(10)) / df['Close'].shift(10)) * 100
    df['ROC_20'] = ((df['Close'] - df['Close'].shift(20)) / df['Close'].shift(20)) * 100
    
    # Momentum indicators
    df['Momentum_10'] = df['Close'] - df['Close'].shift(10)
    df['Price_to_SMA20'] = df['Close'] / df['SMA_20']
    df['Price_to_SMA50'] = df['Close'] / df['SMA_50']
    
    # Volume indicators
    df['Volume_SMA_20'] = df['Volume'].rolling(window=20).mean()
    df['Volume_Ratio'] = df['Volume'] / df['Volume_SMA_20']
    df['Price_Volume'] = df['Close'] * df['Volume']
    
    # Volatility indicators
    df['Volatility_10'] = df['Returns'].rolling(window=10).std() * np.sqrt(252)
    df['Volatility_20'] = df['Returns'].rolling(window=20).std() * np.sqrt(252)
    
    # Support/Resistance levels
    df['High_20'] = df['High'].rolling(window=20).max()
    df['Low_20'] = df['Low'].rolling(window=20).min()
    df['Support_Distance'] = (df['Close'] - df['Low_20']) / df['Close']
    df['Resistance_Distance'] = (df['High_20'] - df['Close']) / df['Close']
    
    return df

def create_hmm_features(df):
    """创建适合HMM模型的特征"""
    
    # 价格状态特征
    df['Price_State'] = np.where(df['Close'] > df['SMA_20'], 1, 
                        np.where(df['Close'] < df['SMA_20'], -1, 0))
    
    # 趋势强度
    df['Trend_Strength'] = (df['Close'] - df['SMA_50']) / df['ATR_14']
    
    # 市场状态组合特征
    df['Bull_Signal'] = ((df['RSI_14'] > 30) & (df['RSI_14'] < 70) & 
                        (df['MACD'] > df['MACD_Signal']) & 
                        (df['Close'] > df['SMA_20'])).astype(int)
    
    df['Bear_Signal'] = ((df['RSI_14'] > 30) & (df['RSI_14'] < 70) & 
                        (df['MACD'] < df['MACD_Signal']) & 
                        (df['Close'] < df['SMA_20'])).astype(int)
    
    # 波动率状态
    df['Vol_State'] = pd.qcut(df['Volatility_20'].fillna(df['Volatility_20'].median()), 
                             q=3, labels=['Low_Vol', 'Med_Vol', 'High_Vol'])
    df['Vol_State'] = df['Vol_State'].astype(str)
    
    return df

def get_spy_data(start_date='2020-01-01', end_date=None):
    """获取SPY数据并计算技术指标"""
    
    if end_date is None:
        end_date = datetime.now().strftime('%Y-%m-%d')
    
    print(f"📊 获取SPY数据: {start_date} 到 {end_date}")
    
    # 下载数据
    spy = yf.Ticker("SPY")
    df = spy.history(start=start_date, end=end_date)
    
    if df.empty:
        raise ValueError("无法获取SPY数据")
    
    print(f"✅ 获取到 {len(df)} 条数据记录")
    
    # 重置索引，将Date作为列
    df.reset_index(inplace=True)
    df['Date'] = df['Date'].dt.strftime('%Y-%m-%d')
    
    # 计算技术指标
    print("🔧 计算技术指标...")
    df = calculate_technical_indicators(df)
    
    # 创建HMM特征
    print("🧠 创建HMM特征...")
    df = create_hmm_features(df)
    
    # 清理数据
    df = df.dropna()
    
    print(f"✅ 最终数据集大小: {len(df)} 行 x {len(df.columns)} 列")
    print(f"📅 数据范围: {df['Date'].min()} 到 {df['Date'].max()}")
    
    return df

def split_train_test(df, test_ratio=0.2):
    """按时间顺序分割训练和测试集"""
    
    split_idx = int(len(df) * (1 - test_ratio))
    train_df = df.iloc[:split_idx].copy()
    test_df = df.iloc[split_idx:].copy()
    
    print(f"📊 训练集: {len(train_df)} 条 ({train_df['Date'].min()} 到 {train_df['Date'].max()})")
    print(f"📊 测试集: {len(test_df)} 条 ({test_df['Date'].min()} 到 {test_df['Date'].max()})")
    
    return train_df, test_df

def create_submission_template(test_df, output_path):
    """创建提交模板文件"""
    
    submission = pd.DataFrame({
        'Date': test_df['Date'],
        'Signal': 0,  # 初始信号为0 (hold)
        'Confidence': 0.5,  # 初始置信度
        'Predicted_State': 'Unknown'  # 初始状态
    })
    
    submission.to_csv(output_path, index=False)
    print(f"✅ 创建提交模板: {output_path}")
    
    return submission

def main():
    """主函数"""
    
    # 创建输出目录
    output_dir = "./hmm_data/SPY/prepared"
    os.makedirs(output_dir, exist_ok=True)
    
    try:
        # 获取5年的历史数据
        start_date = '2019-01-01'
        end_date = None
        
        print("🚀 开始获取SPY股票数据...")
        df = get_spy_data(start_date, end_date)
        
        # 分割数据
        train_df, test_df = split_train_test(df, test_ratio=0.2)
        
        # 保存数据文件
        train_path = f"{output_dir}/train.csv"
        test_path = f"{output_dir}/test.csv"
        
        train_df.to_csv(train_path, index=False)
        test_df.to_csv(test_path, index=False)
        
        print(f"✅ 保存训练数据: {train_path}")
        print(f"✅ 保存测试数据: {test_path}")
        
        # 创建提交模板
        submission_path = f"{output_dir}/sample_submission.csv"
        create_submission_template(test_df, submission_path)
        
        # 显示数据统计
        print("\n📈 数据统计摘要:")
        print(f"特征数量: {len(df.columns)-1}")  # 除去Date列
        print(f"收益率统计:")
        print(f"  平均年化收益: {train_df['Returns'].mean() * 252:.2%}")
        print(f"  年化波动率: {train_df['Returns'].std() * np.sqrt(252):.2%}")
        print(f"  最大单日涨幅: {train_df['Returns'].max():.2%}")
        print(f"  最大单日跌幅: {train_df['Returns'].min():.2%}")
        
        # 显示特征列表
        feature_cols = [col for col in df.columns if col != 'Date']
        print(f"\n🔧 技术指标列表 ({len(feature_cols)} 个):")
        for i, col in enumerate(feature_cols, 1):
            print(f"  {i:2d}. {col}")
            if i % 5 == 0 and i < len(feature_cols):
                print()
        
        print("\n🎯 数据准备完成！可以开始HMM策略优化...")
        
    except Exception as e:
        print(f"❌ 错误: {e}")
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    if success:
        print("\n✅ SPY数据获取成功！")
    else:
        print("\n❌ 数据获取失败!")
        exit(1)